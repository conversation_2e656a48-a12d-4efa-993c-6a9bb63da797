/******************************************************************************
 * @file rs485_handler_new.c
 * @brief 新的RS485事件处理模块实现 - 使用标准Modbus RTU协议
 * <AUTHOR>
 * @version 2.0
 * @date 2024-11-25
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 本模块处理RS485通信事件并管理Modbus寄存器，使用新的标准Modbus RTU协议实现
 * 
 * @par dependencies
 * FreeRTOS, modbus_rtu_standard
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/
//******************************** Includes *********************************//
#include "rs485_handler_new.h"
//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define MY_MAX_DELAY_NEW    0xFFFFFFFF   /* 最大延时时间              */
#define MY_MAX_PRIORITY_NEW 56           /* 最大优先级               */
#define REG_LEN_NEW         2            /* 32位寄存器分割为16位寄存器时的长度 */

#define CHECK_PARAMETERS_NEW(p_private, p_event) \
    do { \
        if ((NULL == (p_private)) || (NULL == (p_event))) { \
            RS485_HANDLER_LOG_ERROR("p_private or p_event is NULL, file: %s, line: %d", __FILE__, __LINE__); \
            return RS485_HANDLER_NEW_ERROR; \
        } \
    } while (0)

#define CHECK_REG_PLACE_NEW(reg_place) \
    do { \
        if ((reg_place >= REG_QUANTITY) || (reg_place < 0)) { \
            RS485_HANDLER_LOG_ERROR("reg_place error, file: %s, line: %d", __FILE__, __LINE__); \
            return RS485_HANDLER_NEW_ERROR; \
        } \
    } while (0)
//******************************** Defines **********************************//

//******************************** 类型定义 *********************************//
/**
 * @brief 寄存器存储位置与Modbus地址映射结构体
 */
typedef struct
{
    reg_storage_place_t reg_place;            /* 内部寄存器存储位置/索引 */
    uint16_t            modbus_start_reg_adr; /* Modbus起始寄存器地址 */
    uint16_t            modbus_end_reg_adr;   /* Modbus结束寄存器地址(对于多寄存器值) */
} reg_matchup_new_t;

/**
 * @brief RS485事件处理器私有数据结构
 */
typedef struct rs485_event_handler_private_data_new_t
{
    rs485_reg_t*                       p_reg_data;           /* 寄存器数据数组指针 */
    rs485_ope_fun_new_t                *p_ope_fun;           /* 操作函数指针 */
    reg_matchup_new_t                  *p_reg_matchup_table;  /* 寄存器匹配表指针 */
    rs485_event_handler_map_new_t      *p_event_handler_map;  /* RS485事件处理器映射表指针 */
    uint8_t                            event_mapping_count;   /* 事件映射表项数量 */
    uart_dev_interface_new_t           *p_uart_interface;     /* UART接口指针 */
    uint16_t                           *p_registers;          /* Modbus寄存器数组 */
    uint16_t                           register_count;        /* 寄存器数量 */
} rs485_event_handler_private_data_new_t;
//******************************** 类型定义 *********************************//

//******************************** 变量 ************************************//
/* 全局寄存器数据数组 */
static rs485_reg_t g_rs485_reg_new[REG_QUANTITY] = {0};

/* 全局寄存器匹配表 */
static reg_matchup_new_t g_reg_matchup_table_new[REG_QUANTITY] = {0};

/* RS485事件处理器映射表 */
static rs485_event_handler_map_new_t g_rs485_event_handler_map_new[RS485_EVENT_QUANTITY_NEW]; 

/* RS485事件处理器私有数据 */
static rs485_event_handler_private_data_new_t g_rs485_event_data_new = 
{
    .p_reg_data          = g_rs485_reg_new,
    .p_reg_matchup_table = g_reg_matchup_table_new,
    .p_event_handler_map = g_rs485_event_handler_map_new,
    .event_mapping_count = 0,
    .p_uart_interface    = NULL,
    .p_registers         = NULL,
    .register_count      = 0,
};

/* RS485事件处理器实例 */
static rs485_event_handler_new_t g_rs485_event_new =
{
    .inited                    = false,
    .rs485_event_handle_thread = NULL,
    .rs485_modbus_thread       = NULL,
    .rs485_event_handle_queue  = NULL,
    .rs485_event_handle_timer  = NULL,
    .p_os_interfac             = NULL, 
    .p_sys_config              = NULL,
    .private_data              = &g_rs485_event_data_new,
};
//******************************** 变量 ************************************//

//******************************** 前向声明 ********************************//
static rs485_handler_new_ret_t save_reg_value_new(rs485_event_handler_private_data_new_t *p_private,
                                         uint16_t reg_place);

static rs485_handler_new_ret_t process_reg_value_new(rs485_event_handler_private_data_new_t *p_private,
                                           uint16_t reg_place);
static uint16_t rs485_get_reg_place_new(uint16_t reg_adr);
static void rs485_event_handle_thread_new(void *argument);
static void rs485_modbus_thread_new(void *argument);
static rs485_handler_new_ret_t rs485_reg_value_init_new(rs485_event_handler_new_t *phandler);

// Modbus回调函数
static uint16_t modbus_read_register_callback_new(uint16_t address);
static modbus_ret_t modbus_write_register_callback_new(uint16_t address, uint16_t value);
static modbus_reg_type_t modbus_get_reg_type_callback_new(uint16_t address);

// UART接口适配函数
static int uart_init_adapter_new(uart_interface_t *puart_dev);
static int uart_send_adapter_new(uart_interface_t* puart_dev, uint8_t *datas, int len, uint32_t timeout_ms);
static int uart_receive_adapter_new(uart_interface_t* puart_dev, uint8_t *data, int timeout_ms);
static void uart_set_mode_adapter_new(uart_mode_t mode);
//******************************** 前向声明 ********************************//

//******************************** 函数 ************************************//

/******************************************************************************
 * @brief 获取当前系统时间戳
 * 
 * @return uint32_t 当前时间戳
 *****************************************************************************/
uint32_t rs485_get_timestamp_new(void)
{
    if (g_rs485_event_new.p_os_interfac && g_rs485_event_new.p_os_interfac->rtos_get_tick_count)
    {
        return g_rs485_event_new.p_os_interfac->rtos_get_tick_count();
    }
    return 0;
}

/******************************************************************************
 * @brief UART初始化适配器
 * 
 * @param puart_dev UART接口指针
 * 
 * @return int 状态码
 *****************************************************************************/
static int uart_init_adapter_new(uart_interface_t *puart_dev)
{
    if (g_rs485_event_data_new.p_uart_interface && 
        g_rs485_event_data_new.p_uart_interface->uart_init)
    {
        return g_rs485_event_data_new.p_uart_interface->uart_init(
            (uart_dev_interface_new_t*)g_rs485_event_data_new.p_uart_interface);
    }
    return -1;
}

/******************************************************************************
 * @brief UART发送适配器
 * 
 * @param puart_dev UART接口指针
 * @param datas 数据指针
 * @param len 数据长度
 * @param timeout_ms 超时时间
 * 
 * @return int 状态码
 *****************************************************************************/
static int uart_send_adapter_new(uart_interface_t* puart_dev, uint8_t *datas, int len, uint32_t timeout_ms)
{
    if (g_rs485_event_data_new.p_uart_interface && 
        g_rs485_event_data_new.p_uart_interface->uart_send)
    {
        return g_rs485_event_data_new.p_uart_interface->uart_send(
            (uart_dev_interface_new_t*)g_rs485_event_data_new.p_uart_interface, 
            datas, len, timeout_ms);
    }
    return -1;
}

/******************************************************************************
 * @brief UART接收适配器
 * 
 * @param puart_dev UART接口指针
 * @param data 数据指针
 * @param timeout_ms 超时时间
 * 
 * @return int 状态码
 *****************************************************************************/
static int uart_receive_adapter_new(uart_interface_t* puart_dev, uint8_t *data, int timeout_ms)
{
    if (g_rs485_event_data_new.p_uart_interface && 
        g_rs485_event_data_new.p_uart_interface->uart_receive)
    {
        return g_rs485_event_data_new.p_uart_interface->uart_receive(
            (uart_dev_interface_new_t*)g_rs485_event_data_new.p_uart_interface, 
            data, timeout_ms);
    }
    return -1;
}

/******************************************************************************
 * @brief UART模式设置适配器
 * 
 * @param mode UART模式
 *****************************************************************************/
static void uart_set_mode_adapter_new(uart_mode_t mode)
{
    if (g_rs485_event_data_new.p_uart_interface && 
        g_rs485_event_data_new.p_uart_interface->uart_set_mode)
    {
        g_rs485_event_data_new.p_uart_interface->uart_set_mode(
            (uart_dev_interface_new_t*)g_rs485_event_data_new.p_uart_interface, 
            (int)mode);
    }
}

/******************************************************************************
 * @brief Modbus读寄存器回调函数
 * 
 * @param address 寄存器地址
 * 
 * @return uint16_t 寄存器值
 *****************************************************************************/
static uint16_t modbus_read_register_callback_new(uint16_t address)
{
    uint16_t reg_place = rs485_get_reg_place_new(address);
    
    if (reg_place >= REG_QUANTITY)
    {
        RS485_HANDLER_LOG_ERROR("Invalid register address: %d", address);
        return 0;
    }
    
    rs485_reg_t *p_reg = &g_rs485_event_data_new.p_reg_data[reg_place];
    
    /* 32位或浮点型寄存器将被分割成两个16位寄存器 */
    if (REG_LEN_NEW == p_reg->reg_stru.reg_len)
    {
        /* 检查是否读取分割的第一个寄存器 */
        if (g_rs485_event_data_new.p_reg_matchup_table[reg_place].modbus_start_reg_adr == address)
        {
            return (uint16_t)(p_reg->reg_data.u32_data >> 16);
        }
        /* 检查是否读取分割的第二个寄存器 */
        else if (g_rs485_event_data_new.p_reg_matchup_table[reg_place].modbus_end_reg_adr == address)
        {
            return (uint16_t)(p_reg->reg_data.u32_data & 0x0000FFFF);
        }
    }
    /* 8位或16位寄存器，直接读取 */
    else
    {
        return p_reg->reg_data.u16_data;
    }
    
    return 0;
}

/******************************************************************************
 * @brief Modbus写寄存器回调函数
 * 
 * @param address 寄存器地址
 * @param value 寄存器值
 * 
 * @return modbus_ret_t 状态码
 *****************************************************************************/
static modbus_ret_t modbus_write_register_callback_new(uint16_t address, uint16_t value)
{
    uint16_t reg_place = rs485_get_reg_place_new(address);
    
    if (reg_place >= REG_QUANTITY)
    {
        RS485_HANDLER_LOG_ERROR("Invalid register address: %d", address);
        return MODBUS_ERROR_INVALID_ADDRESS;
    }
    
    rs485_reg_t *p_reg = &g_rs485_event_data_new.p_reg_data[reg_place];
    
    /* 检查寄存器是否可写 */
    if ((READABLE_WRITEABLE != p_reg->property) && 
        (WRITEABLE_NO_SAVE != p_reg->property))
    {
        RS485_HANDLER_LOG_ERROR("Register %d is not writable", address);
        return MODBUS_ERROR_INVALID_ADDRESS;
    }
    
    /* 8位或16位寄存器，写入一次 */
    if ((RGE_8BIT == p_reg->data_bit) || (RGE_16BIT == p_reg->data_bit))
    {
        p_reg->reg_data.u16_data = value;
        return process_reg_value_new(&g_rs485_event_data_new, reg_place) == RS485_HANDLER_NEW_OK ? 
               MODBUS_OK : MODBUS_ERROR_DEVICE_FAILURE;
    }
    /* 32位或浮点型寄存器，需要写入两次 */
    else if ((RGE_32BIT == p_reg->data_bit) || (REG_FLOAT == p_reg->data_bit))
    {
        if (g_rs485_event_data_new.p_reg_matchup_table[reg_place].modbus_start_reg_adr == address)
        {
            p_reg->reg_data.u32_data = (uint32_t)(value << 16);
            return MODBUS_OK;
        }
        else if (g_rs485_event_data_new.p_reg_matchup_table[reg_place].modbus_end_reg_adr == address)
        {
            p_reg->reg_data.u32_data |= value;
            return process_reg_value_new(&g_rs485_event_data_new, reg_place) == RS485_HANDLER_NEW_OK ? 
                   MODBUS_OK : MODBUS_ERROR_DEVICE_FAILURE;
        }
    }
    
    return MODBUS_ERROR_INVALID_ADDRESS;
}

/******************************************************************************
 * @brief Modbus获取寄存器类型回调函数
 * 
 * @param address 寄存器地址
 * 
 * @return modbus_reg_type_t 寄存器类型
 *****************************************************************************/
static modbus_reg_type_t modbus_get_reg_type_callback_new(uint16_t address)
{
    uint16_t reg_place = rs485_get_reg_place_new(address);
    
    if (reg_place >= REG_QUANTITY)
    {
        return MODBUS_REG_ONLY_READ;  // 默认只读
    }
    
    rs485_reg_t *p_reg = &g_rs485_event_data_new.p_reg_data[reg_place];
    
    if ((READABLE_WRITEABLE == p_reg->property) || 
        (WRITEABLE_NO_SAVE == p_reg->property))
    {
        return MODBUS_REG_READ_WRITE;
    }
    
    return MODBUS_REG_ONLY_READ;
}
/******************************************************************************
 * @brief 处理寄存器值(特定寄存器的特殊处理)
 *
 * @param p_private 私有数据指针
 * @param reg_place 寄存器存储位置/索引
 *
 * @return rs485_handler_new_ret_t 状态码
 *****************************************************************************/
static rs485_handler_new_ret_t process_reg_value_new(
                                rs485_event_handler_private_data_new_t *p_private,
                                                            uint16_t reg_place)
{
    /* 0. 检查参数是否有效 */
    if(NULL == p_private)
    {
        RS485_HANDLER_LOG_ERROR("p_private is NULL, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_NEW_ERROR;
    }

    /* 1. 根据寄存器位置处理寄存器数据 */
    /* 可读可写寄存器，保存到flash */
    if(READABLE_WRITEABLE == p_private->p_reg_data[reg_place].property)
    {
        if(DEV_ADR_REG == reg_place)
        {
            // 设备地址寄存器特殊处理
            // 这里可以添加设备地址更新逻辑
        }
        return p_private->p_ope_fun->rs485_save_reg_value(reg_place,
                                    &p_private->p_reg_data[reg_place].reg_data);
    }
    /* 可写寄存器，不保存到flash */
    else if(WRITEABLE_NO_SAVE == p_private->p_reg_data[reg_place].property)
    {
        // 不需要保存到flash的寄存器处理
    }
    else
    {
        RS485_HANDLER_LOG_ERROR("reg_place error, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_NEW_ERROR;
    }

    return RS485_HANDLER_NEW_OK;
}

//******************************** 事件处理函数 ********************************//
/******************************************************************************
 * @brief 处理采集数据输入事件
 *
 * @param phandler 处理器指针
 * @param p_event 事件指针
 *
 * @return rs485_handler_new_ret_t 状态码
 *****************************************************************************/
static rs485_handler_new_ret_t rs485_handle_collect_data_input_new(void *phandler,
                                                    rs485_event_new_t *p_event)
{
    rs485_event_handler_private_data_new_t *p_private = phandler;

    /* 检查参数 */
    CHECK_PARAMETERS_NEW(p_private, p_event);
    CHECK_REG_PLACE_NEW(p_event->reg_num);

    /* 更新寄存器数据 */
    memcpy(&p_private->p_reg_data[p_event->reg_num].reg_data,
           &p_event->reg_data,
           sizeof(rs485_reg_data_t));

    /* 同时更新Modbus寄存器数组 */
    if (p_private->p_registers && p_event->reg_num < p_private->register_count)
    {
        p_private->p_registers[p_event->reg_num] = p_event->reg_data.u16_data;
    }

    return RS485_HANDLER_NEW_OK;
}

/******************************************************************************
 * @brief 处理读取寄存器数据事件
 *
 * @param phandler 处理器指针
 * @param p_event 事件指针
 *
 * @return rs485_handler_new_ret_t 状态码
 *****************************************************************************/
static rs485_handler_new_ret_t rs485_handle_read_reg_data_new( void *phandler,
                                                rs485_event_new_t *p_event)
{
    rs485_event_handler_private_data_new_t *p_private = phandler;
    uint16_t reg_place = rs485_get_reg_place_new(p_event->reg_num);

    /* 检查参数 */
    CHECK_PARAMETERS_NEW(p_private, p_event);

    if (reg_place >= REG_QUANTITY)
    {
        RS485_HANDLER_LOG_ERROR("Invalid reg_place: %d", reg_place);
        return RS485_HANDLER_NEW_ERROR;
    }

    /* 32位或浮点型寄存器将被分割成两个16位寄存器 */
    if(REG_LEN_NEW == p_private->p_reg_data[reg_place].reg_stru.reg_len)
    {
        /* 检查是否读取分割的第一个寄存器 */
        if(p_private->p_reg_matchup_table[reg_place].modbus_start_reg_adr == p_event->reg_num)
        {
            p_event->read_reg_data_callback(
                p_private->p_reg_data[reg_place].reg_data.u32_data >> 16,
                p_event->read_data);
        }
        /* 检查是否读取分割的第二个寄存器 */
        else if(p_private->p_reg_matchup_table[reg_place].modbus_end_reg_adr == p_event->reg_num)
        {
            p_event->read_reg_data_callback(
                p_private->p_reg_data[reg_place].reg_data.u32_data & 0x0000FFFF,
                p_event->read_data);
        }
        else
        {
            RS485_HANDLER_LOG_ERROR("reg num error: [%d], file: %s, line: %d",
                  p_event->reg_num, __FILE__, __LINE__);
            return RS485_HANDLER_NEW_ERROR;
        }
    }
    /* 8位或16位寄存器，直接读取 */
    else
    {
        p_event->read_reg_data_callback(
            p_private->p_reg_data[reg_place].reg_data.u16_data,
            p_event->read_data);
    }

    return RS485_HANDLER_NEW_OK;
}

/******************************************************************************
 * @brief 处理写入寄存器数据事件
 *
 * @param phandler 处理器指针
 * @param p_event 事件指针
 *
 * @return rs485_handler_new_ret_t 状态码
 *****************************************************************************/
static rs485_handler_new_ret_t rs485_handle_write_reg_data_new( void *phandler,
                                                 rs485_event_new_t *p_event)
{
    rs485_event_handler_private_data_new_t *p_private = phandler;
    uint16_t reg_place = rs485_get_reg_place_new(p_event->reg_num);

    /* 检查参数 */
    CHECK_PARAMETERS_NEW(p_private, p_event);

    if (reg_place >= REG_QUANTITY)
    {
        RS485_HANDLER_LOG_ERROR("Invalid reg_place: %d", reg_place);
        return RS485_HANDLER_NEW_ERROR;
    }

    if((READABLE_WRITEABLE == p_private->p_reg_data[reg_place].property) ||
       (WRITEABLE_NO_SAVE == p_private->p_reg_data[reg_place].property))
    {
        /* 8位或16位寄存器，写入一次 */
        if((RGE_8BIT == p_private->p_reg_data[reg_place].data_bit) ||
           (RGE_16BIT == p_private->p_reg_data[reg_place].data_bit))
        {
            p_private->p_reg_data[reg_place].reg_data = p_event->reg_data;
            return process_reg_value_new(p_private, reg_place);
        }
        /* 32位或浮点型寄存器，需要写入两次 */
        else if((RGE_32BIT == p_private->p_reg_data[reg_place].data_bit) ||
                (REG_FLOAT == p_private->p_reg_data[reg_place].data_bit))
        {
            if(p_private->p_reg_matchup_table[reg_place].modbus_start_reg_adr == p_event->reg_num)
            {
                p_private->p_reg_data[reg_place].reg_data.u32_data =
                                        (p_event->reg_data.u16_data << 16);
                return RS485_HANDLER_NEW_OK;
            }
            else if(p_private->p_reg_matchup_table[reg_place].modbus_end_reg_adr == p_event->reg_num)
            {
                p_private->p_reg_data[reg_place].reg_data.u32_data |=
                                        p_event->reg_data.u16_data;
                return process_reg_value_new(p_private, reg_place);
            }
        }
    }
    else
    {
        RS485_HANDLER_LOG_ERROR("reg %d property error, can't write, file: %s, line: %d",
              reg_place, __FILE__, __LINE__);
    }

    return RS485_HANDLER_NEW_ERROR;
}
//******************************** 事件处理函数 ********************************//

/******************************************************************************
 * @brief 处理Modbus更新事件
 *
 * @param phandler 处理器指针
 * @param p_event 事件指针
 *
 * @return rs485_handler_new_ret_t 状态码
 *****************************************************************************/
static rs485_handler_new_ret_t rs485_handle_modbus_update_new(void *phandler,
                                                rs485_event_new_t *p_event)
{
    rs485_event_handler_new_t *p_handler = (rs485_event_handler_new_t *)phandler;

    if (NULL == p_handler)
    {
        RS485_HANDLER_LOG_ERROR("p_handler is NULL");
        return RS485_HANDLER_NEW_ERROR;
    }

    /* 调用Modbus更新函数 */
    Modbus_Update(&p_handler->modbus_device, p_event->timestamp);

    return RS485_HANDLER_NEW_OK;
}

/******************************************************************************
 * @brief 处理RS485事件
 *
 * @param phandle 处理器句柄
 * @param pevent 事件指针
 *
 * @return rs485_handler_new_ret_t 状态码
 *****************************************************************************/
static rs485_handler_new_ret_t process_rs485_event_new(rs485_event_handler_new_t *phandle, rs485_event_new_t *pevent)
 {
    RS485_HANDLER_LOG_DEBUG("process_rs485_event_new start");

    if(NULL == pevent)
    {
        RS485_HANDLER_LOG_ERROR("event is NULL");
        return RS485_HANDLER_NEW_ERRORRESOURCE;
    }

    rs485_event_handler_private_data_new_t *p_private_data = phandle->private_data;

    /* 查找注册的处理函数 */
    for(uint8_t i = 0; i < p_private_data->event_mapping_count; i++)
    {
        if(p_private_data->p_event_handler_map[i].event_type == pevent->event_type)
        {
            /* 检查函数是否注册 */
            if(NULL != p_private_data->p_event_handler_map[i].handler)
            {
                /* 执行函数 */
                if (pevent->event_type == MODBUS_UPDATE_EVENT_NEW)
                {
                    return p_private_data->p_event_handler_map[i].handler(phandle, pevent);
                }
                else
                {
                    return p_private_data->p_event_handler_map[i].handler(p_private_data, pevent);
                }
            }
            else
            {
                RS485_HANDLER_LOG_ERROR("process_rs485_event_new handler not register,event_type:%d file:%s,line:%d",
                      pevent->event_type, __FILE__, __LINE__);
                return RS485_HANDLER_NEW_ERRORRESOURCE;
            }
        }
    }

    RS485_HANDLER_LOG_ERROR("process_rs485_event_new not find handler,event_type:%d", pevent->event_type);
    return RS485_HANDLER_NEW_ERRORRESOURCE;
}

/******************************************************************************
 * @brief RS485事件处理线程
 *
 * @param argument 线程参数(指向rs485_event_handler_new_t的指针)
 *****************************************************************************/
static void rs485_event_handle_thread_new(void *argument)
{
    rs485_event_handler_new_t *p_handler = (rs485_event_handler_new_t *)argument;
    rs485_event_handler_private_data_new_t *pdata = p_handler->private_data;
    rs485_event_new_t event;

    if (RS485_HANDLER_NEW_OK != rs485_reg_value_init_new(&g_rs485_event_new))
	{
		RS485_HANDLER_LOG_ERROR("rs485_reg_value_init_new error, file: %s, line: %d", __FILE__, __LINE__);
        return ;
	}

    while(1)
    {
        if (RS485_RTOS_PDTRUE_NEW == g_rs485_event_new.p_os_interfac->rtos_queue_receive(
            p_handler->rs485_event_handle_queue, &event, MY_MAX_DELAY_NEW))
        {
            if(RS485_HANDLER_NEW_OK != process_rs485_event_new(p_handler, &event))
            {
                RS485_HANDLER_LOG_ERROR("process_rs485_event_new error, file: %s, line: %d", __FILE__, __LINE__);
            }
        }
    }
}

/******************************************************************************
 * @brief RS485 Modbus处理线程
 *
 * @param argument 线程参数(指向rs485_event_handler_new_t的指针)
 *****************************************************************************/
static void rs485_modbus_thread_new(void *argument)
{
    rs485_event_handler_new_t *p_handler = (rs485_event_handler_new_t *)argument;
    rs485_event_new_t modbus_event;

    /* 初始化Modbus更新事件 */
    modbus_event.event_type = MODBUS_UPDATE_EVENT_NEW;
    modbus_event.reg_num = 0;
    modbus_event.read_reg_data_callback = NULL;
    modbus_event.read_data = NULL;

    while(1)
    {
        /* 获取当前时间戳 */
        modbus_event.timestamp = rs485_get_timestamp_new();

        /* 发送Modbus更新事件 */
        if (RS485_RTOS_PDTRUE_NEW != g_rs485_event_new.p_os_interfac->rtos_queue_send(
            p_handler->rs485_event_handle_queue, &modbus_event, 0))
        {
            RS485_HANDLER_LOG_ERROR("Failed to send modbus update event");
        }

        /* 延时等待下一次更新 */
        // 这里应该使用RTOS的延时函数，暂时使用简单的循环
        for(volatile uint32_t i = 0; i < 100000; i++);
    }
}

/******************************************************************************
 * @brief 从flash存储器初始化寄存器值
 *
 * @return rs485_handler_new_ret_t 状态码
 *****************************************************************************/
static rs485_handler_new_ret_t rs485_reg_value_init_new(rs485_event_handler_new_t *phandler)
{
    if(NULL == phandler)
    {
        RS485_HANDLER_LOG_ERROR("phandler is NULL, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_NEW_ERRORPARAMETER;
    }

    rs485_event_handler_private_data_new_t *p_private = phandler->private_data;
    rs485_reg_t reg_value;

    if(RS485_HANDLER_NEW_OK != p_private->p_ope_fun->rs485_read_reg_value(DEV_ADR_REG, &reg_value.reg_data))
    {
        return RS485_HANDLER_NEW_ERROR;
    }

    g_rs485_event_data_new.p_reg_data[DEV_ADR_REG].reg_data.u8_data = reg_value.reg_data.u8_data;

    return RS485_HANDLER_NEW_OK;
}

/******************************************************************************
 * @brief 从Modbus寄存器地址获取寄存器存储位置
 *
 * @param reg_adr Modbus寄存器地址
 *
 * @return uint16_t 寄存器存储位置，如果未找到则返回INVALID_REG_ADRESS
 *****************************************************************************/
static uint16_t rs485_get_reg_place_new(uint16_t reg_adr)
{
    if(reg_adr >= INVALID_REG_ADRESS)
    {
        RS485_HANDLER_LOG_ERROR("reg_adr error, file: %s, line: %d", __FILE__, __LINE__);
        return INVALID_REG_ADRESS;
    }

    for(uint16_t i = 0; i < REG_QUANTITY; i++)
    {
        if((reg_adr >= g_reg_matchup_table_new[i].modbus_start_reg_adr) &&
           (reg_adr <= g_reg_matchup_table_new[i].modbus_end_reg_adr))
        {
            return g_reg_matchup_table_new[i].reg_place;
        }
    }

    return INVALID_REG_ADRESS;
}

/******************************************************************************
 * @brief 初始化RS485事件处理器
 *
 * @param pinput 输入参数结构体，包含必要的系统配置和接口
 *
 * @return rs485_handler_new_ret_t 状态码
 *****************************************************************************/
rs485_handler_new_ret_t rs485_event_handler_new_init(rs485_handler_input_new_t *pinput)
{
    rs485_handler_new_ret_t ret_code = RS485_HANDLER_NEW_OK;

    /* 0. 检查输入参数是否为空 */
    if (NULL == pinput)
    {
        RS485_HANDLER_LOG_ERROR("pinput is NULL, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_NEW_ERRORPARAMETER;
    }

    /* 1. 检查必要的接口和配置是否为空 */
    if ((NULL == pinput->p_sys_config) ||
        (NULL == pinput->p_os_interface) ||
        (NULL == pinput->p_ope_fun) ||
        (NULL == pinput->p_uart_interface))
    {
        RS485_HANDLER_LOG_ERROR("One of required interfaces is NULL, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_NEW_ERRORPARAMETER;
    }

    /* 2. 检查参数有效性 */
    if ((0 == pinput->p_sys_config->rs485_event_thread_stack_size) ||
        (pinput->p_sys_config->rs485_event_thread_proirity > (MY_MAX_PRIORITY_NEW - 1)) ||
        (0 == pinput->p_sys_config->rs485_modbus_thread_stack_size) ||
        (pinput->p_sys_config->rs485_modbus_thread_proirity > (MY_MAX_PRIORITY_NEW - 1)) ||
        (0 == pinput->p_sys_config->rs485_event_queue_size))
    {
        RS485_HANDLER_LOG_ERROR("Invalid configuration parameters, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_NEW_ERRORPARAMETER;
    }

    /* 3. 检查是否已初始化 */
    if (true == g_rs485_event_new.inited)
    {
        RS485_HANDLER_LOG_ERROR("RS485 event handler already initialized, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_NEW_ERRORRESOURCE;
    }

    /* 4. 配置全局句柄 */
    g_rs485_event_new.p_sys_config = pinput->p_sys_config;
    g_rs485_event_new.p_os_interfac = pinput->p_os_interface;
    g_rs485_event_data_new.p_ope_fun = pinput->p_ope_fun;
    g_rs485_event_data_new.p_uart_interface = pinput->p_uart_interface;
    g_rs485_event_data_new.p_registers = pinput->p_registers;
    g_rs485_event_data_new.register_count = pinput->register_count;

    /* 5. 创建事件队列 */
    if (RS485_RTOS_PDTRUE_NEW != g_rs485_event_new.p_os_interfac->rtos_queue_create(
        &g_rs485_event_new.rs485_event_handle_queue,
        pinput->p_sys_config->rs485_event_queue_size,
        sizeof(rs485_event_new_t)))
    {
        RS485_HANDLER_LOG_ERROR("Failed to create rs485 event queue, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_NEW_ERROR;
    }

    /* 6. 创建事件处理线程 */
    if (RS485_RTOS_PDTRUE_NEW != g_rs485_event_new.p_os_interfac->rtos_task_create(
        rs485_event_handle_thread_new,
        "rs485_event_handle_thread_new",
        pinput->p_sys_config->rs485_event_thread_stack_size,
        &g_rs485_event_new,
        pinput->p_sys_config->rs485_event_thread_proirity,
        &g_rs485_event_new.rs485_event_handle_thread))
    {
        /* 释放已分配资源 */
        g_rs485_event_new.p_os_interfac->rtos_queue_delete(g_rs485_event_new.rs485_event_handle_queue);
        RS485_HANDLER_LOG_ERROR("Failed to create rs485 event thread, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_NEW_ERROR;
    }

    /* 7. 创建Modbus处理线程 */
    if (RS485_RTOS_PDTRUE_NEW != g_rs485_event_new.p_os_interfac->rtos_task_create(
        rs485_modbus_thread_new,
        "rs485_modbus_thread_new",
        pinput->p_sys_config->rs485_modbus_thread_stack_size,
        &g_rs485_event_new,
        pinput->p_sys_config->rs485_modbus_thread_proirity,
        &g_rs485_event_new.rs485_modbus_thread))
    {
        /* 释放已分配资源 */
        g_rs485_event_new.p_os_interfac->rtos_queue_delete(g_rs485_event_new.rs485_event_handle_queue);
        g_rs485_event_new.p_os_interfac->rtos_task_delete(g_rs485_event_new.rs485_event_handle_thread);
        RS485_HANDLER_LOG_ERROR("Failed to create rs485 modbus thread, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_NEW_ERROR;
    }

    /* 8. 设置私有数据 */
    g_rs485_event_new.private_data = &g_rs485_event_data_new;

    /* 9. 初始化Modbus设备 */
    modbus_input_data_t modbus_input = {0};
    uart_interface_t uart_adapter = {0};

    /* 设置UART适配器 */
    uart_adapter.uart_init = uart_init_adapter_new;
    uart_adapter.uart_send = uart_send_adapter_new;
    uart_adapter.uart_receive = uart_receive_adapter_new;

    /* 配置Modbus输入参数 */
    modbus_input.mode = MODBUS_SLAVE_MODE;
    modbus_input.address = pinput->device_address;
    modbus_input.registers = pinput->p_registers;
    modbus_input.registerCount = pinput->register_count;
    modbus_input.enabled_functions = MODBUS_ENABLE_READ_HOLDING | MODBUS_ENABLE_WRITE_SINGLE | MODBUS_ENABLE_WRITE_MULTIPLE;
    modbus_input.puart_interface = &uart_adapter;
    modbus_input.pf_set_mode_callback = uart_set_mode_adapter_new;
    modbus_input.pf_read_register_cb = modbus_read_register_callback_new;
    modbus_input.pf_write_register_cb = modbus_write_register_callback_new;
    modbus_input.pf_get_reg_type_cb = modbus_get_reg_type_callback_new;

    /* 初始化Modbus设备 */
    if (MODBUS_OK != modbus_instance(&g_rs485_event_new.modbus_device, &modbus_input))
    {
        /* 释放已分配资源 */
        g_rs485_event_new.p_os_interfac->rtos_queue_delete(g_rs485_event_new.rs485_event_handle_queue);
        g_rs485_event_new.p_os_interfac->rtos_task_delete(g_rs485_event_new.rs485_event_handle_thread);
        g_rs485_event_new.p_os_interfac->rtos_task_delete(g_rs485_event_new.rs485_modbus_thread);
        RS485_HANDLER_LOG_ERROR("Failed to initialize modbus device, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_NEW_ERROR;
    }

    /* 10. 注册事件处理函数 */
    if (RS485_HANDLER_NEW_OK != rs485_event_fun_init_new())
    {
        /* 释放已分配资源 */
        g_rs485_event_new.p_os_interfac->rtos_queue_delete(g_rs485_event_new.rs485_event_handle_queue);
        g_rs485_event_new.p_os_interfac->rtos_task_delete(g_rs485_event_new.rs485_event_handle_thread);
        g_rs485_event_new.p_os_interfac->rtos_task_delete(g_rs485_event_new.rs485_modbus_thread);
        RS485_HANDLER_LOG_ERROR("Failed to register rs485 event handlers, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_NEW_ERROR;
    }

    /* 11. 设置初始化标志 */
    g_rs485_event_new.inited = true;

    RS485_HANDLER_LOG_DEBUG("RS485 event handler initialized successfully");
    return RS485_HANDLER_NEW_OK;
}

/******************************************************************************
 * @brief 发送事件到RS485事件队列
 *
 * @param event 要发送的RS485事件
 *
 * @return rs485_handler_new_ret_t 状态码
 *****************************************************************************/
rs485_handler_new_ret_t rs485_event_handle_new_send(rs485_event_new_t event)
{
    if(false == g_rs485_event_new.inited)
    {
        return RS485_HANDLER_NEW_ERRORRESOURCE;
    }

    if (RS485_RTOS_PDTRUE_NEW != g_rs485_event_new.p_os_interfac->rtos_queue_send(
            g_rs485_event_new.rs485_event_handle_queue, &event, MY_MAX_DELAY_NEW))
    {
        return RS485_HANDLER_NEW_ERROR;
    }

    return RS485_HANDLER_NEW_OK;
}

/******************************************************************************
 * @brief 注册定时器回调函数
 *
 * @param timer_callbcakfun 定时器回调函数
 * @param timerperiod 定时器周期（以tick为单位）
 *
 * @return int8_t 状态码（0：成功，负值：错误）
 *****************************************************************************/
int8_t register_timer_new(void (*timer_callbcakfun)(void*),
                     uint32_t timerperiod)
{
    if(false == g_rs485_event_new.inited)
    {
        return -1;
    }

    if( RS485_RTOS_PDTRUE_NEW == g_rs485_event_new.p_os_interfac->rtos_timer_create(
                                                    "rs485_event_handle_timer_new",
                                                    timerperiod,
                                                    NULL,
                                                    timer_callbcakfun,
                                                    &g_rs485_event_new.rs485_event_handle_timer
                                                ))
    {
        return -2;
    }

    if(NULL == g_rs485_event_new.rs485_event_handle_timer)
    {
        return -2;
    }

    return 0;
}

/******************************************************************************
 * @brief 启动定时器
 *
 * @return int8_t 状态码（0：成功，负值：错误）
 *****************************************************************************/
int8_t timer_start_new(void)
{
    if(false == g_rs485_event_new.inited)
    {
        return -1;
    }

    if(RS485_RTOS_PDTRUE_NEW != g_rs485_event_new.p_os_interfac->rtos_timer_start(g_rs485_event_new.rs485_event_handle_timer, 0))
    {
        return -1;
    }

    return 0;
}

/******************************************************************************
 * @brief 停止定时器
 *
 * @return int8_t 状态码（0：成功，负值：错误）
 *****************************************************************************/
int8_t timer_stop_new(void)
{
    if(false == g_rs485_event_new.inited)
    {
        return -1;
    }

    if(RS485_RTOS_PDTRUE_NEW != g_rs485_event_new.p_os_interfac->rtos_timer_stop(g_rs485_event_new.rs485_event_handle_timer, 0))
    {
        return -1;
    }

    return 0;
}

/******************************************************************************
 * @brief 在RS485寄存器表中注册寄存器消息
 *
 * @param reg_adr 寄存器存储位置
 * @param reg_msg 寄存器信息
 *
 * @return int8_t 状态码（0：成功，负值：错误）
 *****************************************************************************/
int8_t rs485_instance_reg_msg_new(reg_storage_place_t reg_adr, rs485_reg_t reg_msg)
{
    if(reg_adr >= REG_QUANTITY)
    {
        return -1;
    }

    g_rs485_reg_new[reg_adr] = reg_msg;
    g_reg_matchup_table_new[reg_adr].reg_place = reg_adr;
    g_reg_matchup_table_new[reg_adr].modbus_start_reg_adr = reg_msg.reg_stru.reg_num;
    g_reg_matchup_table_new[reg_adr].modbus_end_reg_adr =
    reg_msg.reg_stru.reg_num + reg_msg.reg_stru.reg_len - 1;

    return 0;
}

/******************************************************************************
 * @brief 初始化事件处理函数映射
 *
 * @return rs485_handler_new_ret_t 状态码
 *****************************************************************************/
rs485_handler_new_ret_t rs485_event_fun_init_new(void)
{
    g_rs485_event_data_new.event_mapping_count = 0;

    // 注册采集数据输入事件处理函数
    g_rs485_event_data_new.p_event_handler_map[g_rs485_event_data_new.event_mapping_count].event_type = COLLECT_DATA_INPUT_EVENT_NEW;
    g_rs485_event_data_new.p_event_handler_map[g_rs485_event_data_new.event_mapping_count].handler = rs485_handle_collect_data_input_new;
    g_rs485_event_data_new.event_mapping_count++;

    // 注册读取寄存器数据事件处理函数
    g_rs485_event_data_new.p_event_handler_map[g_rs485_event_data_new.event_mapping_count].event_type = READ_REG_DATA_EVENT_NEW;
    g_rs485_event_data_new.p_event_handler_map[g_rs485_event_data_new.event_mapping_count].handler = rs485_handle_read_reg_data_new;
    g_rs485_event_data_new.event_mapping_count++;

    // 注册写入寄存器数据事件处理函数
    g_rs485_event_data_new.p_event_handler_map[g_rs485_event_data_new.event_mapping_count].event_type = WRITE_REG_DATA_EVENT_NEW;
    g_rs485_event_data_new.p_event_handler_map[g_rs485_event_data_new.event_mapping_count].handler = rs485_handle_write_reg_data_new;
    g_rs485_event_data_new.event_mapping_count++;

    // 注册Modbus更新事件处理函数
    g_rs485_event_data_new.p_event_handler_map[g_rs485_event_data_new.event_mapping_count].event_type = MODBUS_UPDATE_EVENT_NEW;
    g_rs485_event_data_new.p_event_handler_map[g_rs485_event_data_new.event_mapping_count].handler = rs485_handle_modbus_update_new;
    g_rs485_event_data_new.event_mapping_count++;

    return RS485_HANDLER_NEW_OK;
}

//******************************** 函数 ************************************//
