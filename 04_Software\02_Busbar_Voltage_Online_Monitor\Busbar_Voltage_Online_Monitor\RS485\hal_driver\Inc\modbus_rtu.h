#ifndef __MODBUS_RTU_H
#define __MODBUS_RTU_H


#include "rs485_dev.h"
#include <stdio.h>
#include <stdbool.h>




typedef enum
{
	PROCESSING,
	COMPLETE,
}modbus_process_state;


typedef union __data_format_cvr
{
	float f_data;
	unsigned int uint_data;
}data_format_cvr;

//当前485设备的地址
#define MODBUS_DEV_ADR_REG DEV_ADR_REG


int8_t modbus_init(void);

void Modbus_RTU_cmd_0x03_Request(PRS485_Device rs485_dev, unsigned short reg_start_addr, unsigned short reg_num);


modbus_process_state Modbus_RTU_cmd_Recv(PRS485_Device rs485_dev, uint32_t timeout_ms);
void Modbus_rtu_Reply(PRS485_Device rs485_dev);

int	Modbus_rtu_dispose_replydata(PRS485_Device rs485_dev);
void	Modbus_rtu_process_data(PRS485_Device rs485_dev);

unsigned short modbuscrc16(unsigned char *ptr, unsigned char len);


#endif /* __MODBUS_RTU_H */
