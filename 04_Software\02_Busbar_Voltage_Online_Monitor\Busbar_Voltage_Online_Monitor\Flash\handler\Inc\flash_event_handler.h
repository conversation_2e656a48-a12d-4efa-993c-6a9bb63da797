#ifndef __FLASH_EVENT_HANDLER_H_
#define __FLASH_EVENT_HANDLER_H_

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "elog.h"
#include "system_config.h"

typedef enum
{
  FLASH_HANDLER_OK                = 0,         /* Operation completed successfully.  */
  FLASH_HANDLER_ERROR             = 1,         /* Run-time error without case matched*/
  FLASH_HANDLER_ERRORTIMEOUT      = 2,         /* Operation failed with timeout      */
  FLASH_HANDLER_ERRORRESOURCE     = 3,         /* Resource not available.            */
  FLASH_HANDLER_ERRORPARAMETER    = 4,         /* Parameter error.                   */
  FLASH_HANDLER_ERRORNOMEMORY     = 5,         /* Out of memory.                     */
  FLASH_HANDLER_ERRORISR          = 6,         /* Not allowed in ISR context         */
  FLASH_HANDLER_RESERVED          = 0x7FFFFFFF /* Reserved                           */
}flash_handler_ret_t;

typedef enum
{
    SAVE_DEV_ADR_EVENT             = 0,        /* 保存设备地址 */
    SAVE_PRODUCT_DATE_EVENT        = 1,        /* 保存产品日期 */
    SAVE_PRODUCT_ID_EVENT          = 2,        /* 保存产品ID */
    SAVE_CALIBRATION_DATA_EVENT    = 3,        /* 保存校准数据 */
    READ_SYSTEM_INFO_EVENT         = 4,        /* 读取系统信息 */
    FLASH_EVENT_QUANTITY           = 5,        /* 事件数量 */
}flash_event_type_t;


typedef enum 
{
    FLASH_RTOS_PDFALSE = 0,
    FLASH_RTOS_PDTRUE  = 1,
}flash_rtos_result_t;


// 声明内部 RTOS 操作接口
typedef struct flash_event_handler_os_interface_t 
{
    flash_rtos_result_t (*rtos_queue_create) (void**queue, uint16_t queue_size, uint16_t item_size);
    flash_rtos_result_t (*rtos_queue_delete) (void* queue);
    flash_rtos_result_t (*rtos_queue_send)   (void* queue, const void *item, uint32_t timeout);
    flash_rtos_result_t (*rtos_queue_receive)(void* queue, void *item, uint32_t timeout);
    flash_rtos_result_t (*rtos_task_create)  (void* task_func, 
                                              const char *name, 
                                              uint16_t stack_size, 
                                              void *params, 
                                              uint32_t priority, 
                                              void **task_handle);
} flash_os_interface_t;

typedef union
{
    uint32_t u32_data;
    char     c_data[8];
    float    f_data;
}data_conv_type_t;
typedef struct
{
    uint32_t             dev_adr;
    uint32_t             product_date;
    uint32_t             product_id;  
    data_conv_type_t     software_version;
    data_conv_type_t     hardware_version;
    data_conv_type_t     calibration_data;
    uint32_t             crc32_data;
}system_info_t;

typedef struct
{
    flash_event_type_t   type;
    uint8_t             dev_adr;
    uint32_t             product_date;
    uint32_t             product_id;  
    float                calibration_data;
    system_info_t        *p_sys_info;
}flash_event_t;


typedef struct
{
    uint8_t  flash_event_thread_proirity;
    uint16_t flash_event_thread_stack_size;
    uint16_t flash_event_queue_size;
}flash_event_system_config_t;

/* 事件处理函数的类型定义 */
typedef flash_handler_ret_t (*flash_event_cb_t)(void *p_flash_store, flash_event_t *pevent);

/* 事件映射结构体 */
typedef struct
{
    flash_event_type_t event_type;
    flash_event_cb_t   handler;
} flash_event_mapping_t;

typedef struct flash_event_handler_private_data_t flash_event_handler_private_data_t;

typedef struct flash_event_input_t
{
    flash_event_system_config_t        *p_config;
    flash_os_interface_t               *pos_interface;
    flash_handler_ret_t                (*pf_flash_event_cb_init)(void);
}flash_event_input_t;

// 在 flash_event_handler_t 结构体中添加 RTOS 接口指针
typedef struct flash_event_handler_t {
    flash_event_system_config_t        *p_config;
    void                               *flash_event_thread;
    void                               *flash_event_queue;
    flash_handler_ret_t                (*pf_flash_event_cb_init)(void);
    flash_event_handler_private_data_t *private_data;
} flash_event_handler_t;
/* 函数声明 */
flash_handler_ret_t flash_event_handler_init(flash_event_input_t *p_config);
flash_handler_ret_t flash_event_send(flash_event_t* event);
flash_handler_ret_t flash_read_reg_value(system_info_t* p_system_info);
flash_handler_ret_t flash_register_handler(flash_event_type_t type, flash_event_cb_t handler);

#endif /* __FLASH_EVENT_HANDLER_H_ */
