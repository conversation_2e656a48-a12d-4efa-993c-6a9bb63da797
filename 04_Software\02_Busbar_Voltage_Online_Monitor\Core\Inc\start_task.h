/******************************************************************************
 * @file start_task.h
 * @brief 
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-12
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 
 * @par dependencies
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/
#ifndef __START_TASK_H__
#define __START_TASK_H__

#include <stdint.h>
#include "elog.h"


#endif //  END OF __START_TASK_H__