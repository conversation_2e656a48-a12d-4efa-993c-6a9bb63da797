# 同步采样谐波分析系统 - 嵌入式开发文档

## 一、项目背景

本系统应用于电力行业的氧化锌避雷器与母线电压的在线监测，旨在通过同步采样技术，实现对 50Hz 市电及其谐波成分的准确幅值与相位计算，误差控制在 2% 和 1 度以内。

---

## 二、系统架构概述

**硬件平台：** STM32F411CEUx + AD7606 + LM211

**软件核心：** DMA + SPI + TIM 输入捕获 + 软件锁相 + FFT + 窗函数 + 插值算法

---

## 三、系统功能需求

| 模块   | 功能                                               |
| ---- | ------------------------------------------------ |
| 零点捕获 | 捕获市电过零点上升沿，记录时间戳和次数标号                            |
| 同步采样 | AD7606 通过定时器触发，使用 SPI + DMA 获取数据                 |
| 数据绑定 | 每次 DMA 完成时绑定帧起始时间戳 `t_sample0` 和 `capture_count` |
| 数据缓冲 | 使用环形缓冲区缓存采样帧供 FFT 分析任务读取                         |
| 频谱分析 | 使用 Nuttall 窗 + CMSIS FFT 分析时域数据                  |
| 插值修正 | 三谱线插值获得精确频率和幅度，结合 `t_zero` 进行相位修正                |

---

## 四、软件功能需求细化

### 4.1 任务调度与资源划分

- 使用 FreeRTOS 创建以下任务：
  - `ZeroDetectTask`：处理 TIM 输入捕获中断并更新 `t_zero` 与 `capture_count`
  - `AdcSampleTask`：定时触发 AD7606 启动采样
  - `DmaProcessTask`：DMA 采样完成回调后进行数据打包与入队
  - `FftAnalysisTask`：定时处理缓存队列中的数据帧，执行 FFT、相位修正与结果输出

### 4.2 中断与时间戳模块

- 系统引入 `ZeroDetectTask` 任务，用于专门处理市电零点的捕获与频率分析：

  - 由 TIM4 捕获市电输入信号（来自 LM211 比较器）上升沿作为过零点；
  - 每次捕获后记录当前 `__HAL_TIM_GET_COUNTER(&htim4)` 作为零点时刻 `t_zero`；
  - 自增 `capture_count` 编号；
  - 周期性测量市电周期，计算并更新 `grid_freq_hz` 频率值（推荐每 5 个周期更新一次）。

- 使用 TIM3 用于采样信号（如避雷器电流或母线电压）的频率检测：

  - 捕获其上升沿脉冲，计算频率 `signal_freq_hz`；
  - 与 `grid_freq_hz` 区分管理，便于同步与独立频率评估。

- 中断模块输出以下公共信息，供 FFT 与分析任务访问：

  - `t_zero`：最近一次过零点的 16 位 CNT 时间值；
  - `capture_count`：过零点次数标号（16 位）；
  - `grid_freq_hz`：电网频率估算值；
  - `signal_freq_hz`：采样信号频率估算值。

- 系统采用压缩时间戳方案：

  - 使用 `uint32_t timestamp` 打包采样标识
  - 高 16 位：`capture_count`（零点编号）
  - 低 16 位：`t_sample0`（当前采样起始时间 CNT 值）
  - 定义宏：
    ```c
    #define PACK_TIMESTAMP(capture, cnt) (((capture) << 16) | ((cnt) & 0xFFFF))
    #define UNPACK_CAPTURE(ts) ((ts) >> 16)
    #define UNPACK_T0(ts) ((ts) & 0xFFFF)
    ```

- 为防止时间模糊，在系统运行期间若检测 `capture_count >= 0xFFFC`，则：

  - 将 `capture_count` 清零，
  - 同时清空所有 `AD7606` 数据缓冲和分析队列（调用 `ClearAllBuffers()`）
  - 通知 FFT 模块重置状态

- 建议增强细节：

  - 每次 `capture_count` 自增时，同步备份 `last_t_zero`，用于两周期频率平均值计算；
  - 输出接口支持 `struct GridSyncStatus` 结构体封装全部状态信息，方便主机监控与日志记录：
    ```c
    typedef struct {
        uint16_t capture_count;
        uint16_t t_zero;
        float grid_freq_hz;
        float signal_freq_hz;
    } GridSyncStatus;
    ```

- TIM3 或 TIM4 为 16 位计数器（1us分辨率，最大 65535us）

- 系统采用压缩时间戳方案：

  - 使用 `uint32_t timestamp` 打包采样标识
  - 高 16 位：`capture_count`（零点编号）
  - 低 16 位：`t_sample0`（当前采样起始时间 CNT 值）
  - 定义宏：
    ```c
    #define PACK_TIMESTAMP(capture, cnt) (((capture) << 16) | ((cnt) & 0xFFFF))
    #define UNPACK_CAPTURE(ts) ((ts) >> 16)
    #define UNPACK_T0(ts) ((ts) & 0xFFFF)
    ```

- 为防止时间模糊，在系统运行期间若检测 `capture_count >= 0xFFFC`，则：

  - 将 `capture_count` 清零，
  - 同时清空所有 `AD7606` 数据缓冲和分析队列（调用 `ClearAllBuffers()`）
  - 通知 FFT 模块重置状态

### 4.3 数据采样与缓存模块

- AD7606 使用 SPI + DMA 双缓冲进行 1024 点采样
- DMA 完成后记录：
  - 当前打包时间戳：`uint32_t timestamp = PACK_TIMESTAMP(capture_count, __HAL_TIM_GET_COUNTER(&htim3))`
  - 电压数组转化后放入环形缓冲队列中

### 4.4 FFT处理模块

- 从缓冲区取出帧，判断其绑定的 `capture_count` 是否与当前零点接近
- 使用 Nuttall 窗加权后执行 CMSIS-DSP FFT
- 对 50Hz bin 及其相邻频点执行三谱线插值，提升幅度与频率精度
- **不采用滑动窗口处理 FFT，避免窗口起点偏移带来的相位误差。**
- 每次 FFT 分析仅使用完整的 1024 点帧，确保采样起点 `t_sample0` 与分析起点严格一致，以保证相位修正的准确性

### 4.5 相位修正模块

- 利用 `t_sample0` 与最近 `t_zero` 的差计算当前帧整体延迟角
- FFT 得到的频域相位扣除采样起点相位差后即为同步相位角

### 4.6 输出与通信接口

- 支持通过 UART 或 CAN 输出结构体 `HarmonicResult`
- 可扩展上传 JSON 或 MODBUS 格式供主机解析

### 4.7 异常与边界处理

- 若 DMA 绑定的 `capture_count` 与当前相差大于 1，说明采样帧已滞后于当前零点两个及以上周期，此时放弃该帧处理以避免相位误判。

| 捕获编号差值 `Δc`      | 建议操作      | 说明                     |
| ---------------- | --------- | ---------------------- |
| `Δc == 0`（当前周期）  | ✅ 正常处理    | 采样帧与当前周期同步，可用于相位分析     |
| `Δc == 1`（上一个周期） | ⚠️ 可选保留备用 | 若当前帧丢失可回退分析，但会带来一定相位误差 |
| `Δc > 1`（过期数据）   | ❌ 丢弃该帧    | 已超出一个电网周期，无法做同步相位修正    |

- 若输入捕获频率异常（非45\~65Hz），中止分析并上报故障码
- 若 `capture_count` 接近 65535，自动触发系统“时间戳归零与缓存重置”流程

---

## 八、数据结构定义

```c
// 时间戳打包方式（高16位为capture_count，低16位为t_sample0）
typedef struct {
    uint32_t timestamp;        // 打包后的时间戳（capture_count << 16 | t_sample0）
    float voltage[1024];       // 采样数据
} SampleFrame;

typedef struct {
    float freq;
    float magnitude;
    float phase_rad;
    uint32_t capture_count;
} HarmonicResult;
```

---

