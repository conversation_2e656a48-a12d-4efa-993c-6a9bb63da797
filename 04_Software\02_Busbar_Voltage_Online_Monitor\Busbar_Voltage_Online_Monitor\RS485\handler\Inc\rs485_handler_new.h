/******************************************************************************
 * @file rs485_handler_new.h
 * @brief 新的RS485事件处理模块 - 使用标准Modbus RTU协议
 * <AUTHOR>
 * @version 2.0
 * @date 2024-11-25
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 本模块处理RS485通信事件并管理Modbus寄存器，使用新的标准Modbus RTU协议实现
 * 
 * @par dependencies
 * FreeRTOS, modbus_rtu_standard
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/
#ifndef __RS485_HANDLER_NEW_H_
#define __RS485_HANDLER_NEW_H_

//******************************** Includes *********************************//
#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "modbus_rtu_standard.h"  // 使用新的标准Modbus RTU协议
#include "reg_adress_msg.h"
#include "elog.h"
#include "system_config.h"
//******************************** Includes *********************************//

//******************************** Defines **********************************//
/* 调试宏定义已迁移到 system_config.h 中统一管理 */

#ifdef  DEBUG_RS485_HANDLER_NEW
// #define RS485_HANDLER_DEBUG_PR_ENABLE
// #define RS485_HANDLER_LOG_D_ENABLE
#define RS485_HANDLER_LOG_E_ENABLE
#endif

#ifdef  DEBUG_RS485_HANDLER_NEW
    #ifdef RS485_HANDLER_DEBUG_PR_ENABLE
            #define RS485_HANDLER_DEBUG_PR(...) printf(__VA_ARGS__)
    #else
            #define RS485_HANDLER_DEBUG_PR(...)
    #endif
    #ifdef RS485_HANDLER_LOG_D_ENABLE
        #define RS485_HANDLER_LOG_DEBUG(...) log_d(__VA_ARGS__)
    #else
        #define RS485_HANDLER_LOG_DEBUG(...)
    #endif
    #ifdef RS485_HANDLER_LOG_E_ENABLE
        #define RS485_HANDLER_LOG_ERROR(...) log_e(__VA_ARGS__)
    #else
        #define RS485_HANDLER_LOG_ERROR(...)
    #endif
#endif

// RS485处理器函数返回代码定义
typedef enum
{
  RS485_HANDLER_NEW_OK                = 0,         /* 操作成功完成              */
  RS485_HANDLER_NEW_ERROR             = 1,         /* 运行时错误，无匹配情况    */
  RS485_HANDLER_NEW_ERRORTIMEOUT      = 2,         /* 操作超时失败              */
  RS485_HANDLER_NEW_ERRORRESOURCE     = 3,         /* 资源不可用                */
  RS485_HANDLER_NEW_ERRORPARAMETER    = 4,         /* 参数错误                  */
  RS485_HANDLER_NEW_ERRORNOMEMORY     = 5,         /* 内存不足                  */
  RS485_HANDLER_NEW_ERRORISR          = 6,         /* 在ISR上下文中不允许       */
  RS485_HANDLER_NEW_RESERVED          = 0x7FFFFFFF /* 保留                      */
}rs485_handler_new_ret_t;

// 事件类型定义
typedef enum
{
    COLLECT_DATA_INPUT_EVENT_NEW = 0,   /* 采集数据输入事件 */
    READ_REG_DATA_EVENT_NEW      = 1,   /* 读取寄存器数据事件 */
    WRITE_REG_DATA_EVENT_NEW     = 2,   /* 写入寄存器数据事件 */
    MODBUS_UPDATE_EVENT_NEW      = 3,   /* Modbus更新事件 */
    RS485_EVENT_QUANTITY_NEW     = 4,   /* 事件数量 */
}rs485_event_type_new_t;

typedef enum
{
	RS485_RTOS_PDFALSE_NEW = 0,
	RS485_RTOS_PDTRUE_NEW  = 1,
}rs485_os_ret_new_t;

// 保存寄存器值结构定义
typedef struct
{
    uint32_t dev_adr;    /* 设备地址 */
    // 其他需要从flash保存的值
} save_reg_value_new_t;

// 读取寄存器数据回调函数类型
typedef void (*read_reg_data_callbackfun_new)(uint16_t reg_data, void* read_data);

// RS485事件信息结构体
typedef struct 
{
    uint16_t                      reg_num;                /* 寄存器号 */
    rs485_event_type_new_t        event_type;             /* 事件类型 */
    rs485_reg_data_t              reg_data;               /* 寄存器数据 */
    read_reg_data_callbackfun_new read_reg_data_callback; /* 读取寄存器数据回调函数 */
    void*                         read_data;              /* 回调函数参数，用于存储寄存器数据 */
    uint32_t                      timestamp;              /* 时间戳 */
}rs485_event_new_t;

// RS485事件线程系统配置
typedef struct
{
    uint8_t  rs485_event_thread_proirity;         /* 事件处理线程优先级 */
    uint16_t rs485_event_thread_stack_size;       /* 事件处理线程栈大小 */
    uint8_t  rs485_modbus_thread_proirity;        /* Modbus处理线程优先级 */
    uint16_t rs485_modbus_thread_stack_size;      /* Modbus处理线程栈大小 */
    uint16_t rs485_event_queue_size;              /* 事件队列大小 */
    uint32_t modbus_update_period_ms;             /* Modbus更新周期(毫秒) */
}rs485_event_system_config_new_t;

// RS485事件处理函数类型定义
typedef rs485_handler_new_ret_t (*rs485_event_handler_func_new_t)(void *p_private, rs485_event_new_t *p_event);

// RS485事件处理映射表项结构
typedef struct {
    rs485_event_type_new_t         event_type;  /* 事件类型 */
    rs485_event_handler_func_new_t handler;     /* 对应的处理函数 */
}rs485_event_handler_map_new_t;

// 事件处理器私有数据结构前向声明
typedef struct rs485_event_handler_private_data_new_t rs485_event_handler_private_data_new_t;

// RTOS接口结构体
typedef struct {
    rs485_os_ret_new_t (*rtos_queue_create) (void** queue, uint32_t queue_size, uint32_t item_size);
    rs485_os_ret_new_t (*rtos_task_create) (void (*task)(void*), const char* name, uint32_t stack_size,
                                      void* param, uint32_t priority, void** task_handle);
    rs485_os_ret_new_t (*rtos_queue_delete)(void* queue);
    rs485_os_ret_new_t (*rtos_task_delete)(void* task_handle);
    rs485_os_ret_new_t (*rtos_queue_send)(void* queue, void* item, uint32_t block_time_ticks);
    rs485_os_ret_new_t (*rtos_queue_receive)(void* queue, void* item, uint32_t block_time_ticks);
    rs485_os_ret_new_t (*rtos_timer_create)(const char* name, 
                                        uint32_t timer_period_ticks,
                                        void* timer_id,
                                        void (*callback)(void*),
                                        void** timer_handle);
    rs485_os_ret_new_t (*rtos_timer_start)(void* timer, uint32_t block_time_ticks);
    rs485_os_ret_new_t (*rtos_timer_stop)(void* timer, uint32_t block_time_ticks);
    rs485_os_ret_new_t (*rtos_timer_delete)(void* timer, uint32_t block_time_ticks);
    uint32_t (*rtos_get_tick_count)(void);  /* 获取系统时钟计数 */
} rs485_rtos_interface_new_t;

// RS485操作函数结构体
typedef struct 
{
    rs485_handler_new_ret_t (*rs485_save_reg_value)(uint16_t reg_place, rs485_reg_data_t* reg_value);
    rs485_handler_new_ret_t (*rs485_read_reg_value)(uint16_t reg_place, rs485_reg_data_t* reg_value);
    // 其它操作函数...
} rs485_ope_fun_new_t;

// UART设备接口结构体
typedef struct uart_dev_interface_new_t
{
    int (*uart_init)    (struct uart_dev_interface_new_t *puart_dev);
    int (*uart_send)    (struct uart_dev_interface_new_t* puart_dev, 
                         uint8_t *datas, int len, 
                         uint32_t timeout_ms);
    int (*uart_receive) (struct uart_dev_interface_new_t* puart_dev,
                         uint8_t *data, 
                         int timeout_ms);
    void (*uart_set_mode)(struct uart_dev_interface_new_t* puart_dev, int mode); /* 设置收发模式 */
}uart_dev_interface_new_t;

// 处理器初始化输入参数结构体
typedef struct 
{
    rs485_event_system_config_new_t* p_sys_config;
    rs485_rtos_interface_new_t*      p_os_interface;
    rs485_ope_fun_new_t*             p_ope_fun;
    uart_dev_interface_new_t*        p_uart_interface;
    uint8_t                          device_address;      /* 设备地址 */
    uint16_t                         register_count;      /* 寄存器数量 */
    uint16_t*                        p_registers;         /* 寄存器数组指针 */
} rs485_handler_input_new_t;

// RS485事件处理器主结构
typedef struct 
{
    bool                                    inited;                    /* 初始化状态 */
    void*                                   rs485_event_handle_thread; /* 事件处理线程句柄 */
    void*                                   rs485_modbus_thread;       /* Modbus处理线程句柄 */
    void*                                   rs485_event_handle_queue;  /* 事件队列句柄 */
    void*                                   rs485_event_handle_timer;  /* 定时器句柄 */
    rs485_event_system_config_new_t         *p_sys_config;            /* 系统配置 */
    rs485_rtos_interface_new_t              *p_os_interfac;           /* OS接口 */
    rs485_event_handler_private_data_new_t  *private_data;            /* 私有数据指针 */
    modbus_device_t                         modbus_device;            /* Modbus设备实例 */
} rs485_event_handler_new_t;

//******************************** 函数原型 ********************************//

/**
 * @brief 初始化RS485事件处理器
 * 
 * @param pinput 输入参数结构体，包含必要的系统配置和接口
 * 
 * @return rs485_handler_new_ret_t 状态码
 */
rs485_handler_new_ret_t rs485_event_handler_new_init(rs485_handler_input_new_t *pinput);

/**
 * @brief 发送事件到RS485事件队列
 * 
 * @param event 要发送的RS485事件
 * 
 * @return rs485_handler_new_ret_t 状态码
 */
rs485_handler_new_ret_t rs485_event_handle_new_send(rs485_event_new_t event);

/**
 * @brief 注册定时器回调函数
 * 
 * @param timer_callbcakfun 定时器回调函数
 * @param timerperiod 定时器周期（以tick为单位）
 * 
 * @return int8_t 状态码（0：成功，负值：错误）
 */
int8_t register_timer_new(void (*timer_callbcakfun)(void*), uint32_t timerperiod);

/**
 * @brief 启动定时器
 * 
 * @return int8_t 状态码（0：成功，负值：错误）
 */
int8_t timer_start_new(void);

/**
 * @brief 停止定时器
 * 
 * @return int8_t 状态码（0：成功，负值：错误）
 */
int8_t timer_stop_new(void);

/**
 * @brief 在RS485寄存器表中注册寄存器消息
 * 
 * @param reg_adr 寄存器存储位置
 * @param reg_msg 寄存器信息
 * 
 * @return int8_t 状态码（0：成功，负值：错误）
 */
int8_t rs485_instance_reg_msg_new(reg_storage_place_t reg_adr, rs485_reg_t reg_msg);

/**
 * @brief 初始化事件处理函数映射
 * 
 * @return rs485_handler_new_ret_t 状态码
 */
rs485_handler_new_ret_t rs485_event_fun_init_new(void);

/**
 * @brief 获取当前系统时间戳
 * 
 * @return uint32_t 当前时间戳
 */
uint32_t rs485_get_timestamp_new(void);

//******************************** 函数原型 ********************************//

#endif // __RS485_HANDLER_NEW_H_
