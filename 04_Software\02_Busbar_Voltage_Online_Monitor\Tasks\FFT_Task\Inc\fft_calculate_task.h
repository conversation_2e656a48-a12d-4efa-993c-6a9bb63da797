/******************************************************************************
 * @file fft_calculate_task.h
 * @brief 
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-12
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 
 * @par dependencies
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/
#ifndef __FFT_CALCULATE_TASK_H_
#define __FFT_CALCULATE_TASK_H_
//******************************** Includes *********************************//
#include <stdint.h>
#include <stdio.h>
#include "math.h"
#include "arm_math.h"
#include "arm_const_structs.h"
#include "lpf_fir_alogorithm.h"
#include "algorithm.h"
#include "elog.h"
#include "system_config.h"
//******************************** Includes *********************************//

//******************************** Defines **********************************//
/* 调试宏定义已迁移到 system_config.h 中统一管理 */
#ifdef DEBUG_FFT_CALCU_TASK
#define FFT_CALCU_DEBUG_PR(...) printf(__VA_ARGS__)
#define FFT_CALCU_LOG_DEUBG(...) log_d(__VA_ARGS__)
#define FFT_CALCU_LOG_ERROR(...) log_e(__VA_ARGS__)
#else
#define FFT_CALCU_DEBUG_PR(...)
#define FFT_CALCU_LOG_DEUBG(...)
#define FFT_CALCU_LOG_ERROR(...)
#endif

#define CONFIG_USE_SLIDING_WINDOW 1  //是否使用滑动窗口
#define SYNC_FFT_ENABLE     1     //启用同步FFT计算


typedef enum
{
  FFT_CALCU_OK                = 0,         /* Operation completed successfully.  */
  FFT_CALCU_ERROR             = 1,         /* Run-time error without case matched*/
  FFT_CALCU_ERRORTIMEOUT      = 2,         /* Operation failed with timeout      */
  FFT_CALCU_ERRORRESOURCE     = 3,         /* Resource not available.            */
  FFT_CALCU_ERRORPARAMETER    = 4,         /* Parameter error.                   */
  FFT_CALCU_ERRORNOMEMORY     = 5,         /* Out of memory.                     */
  FFT_CALCU_ERRORISR          = 6,         /* Not allowed in ISR context         */
  FFT_CALCU_RESERVED          = 0x7FFFFFFF /* Reserved                           */
}fft_calcu_ret_t;

typedef enum
{
    FFT_CALCU_RTOS_PDFALSE = 0,
    FFT_CALCU_RTOS_PDTRUE  = 1,
}fft_calcu_os_ret_code_t;


/* 对外接口数据类型定义 */
typedef enum
{
    FFT_VOLTAGE_AC_DATA             = 0,
    FFT_VOLTAGE_UA_DATA             = 1,
    FFT_VOLTAGE_UB_DATA             = 2,
    FFT_VOLTAGE_UC_DATA             = 3,
    FFT_AC_AND_THREE_PHASE_DATA     = 4,
    FFT_ALL_DATA                    = 5,
}fft_data_type_t;

/* 输入数据属性，由调用者决定输入数据类型*/
typedef enum
{
    FFT_INPUT_VOLTAGE_AC = 0,
    FFT_INPUT_VOLTAGE_UA = 1,
    FFT_INPUT_VOLTAGE_UB = 2,
    FFT_INPUT_VOLTAGE_UC = 3,
    FFT_INPUT_QUANTITY,
#if CONFIG_USE_SLIDING_WINDOW
    FFT_INPUT_CLEAR_DATA,
#endif /* CONFIG_USE_SLIDING_WINDOW */
}fft_data_property_t;


/* 1.FFT计算类型,根据采集频率和计算频率的不同，选择不同的计算方式，目前支持同步计算
*  2.如果有两个不同频率的采集数据，则需要异步计算FFT，否则使用同步计算FFT，异步计算待实现
*/
typedef enum
{
    FFT_SYNC_CALCU_TYPE  = 0,   /* 同步计算FFT */
    FFT_ASYNC_CALCU_TYPE = 1,   /* 异步计算FFT */
}fft_calcu_type_t;


typedef enum //定义窗的种类
{
    NONE = 0,           //不加窗，即矩形窗
    HANNING_WIN,        //汉宁窗（能处理大部分的问题）
    HAMMING_WIN,        //汉明窗
    FLATTOP_WIN,        //平顶窗（算基频幅值很准）
    KAISER_WIN          //凯泽窗（算THD很准）
}window_type_t;



typedef struct
{
    fft_data_property_t         *fft_input_data_property;
    fft_data_type_t              fft_data_type;
    fft_calcu_type_t             fft_calcu_type;
}fft_collect_msg_t;

//******************************** Defines **********************************//

//******************************** Variables ********************************//
/*                 From OS层：       RTOS 接口                                */
typedef void (*task_function_t)(void *);
// 定义一个结构体，用于表示RTOS的接口

/* 进入临界区接口*/
typedef struct
{
    fft_calcu_os_ret_code_t (*rtos_enter_critical) (void);
    fft_calcu_os_ret_code_t (*rtos_exit_critical)  (void);
}fft_calcu_critical_t;
typedef struct fft_calcu_handler_os_interface_t
{
    
    // 创建任务
    fft_calcu_os_ret_code_t (*rtos_task_create)           (task_function_t task_function,
                                                           const char * const task_name,
                                                           const uint16_t stack_size,
                                                           void * const task_argument,
                                                           uint32_t priority,
                                                           void ** const task_handle);
        //队列创建
    fft_calcu_os_ret_code_t (*rtos_queue_create)          (void ** const pqueue,
                                                            uint32_t queue_size,
                                                            uint32_t item_size);
    //队列删除                                                                
    fft_calcu_os_ret_code_t (*rtos_queue_delete)          (void * const pqueue);
    //队列发送
    fft_calcu_os_ret_code_t (*rtos_queue_send)            (void * const pqueue,
                                                           void * const item,
                                                           uint32_t timeout);
    //队列接收
    fft_calcu_os_ret_code_t (*rtos_queue_receive)         (void * const pqueue,
                                                           void * const item,
                                                           uint32_t timeout);
// 创建二进制信号量
    fft_calcu_os_ret_code_t (*rtos_semphorebinary_create) (void ** const psemphore);
// 删除信号量
    fft_calcu_os_ret_code_t (*rtos_semphore_delete)       (void * const psemphore);
// 获取信号量
    fft_calcu_os_ret_code_t (*rtos_semphore_take)         (void * const psemphore, 
                                                           uint32_t timeout);
// 中断中释放信号量
    fft_calcu_os_ret_code_t (*rtos_semphore_give_formisr) (void *const psemphore);
//任务通知，实现二进制信号量
    fft_calcu_os_ret_code_t (*rtos_task_notifiy_give)     (void * const task_handle);
//任务等待，实现二进制信号量
    fft_calcu_os_ret_code_t (*rtos_task_notifiy_take)     (uint32_t timeout);
// 获取任务句柄
    fft_calcu_os_ret_code_t (*rtos_task_get_handle)        (void **const task_handle);
    // 事件组相关接口
    fft_calcu_os_ret_code_t (*rtos_event_group_create)     (void **const event_group);
    fft_calcu_os_ret_code_t (*rtos_event_group_delete)     (void *const event_group);
    fft_calcu_os_ret_code_t (*rtos_event_group_set_bits)   (void *const event_group,
                                                             const uint32_t bits_to_set);
    fft_calcu_os_ret_code_t (*rtos_event_group_clear_bits) (void *const event_group,
                                                             const uint32_t bits_to_clear);
    uint32_t                (*rtos_event_group_wait_bits)  (void *const event_group,
                                                             const uint32_t bits_to_wait_for,
                                                             const uint8_t clear_on_exit,
                                                             const uint8_t wait_for_all_bits,
                                                             const uint32_t timeout);
}fft_calcu_handler_os_interface_t;
/*                 From OS层：       RTOS 接口                                */
#ifdef FFT_CALCU_USING_IRQ
/*                 From Core层：     中断回调函数接口                          */
typedef int8_t (*pfirq_callbackfun_t)(void*);

/* 定时器采集数据接口 */
typedef struct 
{
    void                const **params;
    pfirq_callbackfun_t *pfirq_callbackfun;
}fft_calcu_callback_fun_t;
/*                 From Core层：     中断回调函数接口                          */
typedef struct 
{
    fft_calcu_ret_t (*fft_start_timer_collect)(void);  /* 开始采集数据接口 */
    fft_calcu_ret_t (*fft_stop_timer_collect) (void);  /* 停止采集数据接口 */
}fft_calcu_timer_collect_t;
#endif /* FFT_CALCU_USING_IRQ */
/*                 From Core层：     时基接口                                 */
typedef struct
{
    uint32_t (*pfget_timetick_ms) (void);
}fft_calcu_system_timebase_interface_t;
/*                 From Core层：     时基接口                                 */

typedef struct
{
    fft_data_property_t data_property;
    float               data_value;
}fft_input_data_type_t;

/* 带时间戳的FFT输入数据结构 */
typedef struct
{
    fft_data_property_t data_property;
    float               data_value;
    uint32_t            capture_count;      /* 过零点捕获计数 */
    uint32_t            t_zero;            /* 过零点时间戳 */
    uint32_t            t_sample0;         /* 采样起始时间戳 */
    uint32_t            frame_id;          /* 帧ID */
    bool                timestamp_valid;    /* 时间戳有效标志 */
}fft_input_timestamped_data_t;

typedef union  fft_result_data_type_t
{
    struct
    {
        float fft_result_amplitude_data;    /* FFT基频幅值结果数据 */   
        float fft_result_phase_data;        /* FFT基频相位结果数据 */
        float fft_result_freq_data;         /* FFT基频频率结果数据 */
    }fundation_data;                        /* 基频数据,主要用于电流、交流电压 */
    struct
    {
        float fft_result_amplitude_data;    /* FFT基频幅值结果数据 */   
        float fft_result_phase_data;        /* FFT基频相位结果数据 */
        float fft_result_amp_3rd_data;      /* FFT 3次谐波幅值结果数据*/    
        float fft_result_amp_5rd_data;      /* FFT 5次谐波幅值结果数据*/
        float fft_result_amp_7rd_data;      /* FFT 7次谐波幅值结果数据*/
        float fft_result_amp_9rd_data;      /* FFT 9次谐波幅值结果数据*/
    }three_phase_vol_data;                  /* 三相电压数据,主要用于三相电压计算 */
}fft_result_data_type_t;

typedef struct 
{
    fft_data_property_t    data_property;       /* 数据属性 */
    fft_result_data_type_t fft_result_data;     /* FFT结果数据 */
}fft_result_event_t;
typedef struct 
{
    fft_calcu_ret_t (*fft_get_collect_data) (fft_input_data_type_t*,       /* 获取采集数据接口 */
                                             fft_data_type_t);  
    fft_calcu_ret_t (*fft_check_data_valid) (fft_data_property_t, float);  /* 检查采集数据有效性接口 */
    fft_calcu_ret_t (*fft_get_collect_data_limit) (float*);                /* 获取采集数据限制接口 */
    fft_calcu_ret_t (*fft_result_data_send) (fft_result_event_t*);         /* 发送FFT结果数据接口 */
}fft_calcu_get_data_interface_t;

typedef struct
{
    uint8_t  fft_collection_data_thread_proirity;
    uint16_t fft_collection_data_thread_stack_size;
    uint16_t fft_finish_collection_queue_size;
}fft_calcu_system_config_t;



typedef struct
{
    fft_calcu_system_config_t             *pfft_calcu_system_config;
    fft_collect_msg_t                     *fft_collect_msg;
    fft_calcu_handler_os_interface_t      *fft_calcu_os_interface;
    fft_calcu_system_timebase_interface_t *fft_calcu_system_timebase_interface;
#ifdef FFT_CALCU_USING_IRQ
    fft_calcu_callback_fun_t              *fft_calcu_callback_fun;
    fft_calcu_timer_collect_t             *fft_timer_collect_interface;
#endif /* FFT_CALCU_USING_IRQ */
    fft_calcu_get_data_interface_t        *fft_get_data_interface;
    fft_calcu_critical_t                  *fft_calcu_critical_interface;
    lpf_fir_t                             *plpf_fir;
}fft_calcu_input_data_t;

typedef struct fft_calcu_private_data_t fft_calcu_private_data_t;
// 定义一个结构体，用于存储采集事件处理器的数据
typedef struct
{
    fft_calcu_system_config_t             *pfft_calcu_system_config;
    fft_calcu_handler_os_interface_t      *fft_calcu_os_interface;
    fft_calcu_system_timebase_interface_t *fft_calcu_system_timebase_interface;
#ifdef FFT_CALCU_USING_IRQ
    fft_calcu_callback_fun_t              *fft_calcu_callback_fun;
    fft_calcu_timer_collect_t             *fft_timer_collect_interface;
#endif /* FFT_CALCU_USING_IRQ */
    fft_calcu_get_data_interface_t        *fft_get_data_interface;
    fft_calcu_critical_t                  *fft_calcu_critical_interface;
    lpf_fir_t                             *plpf_fir;
    // fft数据采集数据线程
    void *fft_collection_data_thread;
    // 计算FFT线程
    void *fft_event_handler_thread;
#ifdef FFT_CALCU_USING_IRQ
    // 采集完成信号量
    void *fft_start_collection_semaphore;
#endif /* FFT_CALCU_USING_IRQ */
    // 采集完成队列
    void *fft_finish_collection_queue;
#if SYNC_FFT_ENABLE
    // 事件处理线程事件组
    void *fft_handler_event_group;
#endif /* SYNC_FFT_ENABLE */
    // 私有数据
    fft_calcu_private_data_t* private_data;
}fft_calcu_handler_t;



void fft_event_handler_thread(void *arg);

#if CONFIG_USE_SLIDING_WINDOW
fft_calcu_ret_t fft_data_input_clear(void);
#endif /* CONFIG_USE_SLIDING_WINDOW */



#endif /* __FFT_CALCULATE_TASK_H_ */
