#ifndef __LED_EVENT_HANDLER_H_
#define __LED_EVENT_HANDLER_H_

#include <stdint.h>
#include <stdbool.h>
#include "led_device.h"


typedef enum
{
    LED_EVENT_THREAD,
    LED_EVENT_IRQ,
}send_type;

typedef enum
{
    LED_EVENT_ON,
    LED_EVENT_OFF,
    LED_EVENT_TOGGLE,
}led_event_type;


typedef struct
{
    bool inited;
    void* led_run_thread;
    void* led_rs485_thread;
    void* led_event_handle_queue;
    void* private_data;
}led_event_handle_t;




int8_t led_event_handle_init(void);
int8_t led_event_handle_send(led_event_type event, send_type type);

#endif // __LED_EVENT_HANDLER_H_
