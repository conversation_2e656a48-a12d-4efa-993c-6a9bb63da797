/******************************************************************************
 * @file flash_event_fun.c
 * @brief Flash事件处理函数
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-16
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 
 * @par dependencies
 * 
 * @note 1 tab == 4 spaces!
 *****************************************************************************/

//******************************** Includes *********************************//
#include "flash_event_fun.h"
#include "elog.h"
#include "mcu_flash_driver.h"
//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define DEFAULT_CALIBRATION_DATA 1.0f
#define DEFAULT_DEV_ADR 1
#define MAX_DEV_ADR     255
#define BCAST_ADR       0
#define FLASH_NO_WRITE 0xFFFFFFFF

//******************************** Defines **********************************//

//******************************** Variables *********************************//
#define CRC32_POLY 0xEDB88320UL
//******************************** Variables *********************************//

//******************************** Functions *********************************//
/**
 * @brief 计算输入数据的 CRC-32 校验值
 * 
 * CRC-32 是一种循环冗余校验算法，常用于检测数据在传输或存储过程中是否发生错误。
 * 此函数使用标准的 CRC-32 多项式 0xEDB88320。
 * 
 * @param data 指向要计算 CRC-32 校验值的数据的指针
 * @param length 数据的字节长度
 * @return uint32_t 计算得到的 CRC-32 校验值
 */
uint32_t crc32(const void *data, size_t length) {
    const uint8_t *bytes = (const uint8_t *)data;
    uint32_t crc = 0xFFFFFFFFUL;

    for (size_t i = 0; i < length; i++) {
        crc ^= bytes[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 1)
                crc = (crc >> 1) ^ CRC32_POLY;
            else
                crc >>= 1;
        }
    }

    return ~crc;
}
/**
 * @brief 检查系统信息是否有效，并进行必要的修正
 * 
 * @param sys_info 系统信息结构体指针
 * @return flash_handler_ret_t 
 */
static flash_handler_ret_t __flash_check_value(system_info_t *sys_info)
{
    flash_handler_ret_t ret = FLASH_HANDLER_OK;
    if(NULL == sys_info)
    {
        log_e("sys_info is NULL");
        return FLASH_HANDLER_ERRORRESOURCE;
    }
    /* 校验设备地址是否有效,超出范围，说明之前没有保存过数据 */
    if((sys_info->dev_adr > MAX_DEV_ADR) ||
       (BCAST_ADR == sys_info->dev_adr) ||
       (FLASH_NO_WRITE == sys_info->dev_adr))
    {
        sys_info->dev_adr = DEFAULT_DEV_ADR;
        ret = FLASH_HANDLER_ERRORRESOURCE;
    }
    if(FLASH_NO_WRITE == sys_info->product_date)
    {
        sys_info->product_date = 0;
        ret = FLASH_HANDLER_ERRORRESOURCE;
    }
    if(FLASH_NO_WRITE == sys_info->product_id)
    {
        sys_info->product_id = 0;
        ret = FLASH_HANDLER_ERRORRESOURCE;
    }
    if(0 != strncmp(sys_info->software_version.c_data, 
                    SOFTWARE_VERSION, 
                    sizeof(SOFTWARE_VERSION)))
    {
        strcpy(sys_info->software_version.c_data, SOFTWARE_VERSION);
        ret = FLASH_HANDLER_ERRORRESOURCE;
    }
    if(0 != strncmp(sys_info->hardware_version.c_data, 
                    HARDWARE_VERSION, 
                    sizeof(HARDWARE_VERSION)))
    {
        strcpy(sys_info->hardware_version.c_data, HARDWARE_VERSION);
        ret = FLASH_HANDLER_ERRORRESOURCE;
    }
    if(FLASH_NO_WRITE == sys_info->calibration_data.u32_data)
    {
        sys_info->calibration_data.f_data = DEFAULT_CALIBRATION_DATA;
        ret = FLASH_HANDLER_ERRORRESOURCE;
    }
    if(FLASH_NO_WRITE == sys_info->crc32_data)
    {
        sys_info->crc32_data = 0;
        /* 计算crc32数据 */
        sys_info->crc32_data = crc32(sys_info, sizeof(system_info_t) - sizeof(uint32_t));
        ret = FLASH_HANDLER_ERRORRESOURCE;
    }
    else
    {
        /* 校验crc32数据是否有效 */
        uint32_t crc32_data = crc32(sys_info, sizeof(system_info_t) - sizeof(uint32_t));
        if(crc32_data != sys_info->crc32_data) 
        {
            sys_info->crc32_data = crc32_data;
            log_e("crc32_data is error");
            ret = FLASH_HANDLER_ERRORRESOURCE; 
        } 
    }
    return ret;
}

/**
 * @brief 处理保存设备地址事件
 * 
 * @param p_flash_store Flash存储接口
 * @param pevent 事件
 * @return flash_handler_ret_t 处理结果
 */
static flash_handler_ret_t handle_save_sys_info(void *p_flash_store, 
                                                flash_event_t *pevent)
{
    log_i("handle_save_dev_adr start");
    /* 1. 检查输入参数 */
    if(NULL == p_flash_store || NULL == pevent)
    {
        log_e("handle_save_dev_adr input param error,file:%s,line:%d",
              __FILE__, __LINE__);
        return FLASH_HANDLER_ERRORRESOURCE;
    }
    /* 2. 获取系统信息 */
    system_info_t sys_info;
    internal_flash_store_t* p_store = (internal_flash_store_t*)p_flash_store;
    /* 3. 读取保存在flash中的数据 */
    p_store->internal_flash_read(p_store, 
                                 WRITE_START_ADDR, 
                                 &sys_info, 
                                 sizeof(system_info_t));
    
    /* 校验数据是否有效 */
    __flash_check_value(&sys_info);
    switch (pevent->type)
    {
        case SAVE_DEV_ADR_EVENT:
        {
            /* 保存新的设备地址 */
            sys_info.dev_adr = pevent->dev_adr;
        }break;
        case SAVE_PRODUCT_DATE_EVENT:
        {
            /* 保存新的产品日期 */
            sys_info.product_date = pevent->product_date;
        }break;
        case SAVE_PRODUCT_ID_EVENT:
        {
            /* 保存新的产品ID */
            sys_info.product_id = pevent->product_id;
        }break;
        case SAVE_CALIBRATION_DATA_EVENT:
        {
            /* 保存新的校准数据 */
            sys_info.calibration_data.f_data = pevent->calibration_data;
        }break;
        default:
        {
            log_e("handle_save_dev_adr event type error,file:%s,line:%d",
                  __FILE__, __LINE__);
            return FLASH_HANDLER_ERRORRESOURCE;
        }break;
    }
    
    /* 计算crc32数据 */
    sys_info.crc32_data = crc32(&sys_info, 
                                sizeof(system_info_t) - sizeof(uint32_t));
    p_store->internal_flash_write(p_store, 
                                  WRITE_START_ADDR, 
                                  &sys_info, 
                                sizeof(system_info_t));
    log_i("handle_save_dev_adr end");
    return FLASH_HANDLER_OK;
}

/**
 * @brief 处理读取系统信息
 * 
 * @param p_flash_store Flash存储接口
 * @param pevent 事件
 * @return flash_handler_ret_t 处理结果
 */
static flash_handler_ret_t handle_read_sys_info(void *p_flash_store, flash_event_t *pevent)
{
    log_i("handle_read_reg_value start");
    system_info_t sys_info;
    internal_flash_store_t* p_store = (internal_flash_store_t*)p_flash_store;
    /* 1. 检查输入参数 */
    if(NULL == p_flash_store || NULL == pevent)
    {
        log_e("handle_read_reg_value input param error,file:%s,line:%d",
              __FILE__, __LINE__);
        return FLASH_HANDLER_ERRORRESOURCE;
    }
    /* 2. 读取保存在flash中的数据 */
    p_store->internal_flash_read(p_store, 
                                 WRITE_START_ADDR, 
                                 pevent->p_sys_info, 
                                 sizeof(system_info_t));
    /* 校验数据是否有效 */
    if(FLASH_HANDLER_OK != __flash_check_value(pevent->p_sys_info))
    {
        /* 3.写入数据到flash上 */    
        p_store->internal_flash_write(p_store, 
                                    WRITE_START_ADDR, 
                                    pevent->p_sys_info, 
                                   sizeof(system_info_t));
    }
    log_i("handle_read_reg_value end");
    return FLASH_HANDLER_OK;
}

/**
 * @brief 注册所有Flash事件处理函数
 * 
 * @return flash_handler_ret_t 注册结果
 */
flash_handler_ret_t flash_event_register_cb_init(void)
{
    log_i("flash_event_register_cb_init start");
    
    /* 注册保存设备地址事件处理函数 */
    if(FLASH_HANDLER_OK != flash_register_handler(SAVE_DEV_ADR_EVENT, 
                                                    handle_save_sys_info))
    {
        log_e("flash_event_register_cb_init register SAVE_DEV_ADR_EVENT\
                        handler error,file:%s,line:%d", __FILE__, __LINE__);
        return FLASH_HANDLER_ERROR;
    }
    if(FLASH_HANDLER_OK != flash_register_handler(SAVE_PRODUCT_DATE_EVENT, 
                                                    handle_save_sys_info))

    {
        log_e("flash_event_register_cb_init register SAVE_PRODUCT_DATE_EVENT \
                        handler error,file:%s,line:%d", __FILE__, __LINE__);    
        return FLASH_HANDLER_ERROR;
    }
    
    /* 注册保存产品ID事件处理函数 */
    if(FLASH_HANDLER_OK != flash_register_handler(SAVE_PRODUCT_ID_EVENT, 
                                                    handle_save_sys_info))
    {
        log_e("flash_event_register_cb_init register SAVE_PRODUCT_ID_EVENT\
                        handler error,file:%s,line:%d", __FILE__, __LINE__);    
        return FLASH_HANDLER_ERROR;
    }
    
    /* 注册保存校准数据事件处理函数 */
    if(FLASH_HANDLER_OK != flash_register_handler(SAVE_CALIBRATION_DATA_EVENT, 
                                                    handle_save_sys_info))
    {
        log_e("flash_event_register_cb_init register SAVE_CALIBRATION_DATA_EVENT \
                        handler error,file:%s,line:%d", __FILE__, __LINE__);    
        return FLASH_HANDLER_ERROR;
    }
    
    /* 注册读取寄存器值事件处理函数 */
    if(FLASH_HANDLER_OK != flash_register_handler(READ_SYSTEM_INFO_EVENT,
                                                    handle_read_sys_info ))
    {
        log_e("flash_event_register_cb_init register READ_SYSTEM_INFO_EVENT \
                            handler error,file:%s,line:%d", __FILE__, __LINE__);
    }
    log_i("flash_event_register_cb_init end");
    return FLASH_HANDLER_OK;
}
//******************************** Functions *********************************// 