#ifndef __UART_DEV_H
#define __UART_DEV_H

#include "mcu_uart_driver.h"
#include <linked_list.h>
#include <stdint.h>


// 定义一个结构体Uart_dev，其中包含以下成员：
typedef struct Uart_dev{
	// Uart_dev结构体中的名称，用于标识Uart_dev结构体
	char *name;
	// Uart_dev结构体中的Uart_Init函数，用于初始化Uart_dev结构体
	int (*uart_init)(struct Uart_dev *puart_dev);
	// Uart_dev结构体中的Uart_Write函数，用于向Uart_dev结构体写入数据
	int (*uart_send)(struct Uart_dev* puart_dev, uint8_t *datas, int len, uint32_t timeout_ms);
	// Uart_dev结构体中的Uart_Read函数，用于从Uart_dev结构体中读取数据
	int (*uart_receive)(struct Uart_dev* puart_dev, uint8_t *data, int timeout_ms);
	// Uart_dev结构体中的私有数据
	void *private_data;
	/*下一个LED设备地址*/
	Node p_UARTDev;
}Uart_dev,*PUart_dev;

/**
 * 
 串口设备使用步骤
1. 定义一个Uart_dev结构体变量
2. 调用ADDUARTDEVICE函数，将Uart_dev结构体变量添加到UART设备链表中
3. 调用get_uart_dev函数，获取UART设备链表中的Uart_dev结构体变量
**/
void ADDUARTDEVICE(void);
PUart_dev get_uart_dev(char* name);

/**
 * @brief 调试函数：检查串口状态
 *
 * @param name 串口设备名称
 */
void uart_debug_status(char* name);

#endif
