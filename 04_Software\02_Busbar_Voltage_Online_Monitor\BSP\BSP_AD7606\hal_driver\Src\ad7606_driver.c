/******************************************************************************
 * @file ad7606_driver.c
 * @brief 
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-16
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 
 * 1.ad7606驱动实例化所需接口文件以及构造bsp_ad7606_driver_t驱动实例
 * 
 * @par dependencies
 * ad7606_driver.h
 * elog.h
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/

//******************************** Includes *********************************//
#include "ad7606_driver.h"

//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define SPI_READ_DATA_TIMEOUT   1   //SPI读取数据超时时间
#define GPIO_PIN_SET(PORT, LEVEL)  psdata->pgpio_interface[PORT].pfgpio_write_pin(psdata->pgpio_interface[PORT].pgpio_handler, LEVEL)    //设置GPIO引脚电平
//******************************** Defines **********************************//
//******************************** Variables ********************************//
//AD7606对象实例链表，方便后续多个AD7606使用管理
static  PNode gp_ad7606_private_data_list = NULL;

//AD7606私有数据结构体
typedef struct ad7606_private_data_t
{
    // GPIO接口
    gpio_interface_t               *pgpio_interface;
    // SPI接口
    spi_interface_t                *pspi_interface;
    // 动态分配接口
    ad7606_dynamic_allocation_t    *pdynamic_allocation;
    // busy中断回调接口
    ad7606_busy_irq_callback_t     *p_busy_irq_callback;
    // 采集完成通知回调接口
    pf_notify_collect_finish_fun_t pfnotify_collect_finish;
    // 获取时间戳接口
    pf_get_timestamp_fun_t         pf_get_timestamp;
    // 环形缓冲区
    ring_buff_t                    *pring_buff;
}ad7606_private_data_t;

//******************************** Variables ********************************//

//******************************** Functions ********************************//
static int8_t ad7606_busy_irq_callback_fun(void *p_arg);
static int8_t ad7606_spi_rx_dma_callback_fun(void *p_arg);
/******************************************************************************
 * @brief AD7606初始化函数
 * 
 * @param  pbsp_ad7606_driver AD7606驱动实例
 * 
 * 
 * @return ad7606_driver_ret_code_t 
 * @retval AD7606_OK    初始化成功
 * @retval AD7606_ERROR 初始化失败
 *****************************************************************************/
static ad7606_driver_ret_code_t ad7606_init(bsp_ad7606_driver_t* pbsp_ad7606_driver)
{
    if( NULL == pbsp_ad7606_driver)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver init error, driver instance is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    ad7606_private_data_t* psdata = (ad7606_private_data_t*)pbsp_ad7606_driver->private_data;
    if(NULL == psdata)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver init error, private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    //初始化gpio接口
    for(int i = 0; i < AD7606_GPIO_QUANTITY ; i++)
    {
        if(0 != psdata->pgpio_interface[i].pfgpio_init(&psdata->pgpio_interface[i].pgpio_handler))
        {
            AD7606_DRIVER_LOG_ERROR("ad7606 driver init error, gpio init error gpio port:%d, file:%s,line:%d", i, __FILE__,__LINE__);
            return AD7606_ERROR;
        }
    }
    //初始化spi接口
    psdata->pspi_interface->pfspi_dev_init(psdata->pspi_interface->pspi_handler);
    //注册dma回调函数
    psdata->pspi_interface->pfspi_dev_register_callback(psdata->pspi_interface->pspi_handler,
                                                        NULL,
                                                        ad7606_spi_rx_dma_callback_fun,
                                                        pbsp_ad7606_driver);
    //注册busy中断回调函数
    (*psdata->p_busy_irq_callback->pf_busy_callback_fun) = ad7606_busy_irq_callback_fun;
    (*psdata->p_busy_irq_callback->p_arg)                = pbsp_ad7606_driver;
    //初始化环形缓冲区
    psdata->pring_buff->pfring_buff_init(psdata->pring_buff);
    //拉高conv引脚
//	GPIO_PIN_SET(AD7606_CONV_GPIO,GPIO_HIGH_LEVEL);
    //设置没有过采样
    pbsp_ad7606_driver->pfset_sampling_mode(pbsp_ad7606_driver, SAMPLING_32);
    //复位芯片
    pbsp_ad7606_driver->pfreset(pbsp_ad7606_driver);
    //设置采样范围为5V
    pbsp_ad7606_driver->pfset_sampling_range(pbsp_ad7606_driver, SAMPLING_RANGE_10V);
    //启动采集
//    pbsp_ad7606_driver->pfstartconvst(pbsp_ad7606_driver);
    return AD7606_OK;
}

/******************************************************************************
 * @brief AD7606失能函数
 * 
 * @param  pbsp_ad7606_driver AD7606驱动实例
 * 
 * @return ad7606_driver_ret_code_t 
 * @retval AD7606_OK    失能成功
 * @retval AD7606_ERROR 失能失败
 *****************************************************************************/
static ad7606_driver_ret_code_t ad7606_deinit(bsp_ad7606_driver_t* pbsp_ad7606_driver)
{
    if( NULL == pbsp_ad7606_driver)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver deinit error, driver instance is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    ad7606_private_data_t* psdata = (ad7606_private_data_t*)pbsp_ad7606_driver->private_data;
    if(NULL == psdata)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver deinit error, private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    //失能gpio接口
    for(int i = 0; i < AD7606_GPIO_QUANTITY ; i++)
    {
        psdata->pgpio_interface[i].pfgpio_deinit(&psdata->pgpio_interface[i].pgpio_handler);
    }
    //失能spi接口
    psdata->pspi_interface->pfspi_dev_deinit(psdata->pspi_interface->pspi_handler);
    //释放内存
    psdata->pdynamic_allocation->pffree(psdata);
    return AD7606_OK;
}

/******************************************************************************
 * @brief AD7606复位函数
 * 
 * @param  pbsp_ad7606_driver   AD7606驱动实例
 * 
 * @return ad7606_driver_ret_code_t 
 * @retval AD7606_OK    复位成功
 * @retval AD7606_ERROR 复位失败
 *****************************************************************************/
static ad7606_driver_ret_code_t ad7606_reset(bsp_ad7606_driver_t* pbsp_ad7606_driver)
{
    if( NULL == pbsp_ad7606_driver)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver reset error, driver instance is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }

     // 获取私有数据
    ad7606_private_data_t* psdata = (ad7606_private_data_t*)pbsp_ad7606_driver->private_data;
    if(NULL == psdata)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver reset error, private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    // AD7606的RESET引脚产生上升沿的高电平持续50ns
    GPIO_PIN_SET(AD7606_RESET_GPIO, GPIO_LOW_LEVEL);
    GPIO_PIN_SET(AD7606_RESET_GPIO, GPIO_HIGH_LEVEL);
    GPIO_PIN_SET(AD7606_RESET_GPIO, GPIO_HIGH_LEVEL);
    GPIO_PIN_SET(AD7606_RESET_GPIO, GPIO_HIGH_LEVEL);
    GPIO_PIN_SET(AD7606_RESET_GPIO, GPIO_HIGH_LEVEL);
    GPIO_PIN_SET(AD7606_RESET_GPIO, GPIO_HIGH_LEVEL);
    GPIO_PIN_SET(AD7606_RESET_GPIO, GPIO_LOW_LEVEL);
    return AD7606_OK;
}

/******************************************************************************
 * @brief AD7606启动转换
 * 
 * @param  pbsp_ad7606_driver AD7606驱动实例
 * 
 * @return ad7606_driver_ret_code_t 
 * @retval AD7606_OK    启动成功
 * @retval AD7606_ERROR 启动失败
 *****************************************************************************/
static ad7606_driver_ret_code_t ad7606_startconvst(bsp_ad7606_driver_t* pbsp_ad7606_driver)
{
    if (NULL == pbsp_ad7606_driver)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver startconvst error, driver instance is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    ad7606_private_data_t* psdata = (ad7606_private_data_t*)pbsp_ad7606_driver->private_data;
    if(NULL == psdata)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver startconvst error, private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    GPIO_PIN_SET(AD7606_CONV_GPIO, GPIO_HIGH_LEVEL);
	GPIO_PIN_SET(AD7606_CONV_GPIO, GPIO_LOW_LEVEL);
    GPIO_PIN_SET(AD7606_CONV_GPIO, GPIO_LOW_LEVEL);
	GPIO_PIN_SET(AD7606_CONV_GPIO, GPIO_LOW_LEVEL);
	GPIO_PIN_SET(AD7606_CONV_GPIO, GPIO_LOW_LEVEL);
    GPIO_PIN_SET(AD7606_CONV_GPIO, GPIO_HIGH_LEVEL);
    return AD7606_OK;
}

/******************************************************************************
 * @brief 读取AD7606转换完成标志
 * 
 * @param  pbsp_ad7606_driver AD7606驱动实例
 * 
 * @return gpio_interface_pinstate_t 
 *****************************************************************************/
static uint8_t ad7606_read_busy(bsp_ad7606_driver_t* pbsp_ad7606_driver)
{
    if (NULL == pbsp_ad7606_driver)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver read_busy error, driver instance is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return GPIO_ERROR_LEVEL;
    }
    ad7606_private_data_t* psdata = (ad7606_private_data_t*)pbsp_ad7606_driver->private_data;
    if(NULL == psdata)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver read_busy error, private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return GPIO_ERROR_LEVEL;
    }

    return (gpio_interface_pinstate_t)psdata->pgpio_interface[AD7606_BUSY_GPIO].pfgpio_read_pin(&psdata->pgpio_interface[AD7606_BUSY_GPIO]);
}

/******************************************************************************
 * @brief 读取AD7606的通道数据
 * 
 * @param  pbsp_ad7606_driver AD7606驱动实例
 * @param  pdata 读取的数据           
 * @param  start_channel  起始通道:范围0~7
 * @param  channels   
 * 
 * @return ad7606_driver_ret_code_t 
 * @retval AD7606_OK    读取成功
 * @retval AD7606_ERROR 读取失败
 * @retval AD7606_ERRORPARAMETER 输入的通道数错误
 *****************************************************************************/
static ad7606_driver_ret_code_t ad7606_read_data(   
                                                    bsp_ad7606_driver_t* pbsp_ad7606_driver, 
                                                    int16_t* pdata, 
                                                    ad7606_channels_t start_channel,
                                                    uint8_t channels
                                                    )
{
    int8_t ret = 0;
    if ((NULL == pbsp_ad7606_driver)
     || (NULL == pdata))
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver read_data error, driver instance is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    ad7606_private_data_t* psdata = (ad7606_private_data_t*)pbsp_ad7606_driver->private_data;
    if(NULL == psdata)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver read_data error, private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    if( (start_channel >= AD7606_CHANNEL_QUANTITY)                ||
        ((start_channel + channels - 1) > AD7606_CHANNEL_QUANTITY)
      )
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver read_data error, start_channel:%d channels:%d,\
                file:%s,line:%d",start_channel, channels, __FILE__,__LINE__);
        return AD7606_ERRORPARAMETER;
    }
    uint8_t data[AD7606_CHANNEL_QUANTITY * 2] = {0};
//    //启动转换
//    pbsp_ad7606_driver->pfstartconvst(pbsp_ad7606_driver);
    //读取数据
    ret = psdata->pspi_interface->pfspi_dev_read( psdata->pspi_interface->pspi_handler, 
                                                  data,
                                                  AD7606_CHANNEL_QUANTITY*2, 
                                                  SPI_READ_DATA_TIMEOUT);
    if(ret)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver read_data error, spi read data failed,ret:[%d], file:%s,line:%d", ret, __FILE__,__LINE__);
        return AD7606_ERROR;
    }
    uint8_t index = 0;
    for(uint8_t i = start_channel; i < channels; i++)
    {
        pdata[index++] = (int16_t)((data[i*2] << 8) | data[i*2+1]);
    }
//    memcpy(pdata, (int16_t*)&data[start_channel], channels*sizeof(int16_t));
    return AD7606_OK;
}

/******************************************************************************
 * @brief 设置AD7606的采样模式
 * 
 * @param  pbsp_ad7606_driver AD7606驱动实例
 * @param  sampling_mode 采样模式
 * 
 * @return ad7606_driver_ret_code_t 
 * @retval AD7606_OK    设置成功
 * @retval AD7606_ERROR 设置失败
 * @retval AD7606_ERRORPARAMETER 输入的采样模式错误
 *****************************************************************************/
static ad7606_driver_ret_code_t ad7606_set_sampling_mode( struct bsp_ad7606_driver_t* pbsp_ad7606_driver, 
                                            ad7606_sampling_mode_t  sampling_mode)
{
    if(NULL == pbsp_ad7606_driver)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver set_sampling_mode error, driver instance is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    ad7606_private_data_t* psdata = (ad7606_private_data_t*)pbsp_ad7606_driver->private_data;
    if(NULL == psdata)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver set_sampling_mode error, private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    //根据输入的采样模式设置OS2、OS1、OS0引脚
    switch(sampling_mode)
    {
        case SAMPLING_NONE:
        {
            GPIO_PIN_SET(AD7606_OS2_GPIO, GPIO_LOW_LEVEL);
            GPIO_PIN_SET(AD7606_OS1_GPIO, GPIO_LOW_LEVEL);
            GPIO_PIN_SET(AD7606_OS0_GPIO, GPIO_LOW_LEVEL);
        }break;
        case SAMPLING_2:
        {
            GPIO_PIN_SET(AD7606_OS2_GPIO, GPIO_LOW_LEVEL);
            GPIO_PIN_SET(AD7606_OS1_GPIO, GPIO_LOW_LEVEL);
            GPIO_PIN_SET(AD7606_OS0_GPIO, GPIO_HIGH_LEVEL);
        }break;
        case SAMPLING_4:
        {
            GPIO_PIN_SET(AD7606_OS2_GPIO, GPIO_LOW_LEVEL);
            GPIO_PIN_SET(AD7606_OS1_GPIO, GPIO_HIGH_LEVEL);
            GPIO_PIN_SET(AD7606_OS0_GPIO, GPIO_LOW_LEVEL);
        }break;
        case SAMPLING_8:
        {
            GPIO_PIN_SET(AD7606_OS2_GPIO, GPIO_LOW_LEVEL);
            GPIO_PIN_SET(AD7606_OS1_GPIO, GPIO_HIGH_LEVEL);
            GPIO_PIN_SET(AD7606_OS0_GPIO, GPIO_HIGH_LEVEL);
        }break;
        case SAMPLING_16:
        {
            GPIO_PIN_SET(AD7606_OS2_GPIO, GPIO_HIGH_LEVEL);
            GPIO_PIN_SET(AD7606_OS1_GPIO, GPIO_LOW_LEVEL);
            GPIO_PIN_SET(AD7606_OS0_GPIO, GPIO_LOW_LEVEL);
        }break;
        case SAMPLING_32:
        {
            GPIO_PIN_SET(AD7606_OS2_GPIO, GPIO_HIGH_LEVEL);
            GPIO_PIN_SET(AD7606_OS1_GPIO, GPIO_LOW_LEVEL);
            GPIO_PIN_SET(AD7606_OS0_GPIO, GPIO_HIGH_LEVEL);
        }break;
        case SAMPLING_64:
        {
            GPIO_PIN_SET(AD7606_OS2_GPIO, GPIO_HIGH_LEVEL);
            GPIO_PIN_SET(AD7606_OS1_GPIO, GPIO_HIGH_LEVEL);
            GPIO_PIN_SET(AD7606_OS0_GPIO, GPIO_LOW_LEVEL);
        }break;
        default:
        {
            AD7606_DRIVER_LOG_ERROR("ad7606 driver set_sampling_mode error, sampling_mode is invalid:%d,file:%s,line:%d", sampling_mode, __FILE__,__LINE__);
            return AD7606_ERRORPARAMETER;
        }
    }
    return AD7606_OK;
}

/******************************************************************************
 * @brief 设置AD7606的采样范围
 * 
 * @param  pbsp_ad7606_driver AD7606驱动实例
 * @param  range 采样范围 0~5V或0~10V           
 * 
 * @return ad7606_driver_ret_code_t 
 * @retval AD7606_OK    设置成功
 * @retval AD7606_ERROR 设置失败
 * @retval AD7606_ERRORPARAMETER 输入的采样范围错误
 *****************************************************************************/
static ad7606_driver_ret_code_t ad7606_set_sampling_range(bsp_ad7606_driver_t* pbsp_ad7606_driver, ad7606_sampling_range_t range)
{
    if(NULL == pbsp_ad7606_driver)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver set_sampling_range error, driver instance is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    ad7606_private_data_t* psdata = (ad7606_private_data_t*)pbsp_ad7606_driver->private_data;
    if(NULL == psdata)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver set_sampling_range error, private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    switch(range)
    {
        case SAMPLING_RANGE_5V:
        {
            GPIO_PIN_SET(AD7606_RANGE_GPIO, GPIO_LOW_LEVEL);
        }break;
        case SAMPLING_RANGE_10V:
        {
            GPIO_PIN_SET(AD7606_RANGE_GPIO, GPIO_HIGH_LEVEL);
        }break;
        default:
        {
            AD7606_DRIVER_LOG_ERROR("ad7606 driver set_sampling_range error, range is invalid:%d,file:%s,line:%d", range, __FILE__,__LINE__);
            return AD7606_ERRORPARAMETER;
        }
    }
    return AD7606_OK;
}

/******************************************************************************
 * @brief 内部函数，找到AD7606驱动实例
 * 
 * @param  name AD7606驱动实例名称            
 * 
 * @return bsp_ad7606_driver_t* 
 * @retval NULL 未找到
 *****************************************************************************/
static bsp_ad7606_driver_t* __find_ad7606_driver(char const *name)
{
    PNode ptemp = gp_ad7606_private_data_list;
    bsp_ad7606_driver_t* pdata;
    while(ptemp)
    {
       pdata = container_of(ptemp, bsp_ad7606_driver_t, ad7606_node);
       if(0 == strcmp(name, pdata->name))
       {
            return pdata;
       }
       ptemp = ptemp->pNext;
    }
    return NULL;
}

/******************************************************************************
 * @brief ad7606中断回调函数
 * 
 * @param  param AD7606实例的名称          
 * 
 * @return ad7606_driver_ret_code_t 
 * @retval AD7606_ERROR 回调函数执行失败
 * @retval AD7606_OK 回调函数执行成功
 *****************************************************************************/
static ad7606_driver_ret_code_t bsp_ad7606_irq_callback_fun(void *param)
{
    if( NULL == param)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver callback_fun error,input param is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    bsp_ad7606_driver_t *pbsp_driver = __find_ad7606_driver((char*)param);
    if(NULL == pbsp_driver)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver callback_fun error, driver is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    ad7606_private_data_t *psdata = (ad7606_private_data_t*)pbsp_driver->private_data;
    if(NULL == psdata)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver callback_fun error, private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return AD7606_ERROR;
    }
    //执行对应操作
    
    return AD7606_OK;
} 
/******************************************************************************
 * @brief 实例化构造AD7606驱动对象
 * 
 * @param  pgpio_interface gpio接口实例
 * @param  pspi_interface   spi接口实例
 * @param  prtos_interface  rtos接口实例
 * @param  pinterrupt_interface 中断接口实例
 * @param  pdynamic_allocation  动态内存分配接口实例
 * @param  pbsp_ad7606_driver     AD7606驱动实例
 * 
 * @return ad7606_driver_ret_code_t 
 * @retval AD7606_OK    实例化成功
 * @retval AD7606_ERROR 实例化失败
 *****************************************************************************/
ad7606_driver_ret_code_t bsp_ad7606_driver_instance(
                                                    char                  *name,
                                            ad7606_driver_input_t * const pinput,
                                                  ring_buff_t * const pring_buff,
                    pf_notify_collect_finish_fun_t const pfnotify_collect_finish,
                                 bsp_ad7606_driver_t   * const pbsp_ad7606_driver
)
{
    AD7606_DRIVER_LOG_DEUBG("ad7606 driver instance start");
    /* 0.判断输入的参数是否为空*/
    if( 
        (NULL == pinput)
     || (NULL == pbsp_ad7606_driver)
     || (NULL == pring_buff))
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver instance error, input parameters are NULL, \
                                        file:%s, line:%d", __FILE__, __LINE__);
        return AD7606_ERROR;
    }
    /* 2.输入参数内容检查*/
    /* 2.1 spi接口检查 */
    if(
       (NULL == pinput->pspi_interface)                     ||
       (NULL == pinput->pspi_interface->pspi_handler)       ||
       (NULL == pinput->pspi_interface->pfspi_dev_init)     ||
       (NULL == pinput->pspi_interface->pfspi_dev_deinit)   ||
       (NULL == pinput->pspi_interface->pfspi_dev_read)     ||
       (NULL == pinput->pspi_interface->pfspi_dev_read_dma) ||
       (NULL == pinput->pspi_interface->pfspi_dev_write)    ||
       (NULL == pinput->pspi_interface->pfspi_dev_register_callback)
    )
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver instance error, spi interface is invalid,\
                                         file:%s, line:%d", __FILE__, __LINE__);
        return AD7606_ERROR;
    }
    /* 2.2 gpio接口检查 */
    for (uint8_t i = 0; i < AD7606_GPIO_QUANTITY; i++)
    {
        if(
           (NULL == pinput->pgpio_interface)                     ||
           (NULL == pinput->pgpio_interface[i].pgpio_handler)   ||
           (NULL == pinput->pgpio_interface[i].pfgpio_init)        ||
           (NULL == pinput->pgpio_interface[i].pfgpio_deinit)      ||
           (NULL == pinput->pgpio_interface[i].pfgpio_read_pin) ||
           (NULL == pinput->pgpio_interface[i].pfgpio_write_pin)
        )
        {
            AD7606_DRIVER_LOG_ERROR("ad7606 driver instance error, gpio interface is invalid,\
                                             file:%s, line:%d", __FILE__, __LINE__);
            return AD7606_ERROR;
        }
    }
    /* 2.3 动态内存分配接口检查 */
    if(
       (NULL == pinput->pdynamic_allocation)                     ||
       (NULL == pinput->pdynamic_allocation->pfmalloc)           ||
       (NULL == pinput->pdynamic_allocation->pffree)             
    )
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver instance error, dynamic allocation interface is invalid,\
                                             file:%s, line:%d", __FILE__, __LINE__);
        return AD7606_ERROR;
    }
    /* 2.4 采集完成通知回调函数检查 */
    if((NULL == pfnotify_collect_finish))
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver instance error, interrupt interface is\
                                        NULL, file:%s, line:%d", __FILE__, __LINE__);
        return AD7606_ERROR;
    }
    /* 2.5 busy中断回调函数检查 */
    if(
     (NULL    == pinput->pbusy_irq_callback)                       ||
     (NULL    == pinput->pbusy_irq_callback->pf_busy_callback_fun) ||
     (NULL    == pinput->pbusy_irq_callback->p_arg)
     )
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver instance error, busy interrupt interface is\
                                        NULL, file:%s, line:%d", __FILE__, __LINE__);
        return AD7606_ERROR;
    }
    //分配私有数据并初始化
    ad7606_private_data_t *psdata = (ad7606_private_data_t*)pinput->\
                            pdynamic_allocation->pfmalloc(sizeof(ad7606_private_data_t));
    if(NULL == psdata)
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver instance error, failed to allocate private data, \
                                                file:%s, line:%d", __FILE__, __LINE__);
        return AD7606_ERROR;
    }
    psdata->pring_buff              = pring_buff;
    psdata->pgpio_interface         = pinput->pgpio_interface;
    psdata->pspi_interface          = pinput->pspi_interface;
    psdata->pdynamic_allocation     = pinput->pdynamic_allocation;
    psdata->p_busy_irq_callback     = pinput->pbusy_irq_callback;
    psdata->pfnotify_collect_finish = pfnotify_collect_finish;
#ifdef AD7606_DRIVER_USE_TIMESTAMP
    psdata->pf_get_timestamp          = pinput->pf_get_timestamp;
#endif
    if(
        (NULL == psdata->pgpio_interface)         ||
        (NULL == psdata->pspi_interface)          ||
        (NULL == psdata->pdynamic_allocation)     ||
        (NULL == psdata->p_busy_irq_callback)     ||
        (NULL == psdata->pfnotify_collect_finish) ||
#ifdef  AD7606_DRIVER_USE_TIMESTAMP
        (NULL == psdata->pf_get_timestamp)          ||
#endif
        (NULL == psdata->pring_buff)           
    )
    {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver instance error, private data is invalid,\
                                             file:%s, line:%d", __FILE__, __LINE__);
        return AD7606_ERROR;
    }
    //ad7606对象实例化
    pbsp_ad7606_driver->name                 = name;
    pbsp_ad7606_driver->private_data         = psdata;
    pbsp_ad7606_driver->pfinit               = ad7606_init;
    pbsp_ad7606_driver->pfdeinit             = ad7606_deinit;
    pbsp_ad7606_driver->pfstartconvst        = ad7606_startconvst;
    pbsp_ad7606_driver->pfread_busy          = ad7606_read_busy;
    pbsp_ad7606_driver->pfreset              = ad7606_reset;
    pbsp_ad7606_driver->pfread_data          = ad7606_read_data;
    pbsp_ad7606_driver->pfset_sampling_mode  = ad7606_set_sampling_mode;
    pbsp_ad7606_driver->pfset_sampling_range = ad7606_set_sampling_range;
    if(
     (NULL == pbsp_ad7606_driver->pfinit)               ||
     (NULL == pbsp_ad7606_driver->pfdeinit)             ||
     (NULL == pbsp_ad7606_driver->pfstartconvst)        ||
     (NULL == pbsp_ad7606_driver->pfread_busy)          ||
     (NULL == pbsp_ad7606_driver->pfreset)              ||
     (NULL == pbsp_ad7606_driver->pfread_data)          ||
     (NULL == pbsp_ad7606_driver->pfset_sampling_mode)  ||
     (NULL == pbsp_ad7606_driver->pfset_sampling_range) ||
     (NULL == pbsp_ad7606_driver->private_data)
     )
     {
        AD7606_DRIVER_LOG_ERROR("ad7606 driver instance error,ad7606 instance function \
                        pointers are invalid, file:%s, line:%d", __FILE__, __LINE__);
        return AD7606_ERROR;
     }
    ADDLinked_List(&gp_ad7606_private_data_list, &pbsp_ad7606_driver->ad7606_node);
    return AD7606_OK;
}
/******************************************************************************
 * @brief busy中断回调函数
 * 
 * @param  p_arg 输入参数          
 * 
 * @return int8_t 
 * @retval 0 成功
 * @retval 1 失败
 *****************************************************************************/
static int8_t ad7606_busy_irq_callback_fun(void *p_arg)
{
//	static uint32_t busy_count = 0;
    uint32_t *ringbuffer_address;
    /* 0. 检查传入的参数是否为空 */
    if(NULL == p_arg)
    {
        AD7606_DRIVER_LOG_IRQ("ad7606 driver busy irq callback fun error, input param is NULL, \
                                        file:%s, line:%d", __FILE__, __LINE__);
        return 1;
    }
    bsp_ad7606_driver_t *pbsp_driver = (bsp_ad7606_driver_t*)p_arg;

    /* 1.数据准备就绪，开启DMA采集 */
    ad7606_private_data_t *psdata = (ad7606_private_data_t*)pbsp_driver->private_data;
    if(NULL == psdata)
    {
        AD7606_DRIVER_LOG_IRQ("ad7606 driver busy irq callback fun error, private data is NULL, \
                                        file:%s, line:%d", __FILE__, __LINE__);
        return 1;
    }
    /* 1.1 获取ringbuffer地址 */
    if(RING_BUFF_OK != psdata->pring_buff->pfget_buff_adress(psdata->pring_buff,
                                          (void**)&ringbuffer_address)
       )
    {
        AD7606_DRIVER_LOG_IRQ("ad7606 driver busy irq callback fun error, failed to get ringbuffer address, \
                                        file:%s, line:%d", __FILE__, __LINE__);
        return 1;
    }
    /* 1.2 开启DMA采集 */
    if(0 != psdata->pspi_interface->pfspi_dev_read_dma( 
                                                                psdata->pspi_interface->pspi_handler, 
                                                                        (uint8_t*)ringbuffer_address, 
#ifdef AD7606_DRIVER_USE_TIMESTAMP
                psdata->pring_buff->pfring_buff_get_item_size(psdata->pring_buff) - sizeof(uint32_t)
#else
                                    psdata->pring_buff->pfring_buff_get_item_size(psdata->pring_buff)
#endif
                                                        )
        )
    {
        AD7606_DRIVER_LOG_IRQ("ad7606 driver busy irq callback fun error, failed to start dma read, \
                                        file:%s, line:%d", __FILE__, __LINE__);
        return 1;
    }
//	busy_count++;
    return 0;
}

/******************************************************************************
 * @brief  spi dma回调函数
 * 
 * @param  p_arg 输入参数           
 * 
 * @return int8_t 
 * @retval 0 成功
 * @retval 1 失败
 *****************************************************************************/
static int8_t ad7606_spi_rx_dma_callback_fun(void *p_arg)
{
//	static uint32_t spi_dma_count = 0;
    /* 0. 检查传入的参数是否为空 */
    if(NULL == p_arg)
    {
        AD7606_DRIVER_LOG_IRQ("ad7606 driver spi dma callback fun error, input param is NULL, \
                                        file:%s, line:%d", __FILE__, __LINE__);
        return 1;
    }
    bsp_ad7606_driver_t *pbsp_driver = (bsp_ad7606_driver_t*)p_arg;
    /* 1.数据采集完成，通知上层 */
    ad7606_private_data_t *psdata = (ad7606_private_data_t*)pbsp_driver->private_data;
    if(NULL == psdata)
    {
        AD7606_DRIVER_LOG_IRQ("ad7606 driver spi dma callback fun error, private data is NULL, \
                                        file:%s, line:%d", __FILE__, __LINE__);
        return 1;
    }
#ifdef AD7606_DRIVER_USE_TIMESTAMP
    /* 1.0 时间戳绑定处理 - 在移动写指针之前获取当前采样数据 */
    void* current_write_addr = NULL;
    if(RING_BUFF_OK == psdata->pring_buff->pfget_buff_adress(psdata->pring_buff, &current_write_addr))
    {
        /* 将时间戳与当前的数据绑定 */
        uint32_t timestamp = psdata->pf_get_timestamp(); //获取时间戳信息
        memcpy((void*)(current_write_addr + psdata->pring_buff->pfring_buff_get_item_size(psdata->pring_buff) - sizeof(uint32_t)),
               &timestamp, sizeof(uint32_t)); //将时间戳信息拷贝到ringbuffer中
    }
#endif /* AD7606_DRIVER_USE_TIMESTAMP */

    /* 1.1 移动ringbuffer写指针 */
    if(RING_BUFF_OK != psdata->pring_buff->\
                            pfring_buff_dma_writes(psdata->pring_buff, 1))
    {
        AD7606_DRIVER_LOG_IRQ("ad7606 driver spi dma callback fun error, \
        failed to move ringbuffer write pointer,  file:%s, line:%d",
                                                        __FILE__, __LINE__);
        return 1;
    }
    /* 1.2 通知上层采集完成 */
    if(0 != psdata->pfnotify_collect_finish())
    {
        AD7606_DRIVER_LOG_IRQ("ad7606 driver spi dma callback fun error, failed to notify collect finish,\
                                        file:%s, line:%d", __FILE__, __LINE__);
        return 1;
    }
//	spi_dma_count++;
    return 0;
}