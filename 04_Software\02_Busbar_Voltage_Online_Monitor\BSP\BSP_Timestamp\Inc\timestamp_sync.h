#ifndef __TIMESTAMP_SYNC_H__
#define __TIMESTAMP_SYNC_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

/* 时间戳同步模块返回值定义 */
typedef enum {
    TIMESTAMP_SYNC_OK = 0,
    TIMESTAMP_SYNC_ERROR = -1,
    TIMESTAMP_SYNC_ERROR_PARAM = -2,
    TIMESTAMP_SYNC_ERROR_TIMEOUT = -3
} timestamp_sync_ret_t;

/* 过零检测状态 */
typedef enum {
    ZERO_CROSS_RISING = 0,   /* 上升沿过零 */
    ZERO_CROSS_FALLING = 1   /* 下降沿过零 */
} zero_cross_edge_t;

/* 时间戳数据结构 */
typedef struct {
    uint32_t capture_count;     /* 过零点捕获计数 */
    uint32_t t_zero;           /* 最近一次过零点时间戳 (TIM CNT值) */
    uint32_t t_sample0;        /* 采样帧起始时间戳 (TIM CNT值) */
    uint32_t system_tick;      /* 系统tick时间戳 */
    zero_cross_edge_t edge;    /* 过零边沿类型 */
    bool valid;                /* 时间戳有效标志 */
} timestamp_sync_data_t;
/* AD7606采样数据与时间戳绑定结构 */
typedef struct {
    int16_t adc_data[8];              /* AD7606 8通道采样数据 */
    timestamp_sync_data_t timestamp;   /* 对应的时间戳信息 */
    uint32_t frame_id;                /* 帧ID */
} ad7606_timestamped_data_t;

/* 频率测量结果 */
typedef struct {
    float frequency;           /* 测量频率 (Hz) */
    float period_us;          /* 周期 (微秒) */
    uint32_t period_ticks;    /* 周期 (定时器ticks) */
    bool valid;               /* 测量结果有效标志 */
} frequency_measurement_t;

/* 相位修正参数 */
typedef struct {
    float phase_offset_deg;    /* 相位偏移 (度) */
    float time_offset_us;      /* 时间偏移 (微秒) */
    uint32_t sample_offset;    /* 采样点偏移 */
} phase_correction_t;

/* 时间戳同步模块配置 */
typedef struct {
    uint32_t timer_freq_hz;    /* 定时器频率 (Hz) */
    uint32_t sample_rate_hz;   /* 采样率 (Hz) */
    uint16_t fft_length;       /* FFT长度 */
    float nominal_freq_hz;     /* 标称频率 (Hz) */
} timestamp_sync_config_t;

/* 时间戳同步模块句柄 */
typedef struct {
    timestamp_sync_config_t config;
    timestamp_sync_data_t current_timestamp;
    frequency_measurement_t freq_measurement;
    phase_correction_t phase_correction;
    uint32_t frame_counter;
    bool initialized;
} timestamp_sync_handle_t;

/* 函数声明 */

/**
 * @brief 初始化时间戳同步模块
 * @param handle 时间戳同步句柄
 * @param config 配置参数
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_sync_init(timestamp_sync_handle_t *handle, 
                                        const timestamp_sync_config_t *config);

/**
 * @brief 过零点中断回调函数
 * @param handle 时间戳同步句柄
 * @param capture_value 捕获值
 * @param edge 过零边沿类型
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_sync_zero_cross_callback(timestamp_sync_handle_t *handle,
                                                       uint32_t capture_value,
                                                       zero_cross_edge_t edge);

/**
 * @brief 获取当前时间戳
 * @param handle 时间戳同步句柄
 * @param timestamp 输出时间戳数据
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_sync_get_current_timestamp(timestamp_sync_handle_t *handle,
                                                         timestamp_sync_data_t *timestamp);

/**
 * @brief 为AD7606采样数据绑定时间戳
 * @param handle 时间戳同步句柄
 * @param adc_data AD7606采样数据
 * @param timestamped_data 输出带时间戳的数据
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_sync_bind_adc_data(timestamp_sync_handle_t *handle,
                                                  const int16_t adc_data[8],
                                                  ad7606_timestamped_data_t *timestamped_data);

/**
 * @brief 计算电网频率
 * @param handle 时间戳同步句柄
 * @param measurement 输出频率测量结果
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_sync_calculate_frequency(timestamp_sync_handle_t *handle,
                                                       frequency_measurement_t *measurement);

/**
 * @brief 计算FFT相位修正参数
 * @param handle 时间戳同步句柄
 * @param t_sample0 采样起始时间
 * @param t_zero 过零点时间
 * @param correction 输出相位修正参数
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_sync_calculate_phase_correction(timestamp_sync_handle_t *handle,
                                                              uint32_t t_sample0,
                                                              uint32_t t_zero,
                                                              phase_correction_t *correction);

/**
 * @brief 应用相位修正到FFT结果
 * @param fft_complex_data FFT复数结果数组
 * @param fft_length FFT长度
 * @param correction 相位修正参数
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_sync_apply_phase_correction(float *fft_complex_data,
                                                          uint16_t fft_length,
                                                          const phase_correction_t *correction);

#ifdef __cplusplus
}
#endif

#endif /* __TIMESTAMP_SYNC_H__ */
