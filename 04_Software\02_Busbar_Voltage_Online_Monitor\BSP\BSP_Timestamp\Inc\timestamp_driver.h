#ifndef __TIMESTAMP_DRIVER_H__
#define __TIMESTAMP_DRIVER_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "timestamp_config.h"

/* 时间戳驱动层 - 硬件抽象接口 */

/* 驱动返回值 */
typedef enum {
    TIMESTAMP_DRIVER_OK = 0,
    TIMESTAMP_DRIVER_ERROR = -1,
    TIMESTAMP_DRIVER_ERROR_PARAM = -2,
    TIMESTAMP_DRIVER_ERROR_INIT = -3,
    TIMESTAMP_DRIVER_ERROR_TIMEOUT = -4
} timestamp_driver_ret_t;

/* 定时器通道类型 */
typedef enum {
    TIMESTAMP_DRIVER_TIM_GRID_ZERO = 0,     /* TIM4 - 电网零点捕获 */
    TIMESTAMP_DRIVER_TIM_SIGNAL_FREQ = 1,   /* TIM3 - 信号频率检测 */
    TIMESTAMP_DRIVER_TIM_MAX
} timestamp_driver_tim_type_t;

/* 捕获事件类型 */
typedef enum {
    TIMESTAMP_DRIVER_EVENT_RISING = 0,      /* 上升沿 */
    TIMESTAMP_DRIVER_EVENT_FALLING = 1,     /* 下降沿 */
    TIMESTAMP_DRIVER_EVENT_BOTH = 2         /* 双边沿 */
} timestamp_driver_event_type_t;

/* 捕获数据结构 */
typedef struct {
    uint16_t capture_value;                 /* 捕获值 */
    timestamp_driver_event_type_t event_type; /* 事件类型 */
    uint32_t system_tick;                   /* 系统tick */
    bool valid;                             /* 数据有效标志 */
} timestamp_driver_capture_data_t;

/* 驱动状态结构 */
typedef struct {
    bool initialized;                       /* 初始化标志 */
    bool tim_grid_enabled;                  /* TIM4使能标志 */
    bool tim_signal_enabled;                /* TIM3使能标志 */
    uint32_t grid_capture_count;            /* 电网捕获次数 */
    uint32_t signal_capture_count;          /* 信号捕获次数 */
    uint32_t error_count;                   /* 错误次数 */
} timestamp_driver_status_t;

/* 回调函数类型定义 */
typedef void (*timestamp_driver_callback_t)(timestamp_driver_tim_type_t tim_type, 
                                           const timestamp_driver_capture_data_t *capture_data,
                                           void *user_data);

/* 驱动句柄结构 */
typedef struct {
    timestamp_config_t config;              /* 配置参数 */
    timestamp_driver_status_t status;       /* 驱动状态 */
    timestamp_driver_callback_t callback;   /* 回调函数 */
    void *user_data;                        /* 用户数据 */
    
    /* 硬件相关的私有数据 */
    void *hw_tim3_handle;                   /* TIM3句柄 */
    void *hw_tim4_handle;                   /* TIM4句柄 */
} timestamp_driver_handle_t;

/* 驱动接口函数 */

/**
 * @brief 初始化时间戳驱动
 * @param handle 驱动句柄
 * @param config 配置参数
 * @return timestamp_driver_ret_t 返回值
 */
timestamp_driver_ret_t timestamp_driver_init(timestamp_driver_handle_t *handle, 
                                            const timestamp_config_t *config);

/**
 * @brief 反初始化时间戳驱动
 * @param handle 驱动句柄
 * @return timestamp_driver_ret_t 返回值
 */
timestamp_driver_ret_t timestamp_driver_deinit(timestamp_driver_handle_t *handle);

/**
 * @brief 启动定时器捕获
 * @param handle 驱动句柄
 * @param tim_type 定时器类型
 * @return timestamp_driver_ret_t 返回值
 */
timestamp_driver_ret_t timestamp_driver_start_capture(timestamp_driver_handle_t *handle,
                                                     timestamp_driver_tim_type_t tim_type);

/**
 * @brief 停止定时器捕获
 * @param handle 驱动句柄
 * @param tim_type 定时器类型
 * @return timestamp_driver_ret_t 返回值
 */
timestamp_driver_ret_t timestamp_driver_stop_capture(timestamp_driver_handle_t *handle,
                                                    timestamp_driver_tim_type_t tim_type);

/**
 * @brief 注册回调函数
 * @param handle 驱动句柄
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return timestamp_driver_ret_t 返回值
 */
timestamp_driver_ret_t timestamp_driver_register_callback(timestamp_driver_handle_t *handle,
                                                         timestamp_driver_callback_t callback,
                                                         void *user_data);

/**
 * @brief 获取当前定时器计数值
 * @param handle 驱动句柄
 * @param tim_type 定时器类型
 * @param count_value 输出计数值
 * @return timestamp_driver_ret_t 返回值
 */
timestamp_driver_ret_t timestamp_driver_get_counter(timestamp_driver_handle_t *handle,
                                                   timestamp_driver_tim_type_t tim_type,
                                                   uint16_t *count_value);

/**
 * @brief 获取驱动状态
 * @param handle 驱动句柄
 * @param status 输出状态信息
 * @return timestamp_driver_ret_t 返回值
 */
timestamp_driver_ret_t timestamp_driver_get_status(timestamp_driver_handle_t *handle,
                                                  timestamp_driver_status_t *status);

/**
 * @brief 重置驱动统计信息
 * @param handle 驱动句柄
 * @return timestamp_driver_ret_t 返回值
 */
timestamp_driver_ret_t timestamp_driver_reset_statistics(timestamp_driver_handle_t *handle);

/* 中断处理函数 - 由具体硬件平台实现 */

/**
 * @brief TIM3输入捕获中断处理函数
 * @param handle 驱动句柄
 * @param capture_value 捕获值
 * @param event_type 事件类型
 */
void timestamp_driver_tim3_irq_handler(timestamp_driver_handle_t *handle,
                                      uint16_t capture_value,
                                      timestamp_driver_event_type_t event_type);

/**
 * @brief TIM4输入捕获中断处理函数
 * @param handle 驱动句柄
 * @param capture_value 捕获值
 * @param event_type 事件类型
 */
void timestamp_driver_tim4_irq_handler(timestamp_driver_handle_t *handle,
                                      uint16_t capture_value,
                                      timestamp_driver_event_type_t event_type);

/* 平台相关的硬件操作函数 - 需要具体平台实现 */

/**
 * @brief 平台相关的定时器初始化
 * @param handle 驱动句柄
 * @return timestamp_driver_ret_t 返回值
 */
timestamp_driver_ret_t timestamp_driver_hw_init(timestamp_driver_handle_t *handle);

/**
 * @brief 平台相关的定时器反初始化
 * @param handle 驱动句柄
 * @return timestamp_driver_ret_t 返回值
 */
timestamp_driver_ret_t timestamp_driver_hw_deinit(timestamp_driver_handle_t *handle);

/**
 * @brief 平台相关的定时器启动
 * @param handle 驱动句柄
 * @param tim_type 定时器类型
 * @return timestamp_driver_ret_t 返回值
 */
timestamp_driver_ret_t timestamp_driver_hw_start(timestamp_driver_handle_t *handle,
                                                timestamp_driver_tim_type_t tim_type);

/**
 * @brief 平台相关的定时器停止
 * @param handle 驱动句柄
 * @param tim_type 定时器类型
 * @return timestamp_driver_ret_t 返回值
 */
timestamp_driver_ret_t timestamp_driver_hw_stop(timestamp_driver_handle_t *handle,
                                               timestamp_driver_tim_type_t tim_type);

/**
 * @brief 平台相关的获取定时器计数值
 * @param handle 驱动句柄
 * @param tim_type 定时器类型
 * @param count_value 输出计数值
 * @return timestamp_driver_ret_t 返回值
 */
timestamp_driver_ret_t timestamp_driver_hw_get_counter(timestamp_driver_handle_t *handle,
                                                      timestamp_driver_tim_type_t tim_type,
                                                      uint16_t *count_value);

#ifdef __cplusplus
}
#endif

#endif /* __TIMESTAMP_DRIVER_H__ */
