# Parameters:
# instance.parameter=value       #(type, mode) default = 'def value' : description : [min..max]
#------------------------------------------------------------------------------
armcortexm7ct.semihosting-enable=0                    # (bool  , init-time) default = '1'      : Enable semihosting SVC traps. Applications that do not use semihosting must set this parameter to false.
armcortexm7ct.cpi_div=1                               # (int   , run-time ) default = '0x1'    : divider for calculating CPI (Cycles Per Instruction)
armcortexm7ct.cpi_mul=1                               # (int   , run-time ) default = '0x1'    : multiplier for calculating CPI (Cycles Per Instruction)
armcortexm7ct.min_sync_level=3                        # (int   , run-time ) default = '0x0'    : force minimum syncLevel (0=off=default,1=syncState,2=postInsnIO,3=postInsnAll)
armcortexm7ct.vfp-present=1                           # (bool  , init-time) default = '1'      : Set whether the model has VFP support
#------------------------------------------------------------------------------
