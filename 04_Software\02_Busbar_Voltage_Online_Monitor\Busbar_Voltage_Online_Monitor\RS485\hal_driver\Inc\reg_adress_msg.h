#ifndef __REG_ADRESS_MSG__H__
#define __REG_ADRESS_MSG__H__

#include <stdint.h>
#include "elog.h"
// 定义一个枚举类型，表示寄存器的数据位宽
typedef enum
{
    RGE_8BIT,  // 8位
    RGE_16BIT, // 16位
    RGE_32BIT, // 32位
    REG_FLOAT, // 浮点数
}reg_data_bit;

// 定义一个枚举类型，表示寄存器的属性
typedef enum
{
    READ_ONLY,          // 只读
    READABLE_WRITEABLE, // 可读可写
    WRITEABLE_NO_SAVE,  // 可写但不保存,如时间戳
}reg_property_t;

// 定义一个联合体，用于存储寄存器的数据
typedef union 
{
    uint8_t  u8_data;  // 8位数据
    uint16_t u16_data; // 16位数据
    float    f_data;   // 浮点数数据
    uint32_t u32_data; // 32位数据
}rs485_reg_data_t;

typedef struct
{
    uint16_t reg_num;  // 寄存器编号
    uint16_t reg_len;  // 寄存器长度,表示modbus寄存器被分割为几个，如32位寄存器会被分割为两个16位寄存器
}reg_struction_t;

// 定义一个结构体，用于存储寄存器的信息
typedef struct           rs485_reg_t
{
    reg_struction_t  reg_stru;     // 寄存器结构
    reg_data_bit     data_bit;     // 数据位宽
    reg_property_t   property;     // 寄存器属性
    rs485_reg_data_t reg_data;     // 寄存器数据
}rs485_reg_t;

typedef enum 
{
    WRITE_REG_ERROR,
    READ_REG_ERROR,
    WRITE_REG_SUCCESS,
    READ_REG_SUCCESS,
}reg_msg_ret_code_t;

//保持寄存器数量
#define HOLDING_REG_QUANTITY 22

//485寄存器地址
typedef enum
{
    // 基波相关寄存器
    AC_FUNDAMENTAL_VOLTAGE_REG_ADRESS    = 0,  // 交流基波电压
    AC_FUNDAMENTAL_PHASE_REG_ADRESS      = 2,  // 交流基波相位
    AC_FUNDAMENTAL_FREQ_REG_ADRESS       = 4,  // 交流基波频率
    
    // UA相基波及谐波寄存器
    UA_FUNDAMENTAL_VOLTAGE_REG_ADRESS    = 6,  // UA相基波电压
    UA_FUNDAMENTAL_PHASE_REG_ADRESS      = 8,  // UA相基波相位
    UA_3RD_HARMONIC_VOLTAGE_REG_ADRESS   = 10, // UA相3次谐波电压
    UA_5TH_HARMONIC_VOLTAGE_REG_ADRESS   = 12, // UA相5次谐波电压
    UA_7TH_HARMONIC_VOLTAGE_REG_ADRESS   = 14, // UA相7次谐波电压
    UA_9TH_HARMONIC_VOLTAGE_REG_ADRESS   = 16, // UA相9次谐波电压
    
    // UB相基波及谐波寄存器
    UB_FUNDAMENTAL_VOLTAGE_REG_ADRESS    = 18, // UB相基波电压
    UB_FUNDAMENTAL_PHASE_REG_ADRESS      = 20, // UB相基波相位
    UB_3RD_HARMONIC_VOLTAGE_REG_ADRESS   = 22, // UB相3次谐波电压
    UB_5TH_HARMONIC_VOLTAGE_REG_ADRESS   = 24, // UB相5次谐波电压
    UB_7TH_HARMONIC_VOLTAGE_REG_ADRESS   = 26, // UB相7次谐波电压
    UB_9TH_HARMONIC_VOLTAGE_REG_ADRESS   = 28, // UB相9次谐波电压
    
    // UC相基波及谐波寄存器
    UC_FUNDAMENTAL_VOLTAGE_REG_ADRESS    = 30, // UC相基波电压
    UC_FUNDAMENTAL_PHASE_REG_ADRESS      = 32, // UC相基波相位
    UC_3RD_HARMONIC_VOLTAGE_REG_ADRESS   = 34, // UC相3次谐波电压
    UC_5TH_HARMONIC_VOLTAGE_REG_ADRESS   = 36, // UC相5次谐波电压
    UC_7TH_HARMONIC_VOLTAGE_REG_ADRESS   = 38, // UC相7次谐波电压
    UC_9TH_HARMONIC_VOLTAGE_REG_ADRESS   = 40, // UC相9次谐波电压
    
    // 设备地址寄存器
    DEV_ADR_REG_ADRESS                   = 42, // 设备地址
    INVALID_REG_ADRESS                   = 44, // 无效寄存器
}modbus_reg_adress_t;

//数组存储的485寄存器数
typedef enum
{
    // 基波相关寄存器
    AC_FUNDAMENTAL_VOLTAGE_REG = 0,  // 交流基波电压
    AC_FUNDAMENTAL_PHASE_REG,        // 交流基波相位
    AC_FUNDAMENTAL_FREQ_REG,         // 交流基波频率
    
    // UA相基波及谐波寄存器
    UA_FUNDAMENTAL_VOLTAGE_REG,      // UA相基波电压
    UA_FUNDAMENTAL_PHASE_REG,        // UA相基波相位
    UA_3RD_HARMONIC_VOLTAGE_REG,     // UA相3次谐波电压
    UA_5TH_HARMONIC_VOLTAGE_REG,     // UA相5次谐波电压
    UA_7TH_HARMONIC_VOLTAGE_REG,     // UA相7次谐波电压
    UA_9TH_HARMONIC_VOLTAGE_REG,     // UA相9次谐波电压
    
    // UB相基波及谐波寄存器
    UB_FUNDAMENTAL_VOLTAGE_REG,      // UB相基波电压
    UB_FUNDAMENTAL_PHASE_REG,        // UB相基波相位
    UB_3RD_HARMONIC_VOLTAGE_REG,     // UB相3次谐波电压
    UB_5TH_HARMONIC_VOLTAGE_REG,     // UB相5次谐波电压
    UB_7TH_HARMONIC_VOLTAGE_REG,     // UB相7次谐波电压
    UB_9TH_HARMONIC_VOLTAGE_REG,     // UB相9次谐波电压
    
    // UC相基波及谐波寄存器
    UC_FUNDAMENTAL_VOLTAGE_REG,      // UC相基波电压
    UC_FUNDAMENTAL_PHASE_REG,        // UC相基波相位
    UC_3RD_HARMONIC_VOLTAGE_REG,     // UC相3次谐波电压
    UC_5TH_HARMONIC_VOLTAGE_REG,     // UC相5次谐波电压
    UC_7TH_HARMONIC_VOLTAGE_REG,     // UC相7次谐波电压
    UC_9TH_HARMONIC_VOLTAGE_REG,     // UC相9次谐波电压
    
    // 设备地址寄存器
    DEV_ADR_REG,                     // 设备地址
    REG_QUANTITY,                    // 寄存器数量
}reg_storage_place_t;

int8_t Modbus_read_reg_u32(uint16_t reg_adress, uint32_t* value);
int8_t Modbus_read_reg_u32(uint16_t reg_adress, uint32_t* value);
int8_t Modbus_read_reg_u8(uint16_t reg_adress, uint8_t* value);
int8_t Modbus_write_adr_reg(uint16_t reg_adress, uint8_t adress);
int8_t Modbus_write_reg_u32(uint16_t reg_adress, uint32_t reg_value);

int8_t register_timer(void (*timer_callbcakfun)(void*),
                      uint32_t timerperiod);
int8_t timer_stop(void);
int8_t timer_start(void);

reg_msg_ret_code_t modbus_read_reg(uint16_t reg_adress, rs485_reg_data_t *read_data);
reg_msg_ret_code_t modbus_write_reg(uint16_t reg_adress, rs485_reg_data_t reg_value);
reg_msg_ret_code_t modbus_data_origin_input(reg_storage_place_t reg_adr, 
                                            rs485_reg_data_t reg_value);

/**
* 实例化485寄存器信息
* 实例化步骤
* 1.根据内容定义reg_storage_place_t寄存器地址
* 2.调用函数rs485_instance_reg_msg填写寄存器初始信息
*/
int8_t rs485_instance_reg_msg(reg_storage_place_t reg_adr, rs485_reg_t reg_msg);


#endif // __REG_ADRESS_MSG__H__



