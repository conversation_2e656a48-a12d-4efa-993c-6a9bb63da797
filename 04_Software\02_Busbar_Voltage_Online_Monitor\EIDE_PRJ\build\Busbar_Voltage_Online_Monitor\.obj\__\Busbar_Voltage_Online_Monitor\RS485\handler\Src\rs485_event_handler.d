.\build\Busbar_Voltage_Online_Monitor\.obj\__\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.o: .\..\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.c
.\build\Busbar_Voltage_Online_Monitor\.obj\__\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.o: ../Busbar_Voltage_Online_Monitor/RS485/handler/Inc/rs485_event_handler.h
.\build\Busbar_Voltage_Online_Monitor\.obj\__\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.o: ../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Inc/rs485_dev.h
.\build\Busbar_Voltage_Online_Monitor\.obj\__\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.o: ../Middlewares/linked_list/linked_list.h
.\build\Busbar_Voltage_Online_Monitor\.obj\__\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.o: ../MCU_Peripherals_Drivers/Inc/mcu_gpio_driver.h
.\build\Busbar_Voltage_Online_Monitor\.obj\__\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.o: ../easylogger/inc/elog.h
.\build\Busbar_Voltage_Online_Monitor\.obj\__\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.o: ../easylogger/inc/elog_cfg.h
.\build\Busbar_Voltage_Online_Monitor\.obj\__\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.o: ../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Inc/modbus_rtu.h
.\build\Busbar_Voltage_Online_Monitor\.obj\__\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.o: ../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Inc/reg_adress_msg.h
.\build\Busbar_Voltage_Online_Monitor\.obj\__\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.o: ../SYSTEM/Inc/system_config.h
