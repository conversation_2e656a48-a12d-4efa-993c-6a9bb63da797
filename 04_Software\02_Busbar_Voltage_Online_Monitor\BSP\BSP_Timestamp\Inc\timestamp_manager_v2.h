#ifndef __TIMESTAMP_MANAGER_V2_H__
#define __TIMESTAMP_MANAGER_V2_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "timestamp_handler.h"

/* 重构后的时间戳管理器 - 统一接口层 */

/* 管理器返回值 */
typedef enum {
    TIMESTAMP_MANAGER_OK = 0,
    TIMESTAMP_MANAGER_ERROR = -1,
    TIMESTAMP_MANAGER_ERROR_PARAM = -2,
    TIMESTAMP_MANAGER_ERROR_NOT_INIT = -3,
    TIMESTAMP_MANAGER_ERROR_OVERFLOW = -4
} timestamp_manager_ret_t;

/* 管理器配置结构体 */
typedef struct {
    timestamp_config_t base_config;         /* 基础配置 */
    bool enable_debug_output;               /* 启用调试输出 */
    bool enable_statistics;                 /* 启用统计功能 */
    bool auto_overflow_recovery;            /* 自动溢出恢复 */
} timestamp_manager_config_t;

/* 管理器状态结构体 */
typedef struct {
    bool initialized;                       /* 初始化状态 */
    bool running;                          /* 运行状态 */
    timestamp_grid_sync_status_t sync_status; /* 同步状态 */
    timestamp_statistics_t statistics;      /* 统计信息 */
    uint32_t error_count;                  /* 错误计数 */
    uint32_t overflow_count;               /* 溢出计数 */
} timestamp_manager_status_t;

/* 事件回调函数类型 */
typedef void (*timestamp_manager_zero_cross_callback_t)(uint16_t capture_count, uint16_t t_zero, 
                                                       float frequency, void *user_data);
typedef void (*timestamp_manager_signal_freq_callback_t)(float frequency, void *user_data);
typedef void (*timestamp_manager_overflow_callback_t)(uint16_t old_capture_count, void *user_data);
typedef void (*timestamp_manager_error_callback_t)(timestamp_manager_ret_t error_code, 
                                                  const char *error_msg, void *user_data);

/* 事件回调结构体 */
typedef struct {
    timestamp_manager_zero_cross_callback_t on_zero_cross;
    timestamp_manager_signal_freq_callback_t on_signal_freq_update;
    timestamp_manager_overflow_callback_t on_overflow;
    timestamp_manager_error_callback_t on_error;
    void *user_data;
} timestamp_manager_callbacks_t;

/* 管理器接口函数 */

/**
 * @brief 初始化时间戳管理器
 * @param config 配置参数，NULL使用默认配置
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_init(const timestamp_manager_config_t *config);

/**
 * @brief 反初始化时间戳管理器
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_deinit(void);

/**
 * @brief 启动时间戳管理器
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_start(void);

/**
 * @brief 停止时间戳管理器
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_stop(void);

/**
 * @brief 注册事件回调
 * @param callbacks 回调函数结构体
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_register_callbacks(const timestamp_manager_callbacks_t *callbacks);

/**
 * @brief 获取电网同步状态
 * @param status 输出状态信息
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_get_sync_status(timestamp_grid_sync_status_t *status);

/**
 * @brief 获取管理器状态
 * @param status 输出管理器状态
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_get_manager_status(timestamp_manager_status_t *status);

/**
 * @brief 生成压缩时间戳 - 供AD7606驱动调用
 * @return uint32_t 压缩时间戳，0表示失败
 */
uint32_t timestamp_manager_v2_generate_packed_timestamp(void);

/**
 * @brief 计算相位修正参数
 * @param packed_timestamp 压缩时间戳
 * @param correction 输出相位修正参数
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_calculate_phase_correction(uint32_t packed_timestamp,
                                                                       timestamp_phase_correction_t *correction);

/**
 * @brief 应用相位修正到FFT结果
 * @param fft_complex_data FFT复数数据
 * @param fft_length FFT长度
 * @param correction 相位修正参数
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_apply_phase_correction(float *fft_complex_data,
                                                                   uint16_t fft_length,
                                                                   const timestamp_phase_correction_t *correction);

/**
 * @brief 检查并处理溢出
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_check_overflow(void);

/**
 * @brief 获取统计信息
 * @param statistics 输出统计信息
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_get_statistics(timestamp_statistics_t *statistics);

/**
 * @brief 重置统计信息
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_reset_statistics(void);

/**
 * @brief 设置频率测量策略
 * @param tim_type 定时器类型
 * @param strategy_type 策略类型
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_set_freq_strategy(timestamp_driver_tim_type_t tim_type,
                                                              timestamp_freq_strategy_t strategy_type);

/**
 * @brief 获取当前定时器计数值
 * @param tim_type 定时器类型
 * @param count_value 输出计数值
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_get_timer_counter(timestamp_driver_tim_type_t tim_type,
                                                              uint16_t *count_value);

/* 调试和诊断函数 */

/**
 * @brief 打印管理器状态
 */
void timestamp_manager_v2_print_status(void);

/**
 * @brief 打印统计信息
 */
void timestamp_manager_v2_print_statistics(void);

/**
 * @brief 打印配置信息
 */
void timestamp_manager_v2_print_config(void);

/**
 * @brief 执行自检
 * @return timestamp_manager_ret_t 返回值
 */
timestamp_manager_ret_t timestamp_manager_v2_self_test(void);

/* 兼容性接口 - 与旧版本接口兼容 */

/**
 * @brief 兼容性接口：TIM3输入捕获回调
 * @param htim 定时器句柄
 */
void timestamp_manager_v2_tim3_ic_callback(void *htim);

/**
 * @brief 兼容性接口：TIM4输入捕获回调
 * @param htim 定时器句柄
 */
void timestamp_manager_v2_tim4_ic_callback(void *htim);

/* 工厂函数 - 创建不同配置的管理器 */

/**
 * @brief 获取默认配置
 * @return timestamp_manager_config_t 默认配置
 */
timestamp_manager_config_t timestamp_manager_v2_get_default_config(void);

/**
 * @brief 获取高精度配置
 * @return timestamp_manager_config_t 高精度配置
 */
timestamp_manager_config_t timestamp_manager_v2_get_high_precision_config(void);

/**
 * @brief 获取低功耗配置
 * @return timestamp_manager_config_t 低功耗配置
 */
timestamp_manager_config_t timestamp_manager_v2_get_low_power_config(void);

#ifdef __cplusplus
}
#endif

#endif /* __TIMESTAMP_MANAGER_V2_H__ */
