  /******************************************************************************
 * @file bsp_led_driver.h
 * @brief 
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-29
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow: 
 * 
 * @par dependencies
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/

#ifndef __BSP_LED_DRIVER_H__
#define __BSP_LED_DRIVER_H__

  //******************************** Includes *********************************//
#include <linked_list.h>
#include <stdint.h>

#include "elog.h"
  //******************************** Includes *********************************//


  //******************************** Defines **********************************//
#define LED_DRIVER_USE_DYNAMIC_ALLOCATION

  // 定义一个枚举类型LED_WHICH，用于表示不同的LED灯
typedef enum LED_WHICH
{
	  // 表示运行状态的LED灯
	LED_RUN = 0,
	  // 表示485LED灯
	LED_485,
	  // 表示设备数量的LED灯
	LED_DEV_Quantity,
} led_which_t;				// LED设备数量

typedef enum
{
    LED_GPIO_LOW_LEVEL  = 0u,   // 低电平
    LED_GPIO_HIGH_LEVEL = 1,    // 高电平
    LED_GPIO_ERROR_LEVEL,
}led_gpio_interface_state_t;

typedef enum
{
    
  LED_OFF    = 0,
  LED_ON     = 1,
  LED_TOGGLE = 2,
}led_statue_t;


typedef enum
{
  LED_DRIVER_OK             = 0,          /* Operation completed successfully.  */
  LED_DRIVER_ERROR          = 1,          /* Run-time error without case matched*/
  LED_DRIVER_ERRORTIMEOUT   = 2,          /* Operation failed with timeout      */
  LED_DRIVER_ERRORRESOURCE  = 3,          /* Resource not available.            */
  LED_DRIVER_ERRORPARAMETER = 4,          /* Parameter error.                   */
  LED_DRIVER_ERRORNOMEMORY  = 5,          /* Out of memory.                     */
  LED_DRIVER_RESERVED       = 0x7FFFFFFF  /* Reserved                           */
} led_driver_ret_code_t;
  //******************************** Defines **********************************//

  //******************************** Declaring ********************************//

  /* From Core层：     GPIO 接口            */
typedef struct led_gpio_interface_t
{
  void                       *pgpio_handler;
  int8_t                     (*pfgpio_init)        (void *pgpio_handler);                            
  int8_t                     (*pfgpio_deinit)      (void *pgpio_handler);                       
  int8_t                     (*pfgpio_write_pin)   (void *pgpio_handler, 
                                    led_gpio_interface_state_t pinstate);  
  led_gpio_interface_state_t (*pfgpio_read_pin)    (void *pgpio_handler);                           
  int8_t                     (*pfgpio_toggle_pin)  (void *pgpio_handler);                         
}led_gpio_interface_t;
  /* From Core层：     GPIO 接口            */

#ifdef LED_DRIVER_USE_DYNAMIC_ALLOCATION
  /* From Core层：   动态分配 接口        */
typedef struct
{
  void *(*pfmalloc)   (uint32_t mem_size);
  void (*pffree)      (void *p_mem);
}led_dynamic_allocation_t;
  /* From Core层：   动态分配 接口        */
#endif //LED_DRIVER_USE_DYNAMIC_ALLOCATION

  /*  led_driver 实例对象结构         */

typedef struct  led_private_data_t led_private_data_t;

typedef struct bsp_led_driver_t
{
      /*led初始化接口*/
    led_driver_ret_code_t (*pfinit)           (struct bsp_led_driver_t *pled_driver);
      /*led反初始化接口*/
    led_driver_ret_code_t (*pfdeinit)         (struct bsp_led_driver_t *pled_driver);
	  /* 控制LED设备, iStatus取值: 1-亮,0-灭,2-反转 */
	led_driver_ret_code_t (*pfled_control)    (struct bsp_led_driver_t *pled_driver,
                                            led_statue_t statue);
	  /* 控制LED设备亮度, Brightness亮度取值: 待定 */
	led_driver_ret_code_t (*pfset_brightness) (struct bsp_led_driver_t *pled_driver,
                                            int brightness);
    led_driver_ret_code_t (*pfget_led_which)  (struct bsp_led_driver_t *pled_driver,
                                            led_which_t *led_which);
	  /*LED设备私有数据见.c文件*/
	led_private_data_t *private_data;
}bsp_led_driver_t;

led_driver_ret_code_t bsp_led_driver_inst(
                                          bsp_led_driver_t         *pled_driver, 
                                          led_gpio_interface_t     *p_gpio_interface,
#ifdef LED_DRIVER_USE_DYNAMIC_ALLOCATION
                                          led_dynamic_allocation_t *p_dynamic_allocation,
#endif //LED_DRIVER_USE_DYNAMIC_ALLOCATION
                                          led_which_t              led_which
                                          );


#endif  //END OF __BSP_LED_DRIVER_H__
