/******************************************************************************
 * @file uart_led_indicator_example.c
 * @brief 串口LED指示器使用示例
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-01
 *
 * @copyright Copyright (c) 2024
 *
 * 本示例展示如何使用串口LED指示器模块：
 * 1. 初始化配置
 * 2. 在串口接收中断中调用指示函数
 * 3. 在主循环中调用周期性任务
 * 4. 集成到现有的串口处理代码中
 *
 * @note 1 tab == 4 spaces!
 *
 *****************************************************************************/

//******************************** Includes *********************************//
#include "uart_led_indicator.h"
#include "bsp_led_handler.h"
#include "system_adaption.h"  // 假设这里有时基接口
//******************************** Includes *********************************//

//******************************** Variables ********************************//
/* 外部变量声明（假设在其他地方定义） */
extern bsp_led_handler_t g_led_handler;
extern led_handler_timebase_interface_t g_timebase_interface;
//******************************** Variables ********************************//

//******************************** Functions ********************************//

/******************************************************************************
 * @brief 串口LED指示器初始化示例
 *****************************************************************************/
void uart_led_indicator_example_init(void)
{
    /* 配置串口LED指示器 */
    uart_led_indicator_config_t config = {
        .p_led_handler = &g_led_handler,           /* LED处理器实例 */
        .led_which = LED_485,                      /* 使用485通信LED */
        .timeout_ms = 100,                         /* 100ms超时 */
        .p_timebase_interface = (uart_led_indicator_timebase_interface_t*)&g_timebase_interface
    };
    
    /* 初始化 */
    uart_led_indicator_ret_code_t ret = uart_led_indicator_init(&config);
    if (ret != UART_LED_INDICATOR_OK) {
        // 处理初始化失败
        printf("UART LED indicator init failed: %d\n", ret);
        return;
    }
    
    printf("UART LED indicator initialized successfully\n");
}

/******************************************************************************
 * @brief 串口接收中断处理示例（STM32 HAL库）
 *****************************************************************************/
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1) {  // 假设使用USART1
        /* 处理接收到的数据 */
        uint8_t received_data = huart->pRxBuffPtr[0];
        
        // 这里添加您的数据处理逻辑
        // process_received_data(received_data);
        
        /* 触发LED指示 */
        uart_led_indicator_on_data_received();
        
        /* 重新启动接收 */
        HAL_UART_Receive_IT(huart, &received_data, 1);
    }
}

/******************************************************************************
 * @brief 串口接收中断处理示例（标准库）
 *****************************************************************************/
void USART1_IRQHandler(void)
{
    if (USART_GetITStatus(USART1, USART_IT_RXNE) != RESET) {
        /* 读取接收到的数据 */
        uint8_t received_data = USART_ReceiveData(USART1);
        
        // 这里添加您的数据处理逻辑
        // process_received_data(received_data);
        
        /* 触发LED指示 */
        uart_led_indicator_on_data_received();
        
        /* 清除中断标志 */
        USART_ClearITPendingBit(USART1, USART_IT_RXNE);
    }
}

/******************************************************************************
 * @brief DMA接收完成回调示例
 *****************************************************************************/
void HAL_UART_RxHalfCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1) {
        /* DMA接收到一半数据时也触发LED指示 */
        uart_led_indicator_on_data_received();
    }
}

void HAL_UART_RxCpltCallback_DMA(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1) {
        /* DMA接收完成时触发LED指示 */
        uart_led_indicator_on_data_received();
        
        // 处理接收到的数据包
        // process_received_packet(rx_buffer, packet_size);
    }
}

/******************************************************************************
 * @brief 主循环任务示例
 *****************************************************************************/
void main_loop_example(void)
{
    while (1) {
        /* 周期性调用LED指示器任务 */
        uart_led_indicator_task();
        
        /* 其他主循环任务 */
        // other_tasks();
        
        /* 延时 */
        HAL_Delay(10);  // 10ms延时
    }
}

/******************************************************************************
 * @brief RTOS任务示例
 *****************************************************************************/
void uart_led_task(void *argument)
{
    /* 任务初始化 */
    uart_led_indicator_example_init();
    
    while (1) {
        /* 周期性调用LED指示器任务 */
        uart_led_indicator_task();
        
        /* RTOS延时 */
        osDelay(10);  // 10ms延时
    }
}

/******************************************************************************
 * @brief 定时器回调示例（如果使用定时器调用）
 *****************************************************************************/
void TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
    if (htim->Instance == TIM2) {  // 假设使用TIM2，10ms定时器
        /* 在定时器中断中调用LED指示器任务 */
        uart_led_indicator_task();
    }
}

/******************************************************************************
 * @brief 多串口支持示例
 *****************************************************************************/
typedef struct {
    UART_HandleTypeDef *huart;
    led_which_t led_which;
    bool is_initialized;
} uart_led_mapping_t;

/* 多串口LED映射表 */
static uart_led_mapping_t g_uart_led_mappings[] = {
    {&huart1, LED_485, false},      /* UART1 -> LED_485 */
    {&huart2, LED_RUN, false},      /* UART2 -> LED_RUN */
    // 可以添加更多映射
};

void multi_uart_led_init(void)
{
    for (int i = 0; i < sizeof(g_uart_led_mappings) / sizeof(g_uart_led_mappings[0]); i++) {
        uart_led_indicator_config_t config = {
            .p_led_handler = &g_led_handler,
            .led_which = g_uart_led_mappings[i].led_which,
            .timeout_ms = 100,
            .p_timebase_interface = (uart_led_indicator_timebase_interface_t*)&g_timebase_interface
        };
        
        // 注意：这个示例假设只有一个全局实例，实际使用时可能需要多个实例
        // 或者修改uart_led_indicator模块支持多实例
        if (i == 0) {  // 只初始化第一个作为示例
            uart_led_indicator_init(&config);
            g_uart_led_mappings[i].is_initialized = true;
        }
    }
}

/******************************************************************************
 * @brief 高级使用示例：根据数据类型设置不同的超时时间
 *****************************************************************************/
void advanced_uart_led_example(void)
{
    /* 根据不同的通信协议设置不同的超时时间 */
    
    // Modbus协议：通常数据包较短，超时时间可以短一些
    uart_led_indicator_config_t modbus_config = {
        .p_led_handler = &g_led_handler,
        .led_which = LED_485,
        .timeout_ms = 50,  // 50ms超时
        .p_timebase_interface = (uart_led_indicator_timebase_interface_t*)&g_timebase_interface
    };
    
    // 调试串口：可能有连续的日志输出，超时时间可以长一些
    uart_led_indicator_config_t debug_config = {
        .p_led_handler = &g_led_handler,
        .led_which = LED_RUN,
        .timeout_ms = 200,  // 200ms超时
        .p_timebase_interface = (uart_led_indicator_timebase_interface_t*)&g_timebase_interface
    };
    
    // 根据实际需要选择配置
    uart_led_indicator_init(&modbus_config);
}

/******************************************************************************
 * @brief 错误处理示例
 *****************************************************************************/
void uart_led_error_handling_example(void)
{
    uart_led_indicator_ret_code_t ret;
    
    /* 检查当前状态 */
    uart_led_indicator_state_t state = uart_led_indicator_get_state();
    if (state == UART_LED_INDICATOR_STATE_RECEIVING) {
        printf("UART is currently receiving data\n");
    }
    
    /* 强制关闭LED（例如在系统错误时） */
    ret = uart_led_indicator_force_off();
    if (ret != UART_LED_INDICATOR_OK) {
        printf("Failed to force off LED: %d\n", ret);
    }
    
    /* 反初始化（例如在系统关闭时） */
    ret = uart_led_indicator_deinit();
    if (ret != UART_LED_INDICATOR_OK) {
        printf("Failed to deinitialize: %d\n", ret);
    }
}
