#include "reg_adress_msg.h"
#include "rs485_event_handler.h"
#include <string.h>






int8_t Mo<PERSON><PERSON>_read_reg_f(uint16_t reg_adress, float* value)
{
    
    if(reg_adress > REG_QUANTITY)
    {
        return -1;
    }

    return 0;
}


int8_t Mo<PERSON><PERSON>_read_reg_u32(uint16_t reg_adress, uint32_t* value)
{
    
    if(reg_adress > REG_QUANTITY)
    {
        return -1;
    }
    return 0;
}

int8_t Modbus_read_reg_u8(uint16_t reg_adress, uint8_t* value)
{
    if(reg_adress > REG_QUANTITY)
    {
        return -1;
    }

    return 0;
}

int8_t Modbus_write_adr_reg(uint16_t reg_adress, uint8_t adress)
{
    rs485_event_t event;
    if(DEV_ADR_REG != reg_adress)
    {
        return -1;
    }
//    event.event_type = MODIFY_DEV_ADRESS_EVENT;
    event.reg_data.u8_data = adress;
    return rs485_event_handle_send(event);
}

int8_t Modbus_write_reg_u32(uint16_t reg_adress, uint32_t reg_value)
{
    rs485_event_t event;

//    event.event_type = MODIFY_THUNDERSTRIKE_COUNT_EVENT;
    event.reg_data.u32_data = reg_value;
    return rs485_event_handle_send(event);
}

/**
 * @brief 读取寄存器时的回调函数
 * 
 * @param reg_data 寄存器数据
 * @param read_data 读取数据
 */
static void __read_reg_data_callbackfun(uint16_t reg_data, void* read_data)
{
    // 如果寄存器数据或读取数据为空，则打印错误信息并返回
    if(NULL == read_data)
    {
        log_e("read reg data callback fun error, filed %s, line %d",__FILE__,__LINE__);
        return;
    }
    rs485_reg_data_t *data = (rs485_reg_data_t*)read_data;
    data->u16_data = reg_data;
}
/**
 * @brief 写入寄存器数据
 * 
 * @param reg_adress 寄存器地址
 * @param reg_value 寄存器数据
 * @return reg_msg_ret_code_t 
 * @retval WRITE_REG_SUCCESS 写入成功
 * @retval WRITE_REG_ERROR 写入失败
 * @retval WRITE_REG_ERROR 寄存器地址错误
 */
reg_msg_ret_code_t modbus_write_reg(uint16_t reg_adress, rs485_reg_data_t reg_value)
{
    // 如果寄存器地址大于寄存器数量，则打印错误信息并返回
    if(reg_adress > INVALID_REG_ADRESS)
    {
        log_e("write reg adress error, adress = %d,filed %s, line %d",__FILE__,__LINE__);
        return WRITE_REG_ERROR;
    }
    // 创建事件
    rs485_event_t event;
    event.event_type = WRITE_REG_DATA_EVENT;
    event.reg_num = reg_adress;
    event.reg_data = reg_value;
    // 发送事件
    if(0 != rs485_event_handle_send(event))
    {
        log_e("rs485 event queue error,filed %s, line %d",__FILE__,__LINE__);
        return WRITE_REG_ERROR;
    }
    // 返回成功
    return WRITE_REG_SUCCESS;
}

/**
 * @brief 读取寄存器数据
 * 
 * @param reg_adress 寄存器地址
 * @param read_data 读取的数据变量地址
 * @return reg_msg_ret_code_t 
 */
reg_msg_ret_code_t modbus_read_reg(uint16_t reg_adress, rs485_reg_data_t *read_data)
{
    // 如果寄存器地址大于寄存器数量，则打印错误信息并返回
    if(reg_adress >= INVALID_REG_ADRESS)
    {
        log_e("read reg adress error, adress = %d,filed %s, line %d",__FILE__,__LINE__);
        return READ_REG_ERROR;
    }
    // 创建事件
    rs485_event_t event;
    event.event_type = READ_REG_DATA_EVENT;
    event.reg_num = reg_adress;
    event.read_data = read_data;
    event.read_reg_data_callback = __read_reg_data_callbackfun;
    // 发送事件
    if(0!= rs485_event_handle_send(event))
    {
        log_e("rs485 event queue error,filed %s, line %d",__FILE__,__LINE__);
        return READ_REG_ERROR;
    }
    // 返回成功
    return READ_REG_SUCCESS;
}

/******************************************************************************
 * @brief 寄存器原始数据输入
 * 
 * @param  reg_adr      寄存器地址        
 * @param  reg_value    寄存器数组      
 * 
 * @return reg_msg_ret_code_t 
 * @retval WRITE_REG_SUCCESS 写入成功
 * @retval WRITE_REG_ERROR 写入失败
 *****************************************************************************/
reg_msg_ret_code_t modbus_data_origin_input(reg_storage_place_t reg_adr, 
                                            rs485_reg_data_t reg_value)
{
    /* 0. 检查寄存器地址是否合法 */
    if(reg_adr >= INVALID_REG_ADRESS)
    {
        log_e("reg adress error, adress = %d,filed %s, line %d",__FILE__,__LINE__);
        return WRITE_REG_ERROR;
    }
    /* 1. 创建事件 */
    rs485_event_t event;
    event.event_type = COLLECT_DATA_INPUT_EVENT;
    event.reg_num = reg_adr;
    event.reg_data = reg_value;
    /* 2. 发送事件 */
    if(0 != rs485_event_handle_send(event))
    {
        log_e("rs485 event queue error,filed %s, line %d",__FILE__,__LINE__);
        return WRITE_REG_ERROR;
    }
    /* 3. 返回成功 */
    return WRITE_REG_SUCCESS;
}




uint16_t find_reg_storage_place(uint16_t reg_adr)
{
    return reg_adr;
}
