#include "timestamp_driver.h"
#include "tim.h"
#include "stm32f4xx_hal.h"
#include <string.h>

/* 外部定时器句柄 */
extern TIM_HandleTypeDef htim3;
extern TIM_HandleTypeDef htim4;

/* 全局驱动句柄 - 用于中断回调 */
static timestamp_driver_handle_t *g_driver_handle = NULL;

/**
 * @brief 初始化时间戳驱动
 */
timestamp_driver_ret_t timestamp_driver_init(timestamp_driver_handle_t *handle, 
                                            const timestamp_config_t *config)
{
    if (handle == NULL) {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    /* 清零句柄 */
    memset(handle, 0, sizeof(timestamp_driver_handle_t));

    /* 设置配置 */
    if (config != NULL) {
        handle->config = *config;
    } else {
        handle->config = timestamp_get_default_config();
    }

    /* 设置硬件句柄 */
    handle->hw_tim3_handle = &htim3;
    handle->hw_tim4_handle = &htim4;

    /* 初始化状态 */
    handle->status.initialized = false;
    handle->status.tim_grid_enabled = false;
    handle->status.tim_signal_enabled = false;
    handle->status.grid_capture_count = 0;
    handle->status.signal_capture_count = 0;
    handle->status.error_count = 0;

    /* 平台相关的硬件初始化 */
    timestamp_driver_ret_t ret = timestamp_driver_hw_init(handle);
    if (ret != TIMESTAMP_DRIVER_OK) {
        return ret;
    }

    /* 设置全局句柄 */
    g_driver_handle = handle;
    
    handle->status.initialized = true;
    return TIMESTAMP_DRIVER_OK;
}

/**
 * @brief 反初始化时间戳驱动
 */
timestamp_driver_ret_t timestamp_driver_deinit(timestamp_driver_handle_t *handle)
{
    if (handle == NULL || !handle->status.initialized) {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    /* 停止所有捕获 */
    timestamp_driver_stop_capture(handle, TIMESTAMP_DRIVER_TIM_GRID_ZERO);
    timestamp_driver_stop_capture(handle, TIMESTAMP_DRIVER_TIM_SIGNAL_FREQ);

    /* 平台相关的硬件反初始化 */
    timestamp_driver_hw_deinit(handle);

    /* 清除全局句柄 */
    if (g_driver_handle == handle) {
        g_driver_handle = NULL;
    }

    handle->status.initialized = false;
    return TIMESTAMP_DRIVER_OK;
}

/**
 * @brief 启动定时器捕获
 */
timestamp_driver_ret_t timestamp_driver_start_capture(timestamp_driver_handle_t *handle,
                                                     timestamp_driver_tim_type_t tim_type)
{
    if (handle == NULL || !handle->status.initialized) {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    timestamp_driver_ret_t ret = timestamp_driver_hw_start(handle, tim_type);
    if (ret == TIMESTAMP_DRIVER_OK) {
        if (tim_type == TIMESTAMP_DRIVER_TIM_GRID_ZERO) {
            handle->status.tim_grid_enabled = true;
        } else if (tim_type == TIMESTAMP_DRIVER_TIM_SIGNAL_FREQ) {
            handle->status.tim_signal_enabled = true;
        }
    }

    return ret;
}

/**
 * @brief 停止定时器捕获
 */
timestamp_driver_ret_t timestamp_driver_stop_capture(timestamp_driver_handle_t *handle,
                                                    timestamp_driver_tim_type_t tim_type)
{
    if (handle == NULL || !handle->status.initialized) {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    timestamp_driver_ret_t ret = timestamp_driver_hw_stop(handle, tim_type);
    if (ret == TIMESTAMP_DRIVER_OK) {
        if (tim_type == TIMESTAMP_DRIVER_TIM_GRID_ZERO) {
            handle->status.tim_grid_enabled = false;
        } else if (tim_type == TIMESTAMP_DRIVER_TIM_SIGNAL_FREQ) {
            handle->status.tim_signal_enabled = false;
        }
    }

    return ret;
}

/**
 * @brief 注册回调函数
 */
timestamp_driver_ret_t timestamp_driver_register_callback(timestamp_driver_handle_t *handle,
                                                         timestamp_driver_callback_t callback,
                                                         void *user_data)
{
    if (handle == NULL || !handle->status.initialized) {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    handle->callback = callback;
    handle->user_data = user_data;
    return TIMESTAMP_DRIVER_OK;
}

/**
 * @brief 获取当前定时器计数值
 */
timestamp_driver_ret_t timestamp_driver_get_counter(timestamp_driver_handle_t *handle,
                                                   timestamp_driver_tim_type_t tim_type,
                                                   uint16_t *count_value)
{
    if (handle == NULL || !handle->status.initialized || count_value == NULL) {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    return timestamp_driver_hw_get_counter(handle, tim_type, count_value);
}

/**
 * @brief 获取驱动状态
 */
timestamp_driver_ret_t timestamp_driver_get_status(timestamp_driver_handle_t *handle,
                                                  timestamp_driver_status_t *status)
{
    if (handle == NULL || status == NULL) {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    *status = handle->status;
    return TIMESTAMP_DRIVER_OK;
}

/**
 * @brief 重置驱动统计信息
 */
timestamp_driver_ret_t timestamp_driver_reset_statistics(timestamp_driver_handle_t *handle)
{
    if (handle == NULL || !handle->status.initialized) {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    handle->status.grid_capture_count = 0;
    handle->status.signal_capture_count = 0;
    handle->status.error_count = 0;
    return TIMESTAMP_DRIVER_OK;
}

/**
 * @brief TIM3输入捕获中断处理函数
 */
void timestamp_driver_tim3_irq_handler(timestamp_driver_handle_t *handle,
                                      uint16_t capture_value,
                                      timestamp_driver_event_type_t event_type)
{
    if (handle == NULL || !handle->status.initialized) {
        handle->status.error_count++;
        return;
    }

    /* 构造捕获数据 */
    timestamp_driver_capture_data_t capture_data = {
        .capture_value = capture_value,
        .event_type = event_type,
        .system_tick = HAL_GetTick(),
        .valid = true
    };

    /* 更新统计 */
    handle->status.signal_capture_count++;

    /* 调用回调函数 */
    if (handle->callback != NULL) {
        handle->callback(TIMESTAMP_DRIVER_TIM_SIGNAL_FREQ, &capture_data, handle->user_data);
    }
}

/**
 * @brief TIM4输入捕获中断处理函数
 */
void timestamp_driver_tim4_irq_handler(timestamp_driver_handle_t *handle,
                                      uint16_t capture_value,
                                      timestamp_driver_event_type_t event_type)
{
    if (handle == NULL || !handle->status.initialized) {
        handle->status.error_count++;
        return;
    }

    /* 构造捕获数据 */
    timestamp_driver_capture_data_t capture_data = {
        .capture_value = capture_value,
        .event_type = event_type,
        .system_tick = HAL_GetTick(),
        .valid = true
    };

    /* 更新统计 */
    handle->status.grid_capture_count++;

    /* 调用回调函数 */
    if (handle->callback != NULL) {
        handle->callback(TIMESTAMP_DRIVER_TIM_GRID_ZERO, &capture_data, handle->user_data);
    }
}

/* 全局中断回调函数 - 供system_adaption.c调用 */
void timestamp_driver_global_tim3_callback(uint16_t capture_value, timestamp_driver_event_type_t event_type)
{
    if (g_driver_handle != NULL) {
        timestamp_driver_tim3_irq_handler(g_driver_handle, capture_value, event_type);
    }
}

void timestamp_driver_global_tim4_callback(uint16_t capture_value, timestamp_driver_event_type_t event_type)
{
    if (g_driver_handle != NULL) {
        timestamp_driver_tim4_irq_handler(g_driver_handle, capture_value, event_type);
    }
}

/* 平台相关的硬件操作函数实现 */

/**
 * @brief 平台相关的定时器初始化
 */
timestamp_driver_ret_t timestamp_driver_hw_init(timestamp_driver_handle_t *handle)
{
    if (handle == NULL) {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    /* STM32F4平台的定时器已经在CubeMX中初始化 */
    /* 这里只需要验证定时器句柄是否有效 */
    if (handle->hw_tim3_handle == NULL || handle->hw_tim4_handle == NULL) {
        return TIMESTAMP_DRIVER_ERROR_INIT;
    }

    return TIMESTAMP_DRIVER_OK;
}

/**
 * @brief 平台相关的定时器反初始化
 */
timestamp_driver_ret_t timestamp_driver_hw_deinit(timestamp_driver_handle_t *handle)
{
    if (handle == NULL) {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    /* STM32F4平台的定时器反初始化 */
    /* 通常不需要特殊处理，因为定时器由HAL管理 */
    return TIMESTAMP_DRIVER_OK;
}

/**
 * @brief 平台相关的定时器启动
 */
timestamp_driver_ret_t timestamp_driver_hw_start(timestamp_driver_handle_t *handle,
                                                timestamp_driver_tim_type_t tim_type)
{
    if (handle == NULL) {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    HAL_StatusTypeDef hal_ret = HAL_OK;

    if (tim_type == TIMESTAMP_DRIVER_TIM_GRID_ZERO) {
        /* 启动TIM4输入捕获中断 */
        TIM_HandleTypeDef *htim4 = (TIM_HandleTypeDef *)handle->hw_tim4_handle;
        hal_ret = HAL_TIM_IC_Start_IT(htim4, TIM_CHANNEL_1);
    } else if (tim_type == TIMESTAMP_DRIVER_TIM_SIGNAL_FREQ) {
        /* 启动TIM3输入捕获中断 */
        TIM_HandleTypeDef *htim3 = (TIM_HandleTypeDef *)handle->hw_tim3_handle;
        hal_ret = HAL_TIM_IC_Start_IT(htim3, TIM_CHANNEL_1);
    } else {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    return (hal_ret == HAL_OK) ? TIMESTAMP_DRIVER_OK : TIMESTAMP_DRIVER_ERROR;
}

/**
 * @brief 平台相关的定时器停止
 */
timestamp_driver_ret_t timestamp_driver_hw_stop(timestamp_driver_handle_t *handle,
                                               timestamp_driver_tim_type_t tim_type)
{
    if (handle == NULL) {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    HAL_StatusTypeDef hal_ret = HAL_OK;

    if (tim_type == TIMESTAMP_DRIVER_TIM_GRID_ZERO) {
        /* 停止TIM4输入捕获中断 */
        TIM_HandleTypeDef *htim4 = (TIM_HandleTypeDef *)handle->hw_tim4_handle;
        hal_ret = HAL_TIM_IC_Stop_IT(htim4, TIM_CHANNEL_1);
    } else if (tim_type == TIMESTAMP_DRIVER_TIM_SIGNAL_FREQ) {
        /* 停止TIM3输入捕获中断 */
        TIM_HandleTypeDef *htim3 = (TIM_HandleTypeDef *)handle->hw_tim3_handle;
        hal_ret = HAL_TIM_IC_Stop_IT(htim3, TIM_CHANNEL_1);
    } else {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    return (hal_ret == HAL_OK) ? TIMESTAMP_DRIVER_OK : TIMESTAMP_DRIVER_ERROR;
}

/**
 * @brief 平台相关的获取定时器计数值
 */
timestamp_driver_ret_t timestamp_driver_hw_get_counter(timestamp_driver_handle_t *handle,
                                                      timestamp_driver_tim_type_t tim_type,
                                                      uint16_t *count_value)
{
    if (handle == NULL || count_value == NULL) {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    if (tim_type == TIMESTAMP_DRIVER_TIM_GRID_ZERO) {
        /* 获取TIM4计数值 */
        TIM_HandleTypeDef *htim4 = (TIM_HandleTypeDef *)handle->hw_tim4_handle;
        *count_value = (uint16_t)__HAL_TIM_GET_COUNTER(htim4);
    } else if (tim_type == TIMESTAMP_DRIVER_TIM_SIGNAL_FREQ) {
        /* 获取TIM3计数值 */
        TIM_HandleTypeDef *htim3 = (TIM_HandleTypeDef *)handle->hw_tim3_handle;
        *count_value = (uint16_t)__HAL_TIM_GET_COUNTER(htim3);
    } else {
        return TIMESTAMP_DRIVER_ERROR_PARAM;
    }

    return TIMESTAMP_DRIVER_OK;
}
