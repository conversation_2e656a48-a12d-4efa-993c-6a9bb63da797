/******************************************************************************
 * @file multi_led_callback_example.c
 * @brief 多LED独立回调机制使用示例
 * <AUTHOR>
 * @version 2.0
 * @date 2024-11-01
 *
 * @copyright Copyright (c) 2024
 *
 * 本示例展示了新的LED回调机制的优势：
 * 1. 每个LED控制请求可以有独立的回调函数
 * 2. 支持多LED同时工作，互不干扰
 * 3. 更好的代码组织和维护性
 * 4. 避免全局回调函数的冲突问题
 *
 * @note 1 tab == 4 spaces!
 *
 *****************************************************************************/

//******************************** Includes *********************************//
#include "bsp_led_handler.h"
#include "uart_led_indicator.h"
#include "system_adaption.h"
//******************************** Includes *********************************//

//******************************** Variables ********************************//
/* LED处理器实例 */
extern bsp_led_handler_t g_led_handler;

/* 统计数据 */
typedef struct {
    uint32_t run_led_blink_count;
    uint32_t comm_led_timeout_count;
    uint32_t total_packets_received;
    uint32_t system_status_changes;
} led_statistics_t;

static led_statistics_t g_led_stats = {0};
//******************************** Variables ********************************//

//******************************** Functions ********************************//

/**
 * @brief 系统运行状态LED回调函数
 * 
 * @param led_which LED编号
 * @param mode LED模式
 * @param p_param 用户参数
 */
void system_run_led_callback(led_which_t led_which, led_control_mode_t mode, void *p_param)
{
    led_statistics_t *p_stats = (led_statistics_t*)p_param;
    
    switch (mode) {
        case LED_MODE_OFF:
            p_stats->run_led_blink_count++;
            printf("[SYSTEM] Run LED blink completed, total: %lu\n", p_stats->run_led_blink_count);
            break;
            
        case LED_MODE_BLINK:
            printf("[SYSTEM] Run LED blink started\n");
            break;
            
        case LED_MODE_ON:
            printf("[SYSTEM] Run LED turned on (steady)\n");
            break;
            
        default:
            break;
    }
}

/**
 * @brief 通信状态LED回调函数
 * 
 * @param led_which LED编号
 * @param mode LED模式
 * @param p_param 用户参数
 */
void communication_led_callback(led_which_t led_which, led_control_mode_t mode, void *p_param)
{
    led_statistics_t *p_stats = (led_statistics_t*)p_param;
    
    switch (mode) {
        case LED_MODE_OFF:
            p_stats->comm_led_timeout_count++;
            printf("[COMM] Communication LED timeout, total timeouts: %lu\n", p_stats->comm_led_timeout_count);
            printf("[COMM] Total packets received in this session: %lu\n", p_stats->total_packets_received);
            break;
            
        case LED_MODE_TIMER_ON:
            printf("[COMM] Communication LED activated (data received)\n");
            break;
            
        default:
            break;
    }
}

/**
 * @brief UART LED指示器状态回调函数
 * 
 * @param new_state 新状态
 * @param p_user_data 用户数据
 */
void uart_indicator_state_callback(uart_led_indicator_state_t new_state, void *p_user_data)
{
    led_statistics_t *p_stats = (led_statistics_t*)p_user_data;
    
    switch (new_state) {
        case UART_LED_INDICATOR_STATE_IDLE:
            printf("[UART] Communication stopped, session ended\n");
            break;
            
        case UART_LED_INDICATOR_STATE_RECEIVING:
            p_stats->system_status_changes++;
            printf("[UART] Communication started, status changes: %lu\n", p_stats->system_status_changes);
            break;
            
        default:
            break;
    }
}

/**
 * @brief 系统状态指示示例
 */
void system_status_indication_example(void)
{
    printf("=== System Status Indication Example ===\n");
    
    /* 系统启动指示 - 快速闪烁3次 */
    led_control_param_t startup_blink = {
        .led_which = LED_RUN,
        .mode = LED_MODE_BLINK,
        .cycle_time_ms = 200,
        .blink_count = 3,
        .proportion = LED_BLINK_RATIO_1_1,
        .brightness = 100,
        .event_callback = system_run_led_callback,
        .p_callback_param = &g_led_stats
    };
    
    bsp_led_handler_control(&g_led_handler, &startup_blink, true);
    printf("System startup indication sent\n");
    
    /* 延时后发送正常运行指示 - 慢速闪烁 */
    HAL_Delay(2000);
    
    led_control_param_t normal_blink = {
        .led_which = LED_RUN,
        .mode = LED_MODE_BLINK,
        .cycle_time_ms = 2000,
        .blink_count = 0,  /* 无限闪烁 */
        .proportion = LED_BLINK_RATIO_1_9,  /* 短亮长暗 */
        .brightness = 50,
        .event_callback = system_run_led_callback,
        .p_callback_param = &g_led_stats
    };
    
    bsp_led_handler_control(&g_led_handler, &normal_blink, true);
    printf("Normal operation indication sent\n");
}

/**
 * @brief 通信状态指示示例
 */
void communication_status_indication_example(void)
{
    printf("=== Communication Status Indication Example ===\n");
    
    /* 模拟接收到数据包 */
    for (int i = 0; i < 5; i++) {
        g_led_stats.total_packets_received++;
        
        /* 每次接收数据时点亮LED 100ms */
        led_control_param_t comm_indication = {
            .led_which = LED_485,
            .mode = LED_MODE_TIMER_ON,
            .timer_duration_ms = 100,
            .brightness = 80,
            .event_callback = communication_led_callback,
            .p_callback_param = &g_led_stats
        };
        
        bsp_led_handler_control(&g_led_handler, &comm_indication, true);
        printf("Packet %d received, LED indication sent\n", i + 1);
        
        /* 模拟数据包间隔 */
        HAL_Delay(150);
    }
    
    printf("Communication burst completed\n");
}

/**
 * @brief UART LED指示器事件驱动示例
 */
void uart_led_indicator_event_driven_example(void)
{
    printf("=== UART LED Indicator Event-Driven Example ===\n");
    
    /* 配置UART LED指示器（事件驱动模式） */
    extern uart_led_indicator_timebase_interface_t led_handler_timebase_interface;
    
    uart_led_indicator_config_t config = {
        .p_led_handler = &g_led_handler,
        .led_which = LED_485,
        .timeout_ms = 200,
        .p_timebase_interface = &led_handler_timebase_interface,
        .state_callback = uart_indicator_state_callback,  /* 启用事件驱动 */
        .p_user_data = &g_led_stats
    };
    
    uart_led_indicator_ret_code_t ret = uart_led_indicator_init(&config);
    if (ret != UART_LED_INDICATOR_OK) {
        printf("UART LED indicator init failed: %d\n", ret);
        return;
    }
    
    printf("UART LED indicator initialized in event-driven mode\n");
    
    /* 模拟串口数据接收 */
    for (int i = 0; i < 3; i++) {
        printf("Simulating UART data reception %d\n", i + 1);
        uart_led_indicator_on_data_received();
        
        /* 模拟数据接收间隔 */
        HAL_Delay(50);
    }
    
    /* 等待超时 */
    printf("Waiting for timeout...\n");
    HAL_Delay(300);
    
    printf("UART LED indicator example completed\n");
}

/**
 * @brief 综合示例 - 多LED协同工作
 */
void comprehensive_multi_led_example(void)
{
    printf("=== Comprehensive Multi-LED Example ===\n");
    
    /* 重置统计数据 */
    memset(&g_led_stats, 0, sizeof(g_led_stats));
    
    /* 1. 系统状态指示 */
    system_status_indication_example();
    HAL_Delay(1000);
    
    /* 2. 通信状态指示 */
    communication_status_indication_example();
    HAL_Delay(1000);
    
    /* 3. UART LED指示器 */
    uart_led_indicator_event_driven_example();
    
    /* 4. 打印统计信息 */
    printf("\n=== Final Statistics ===\n");
    printf("Run LED blink completions: %lu\n", g_led_stats.run_led_blink_count);
    printf("Comm LED timeouts: %lu\n", g_led_stats.comm_led_timeout_count);
    printf("Total packets received: %lu\n", g_led_stats.total_packets_received);
    printf("System status changes: %lu\n", g_led_stats.system_status_changes);
    
    printf("\nMulti-LED comprehensive example completed successfully!\n");
}

/**
 * @brief 架构优势演示
 */
void architecture_benefits_demo(void)
{
    printf("=== Architecture Benefits Demonstration ===\n");
    
    printf("\n1. Independent Callbacks:\n");
    printf("   - Each LED control has its own callback function\n");
    printf("   - No global callback conflicts\n");
    printf("   - Better code organization\n");
    
    printf("\n2. Per-Control Parameters:\n");
    printf("   - Each callback can have different user data\n");
    printf("   - Context-specific processing\n");
    printf("   - Easier debugging and maintenance\n");
    
    printf("\n3. Scalability:\n");
    printf("   - Easy to add new LED types\n");
    printf("   - No modification to existing code\n");
    printf("   - Modular design principles\n");
    
    printf("\n4. Resource Efficiency:\n");
    printf("   - Only active LEDs consume callback resources\n");
    printf("   - No global state management overhead\n");
    printf("   - Better memory locality\n");
}

//******************************** Functions ********************************//
