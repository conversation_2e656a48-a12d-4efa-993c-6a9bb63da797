#include "timestamp_sync.h"
#include "tim.h"
#include "stm32f4xx_hal.h"

/* 全局时间戳同步句柄 */
static timestamp_sync_handle_t g_timestamp_sync_handle = {0};

/* 外部定时器句柄声明 */
extern TIM_HandleTypeDef htim3;
extern TIM_HandleTypeDef htim4;

/**
 * @brief 初始化时间戳管理器
 */
timestamp_sync_ret_t timestamp_manager_init(void)
{
    timestamp_sync_config_t config = {
        .timer_freq_hz = 1000000U,      /* 1MHz定时器频率 */
        .sample_rate_hz = 6400U,        /* 6.4kHz采样率 */
        .fft_length = 1024U,            /* 1024点FFT */
        .nominal_freq_hz = 50.0f        /* 50Hz标称频率 */
    };

    timestamp_sync_ret_t ret = timestamp_sync_init(&g_timestamp_sync_handle, &config);
    
    if (ret == TIMESTAMP_SYNC_OK) {
        /* 启动TIM3输入捕获中断 */
        if (HAL_TIM_IC_Start_IT(&htim3, TIM_CHANNEL_1) != HAL_OK) {
            return TIMESTAMP_SYNC_ERROR;
        }
        if (HAL_TIM_IC_Start_IT(&htim3, TIM_CHANNEL_2) != HAL_OK) {
            return TIMESTAMP_SYNC_ERROR;
        }
        
        /* 启动TIM4输入捕获中断（备用） */
        if (HAL_TIM_IC_Start_IT(&htim4, TIM_CHANNEL_1) != HAL_OK) {
            return TIMESTAMP_SYNC_ERROR;
        }
        if (HAL_TIM_IC_Start_IT(&htim4, TIM_CHANNEL_2) != HAL_OK) {
            return TIMESTAMP_SYNC_ERROR;
        }
    }

    return ret;
}

/**
 * @brief 获取时间戳管理器句柄
 */
timestamp_sync_handle_t* timestamp_manager_get_handle(void)
{
    return &g_timestamp_sync_handle;
}

/**
 * @brief 获取当前定时器CNT值
 */
uint32_t timestamp_manager_get_timer_cnt(void)
{
    return __HAL_TIM_GET_COUNTER(&htim3);
}

/**
 * @brief TIM3信号频率捕获中断回调函数 - 按照文档规范
 */
void timestamp_manager_tim3_ic_callback(TIM_HandleTypeDef *htim)
{
    if (htim->Instance == TIM3) {
        if (htim->Channel == HAL_TIM_ACTIVE_CHANNEL_1) {
            /* TIM3专用于信号频率检测 - 按照文档规范 */
            uint16_t capture_value = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_1);

            /* 调用TIM3信号频率回调 */
            timestamp_sync_tim3_signal_callback(&g_timestamp_sync_handle, capture_value);
        }
    }
}

/**
 * @brief TIM4零点捕获中断回调函数 - 按照文档规范
 */
void timestamp_manager_tim4_ic_callback(TIM_HandleTypeDef *htim)
{
    if (htim->Instance == TIM4) {
        if (htim->Channel == HAL_TIM_ACTIVE_CHANNEL_1) {
            /* TIM4专用于电网零点捕获 - 按照文档规范 */
            uint16_t capture_value = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_1);

            /* 调用TIM4零点捕获回调 */
            timestamp_sync_tim4_zero_cross_callback(&g_timestamp_sync_handle, capture_value);
        }
    }
}

/**
 * @brief 生成压缩时间戳（供AD7606驱动调用）- 按照文档规范
 */
uint32_t timestamp_manager_generate_packed_timestamp(void)
{
    if (!g_timestamp_sync_handle.initialized) {
        return 0;
    }

    /* 获取当前定时器CNT值作为采样起始时间 */
    uint16_t current_timer_cnt = (uint16_t)timestamp_manager_get_timer_cnt();

    /* 生成压缩时间戳 */
    return timestamp_sync_generate_packed_timestamp(&g_timestamp_sync_handle, current_timer_cnt);
}

/**
 * @brief 获取电网同步状态 - 按照文档规范
 */
timestamp_sync_ret_t timestamp_manager_get_grid_status(GridSyncStatus *status)
{
    if (!g_timestamp_sync_handle.initialized) {
        return TIMESTAMP_SYNC_ERROR;
    }

    return timestamp_sync_get_grid_status(&g_timestamp_sync_handle, status);
}

/**
 * @brief 计算相位修正参数 - 按照文档规范使用压缩时间戳
 */
timestamp_sync_ret_t timestamp_manager_calculate_phase_correction(uint32_t packed_timestamp,
                                                                 phase_correction_t *correction)
{
    if (!g_timestamp_sync_handle.initialized) {
        return TIMESTAMP_SYNC_ERROR;
    }

    return timestamp_sync_calculate_phase_correction(&g_timestamp_sync_handle,
                                                    packed_timestamp,
                                                    correction);
}

/**
 * @brief 应用相位修正到FFT结果
 */
timestamp_sync_ret_t timestamp_manager_apply_phase_correction(float *fft_complex_data,
                                                             uint16_t fft_length,
                                                             const phase_correction_t *correction)
{
    return timestamp_sync_apply_phase_correction(fft_complex_data, fft_length, correction);
}

/**
 * @brief 检查并处理溢出 - 按照文档规范
 */
timestamp_sync_ret_t timestamp_manager_check_overflow(void)
{
    if (!g_timestamp_sync_handle.initialized) {
        return TIMESTAMP_SYNC_ERROR;
    }

    return timestamp_sync_check_overflow(&g_timestamp_sync_handle);
}
