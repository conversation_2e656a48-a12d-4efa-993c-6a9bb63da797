#include "timestamp_sync.h"
#include "tim.h"
#include "stm32f4xx_hal.h"

/* 全局时间戳同步句柄 */
static timestamp_sync_handle_t g_timestamp_sync_handle = {0};

/* 外部定时器句柄声明 */
extern TIM_HandleTypeDef htim3;
extern TIM_HandleTypeDef htim4;

/**
 * @brief 初始化时间戳管理器
 */
timestamp_sync_ret_t timestamp_manager_init(void)
{
    timestamp_sync_config_t config = {
        .timer_freq_hz = 1000000U,      /* 1MHz定时器频率 */
        .sample_rate_hz = 6400U,        /* 6.4kHz采样率 */
        .fft_length = 1024U,            /* 1024点FFT */
        .nominal_freq_hz = 50.0f        /* 50Hz标称频率 */
    };

    timestamp_sync_ret_t ret = timestamp_sync_init(&g_timestamp_sync_handle, &config);
    
    if (ret == TIMESTAMP_SYNC_OK) {
        /* 启动TIM3输入捕获中断 */
        if (HAL_TIM_IC_Start_IT(&htim3, TIM_CHANNEL_1) != HAL_OK) {
            return TIMESTAMP_SYNC_ERROR;
        }
        if (HAL_TIM_IC_Start_IT(&htim3, TIM_CHANNEL_2) != HAL_OK) {
            return TIMESTAMP_SYNC_ERROR;
        }
        
        /* 启动TIM4输入捕获中断（备用） */
        if (HAL_TIM_IC_Start_IT(&htim4, TIM_CHANNEL_1) != HAL_OK) {
            return TIMESTAMP_SYNC_ERROR;
        }
        if (HAL_TIM_IC_Start_IT(&htim4, TIM_CHANNEL_2) != HAL_OK) {
            return TIMESTAMP_SYNC_ERROR;
        }
    }

    return ret;
}

/**
 * @brief 获取时间戳管理器句柄
 */
timestamp_sync_handle_t* timestamp_manager_get_handle(void)
{
    return &g_timestamp_sync_handle;
}

/**
 * @brief 获取当前定时器CNT值
 */
uint32_t timestamp_manager_get_timer_cnt(void)
{
    return __HAL_TIM_GET_COUNTER(&htim3);
}

/**
 * @brief TIM3输入捕获中断回调函数（替换原有的回调）
 */
void timestamp_manager_tim3_ic_callback(TIM_HandleTypeDef *htim)
{
    if (htim->Instance == TIM3) {
        uint32_t capture_value = 0;
        zero_cross_edge_t edge;
        
        if (htim->Channel == HAL_TIM_ACTIVE_CHANNEL_1) {
            /* 上升沿捕获 */
            capture_value = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_1);
            edge = ZERO_CROSS_RISING;
            
            /* 调用时间戳同步回调 */
            timestamp_sync_zero_cross_callback(&g_timestamp_sync_handle, capture_value, edge);
        }
        else if (htim->Channel == HAL_TIM_ACTIVE_CHANNEL_2) {
            /* 下降沿捕获 */
            capture_value = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_2);
            edge = ZERO_CROSS_FALLING;
            
            /* 调用时间戳同步回调 */
            timestamp_sync_zero_cross_callback(&g_timestamp_sync_handle, capture_value, edge);
        }
    }
}

/**
 * @brief TIM4输入捕获中断回调函数（备用通道）
 */
void timestamp_manager_tim4_ic_callback(TIM_HandleTypeDef *htim)
{
    if (htim->Instance == TIM4) {
        uint32_t capture_value = 0;
        zero_cross_edge_t edge;
        
        if (htim->Channel == HAL_TIM_ACTIVE_CHANNEL_1) {
            /* 上升沿捕获 */
            capture_value = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_1);
            edge = ZERO_CROSS_RISING;
            
            /* 调用时间戳同步回调 */
            timestamp_sync_zero_cross_callback(&g_timestamp_sync_handle, capture_value, edge);
        }
        else if (htim->Channel == HAL_TIM_ACTIVE_CHANNEL_2) {
            /* 下降沿捕获 */
            capture_value = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_2);
            edge = ZERO_CROSS_FALLING;
            
            /* 调用时间戳同步回调 */
            timestamp_sync_zero_cross_callback(&g_timestamp_sync_handle, capture_value, edge);
        }
    }
}

/**
 * @brief 为AD7606数据绑定时间戳（供AD7606驱动调用）
 */
timestamp_sync_ret_t timestamp_manager_bind_adc_data(const int16_t adc_data[8],
                                                    ad7606_timestamped_data_t *timestamped_data)
{
    if (!g_timestamp_sync_handle.initialized) {
        return TIMESTAMP_SYNC_ERROR;
    }
    
    /* 获取当前定时器CNT值作为采样起始时间 */
    uint32_t current_timer_cnt = timestamp_manager_get_timer_cnt();
    
    /* 绑定ADC数据和时间戳 */
    timestamp_sync_ret_t ret = timestamp_sync_bind_adc_data(&g_timestamp_sync_handle, 
                                                           adc_data, 
                                                           timestamped_data);
    
    if (ret == TIMESTAMP_SYNC_OK) {
        /* 更新采样起始时间戳 */
        timestamped_data->timestamp.t_sample0 = current_timer_cnt;
    }
    
    return ret;
}

/**
 * @brief 获取当前频率测量结果
 */
timestamp_sync_ret_t timestamp_manager_get_frequency(frequency_measurement_t *measurement)
{
    if (!g_timestamp_sync_handle.initialized) {
        return TIMESTAMP_SYNC_ERROR;
    }
    
    return timestamp_sync_calculate_frequency(&g_timestamp_sync_handle, measurement);
}

/**
 * @brief 计算相位修正参数
 */
timestamp_sync_ret_t timestamp_manager_calculate_phase_correction(uint32_t t_sample0,
                                                                 uint32_t t_zero,
                                                                 phase_correction_t *correction)
{
    if (!g_timestamp_sync_handle.initialized) {
        return TIMESTAMP_SYNC_ERROR;
    }
    
    return timestamp_sync_calculate_phase_correction(&g_timestamp_sync_handle, 
                                                    t_sample0, 
                                                    t_zero, 
                                                    correction);
}

/**
 * @brief 应用相位修正到FFT结果
 */
timestamp_sync_ret_t timestamp_manager_apply_phase_correction(float *fft_complex_data,
                                                             uint16_t fft_length,
                                                             const phase_correction_t *correction)
{
    return timestamp_sync_apply_phase_correction(fft_complex_data, fft_length, correction);
}

/**
 * @brief 获取当前时间戳信息
 */
timestamp_sync_ret_t timestamp_manager_get_current_timestamp(timestamp_sync_data_t *timestamp)
{
    if (!g_timestamp_sync_handle.initialized) {
        return TIMESTAMP_SYNC_ERROR;
    }
    
    return timestamp_sync_get_current_timestamp(&g_timestamp_sync_handle, timestamp);
}
