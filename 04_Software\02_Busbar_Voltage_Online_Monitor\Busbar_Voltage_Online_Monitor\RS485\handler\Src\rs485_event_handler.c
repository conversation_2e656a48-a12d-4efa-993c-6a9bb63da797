/******************************************************************************
 * @file rs485_event_handler.c
 * @brief RS485事件处理模块实现
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-25
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 本模块处理RS485通信事件并管理Modbus寄存器
 * 
 * @par dependencies
 * FreeRTOS, modbus_rtu
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/
//******************************** Includes *********************************//
#include "rs485_event_handler.h"
//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define MY_MAX_DELAY    0xFFFFFFFF   /* 最大延时时间              */
#define MY_MAX_PRIORITY 56           /* 最大优先级               */
#define REG_LEN         2            /* 32位寄存器分割为16位寄存器时的长度 */

#define CHECK_PARAMETERS(p_private, p_event) \
    do { \
        if ((NULL == (p_private)) || (NULL == (p_event))) { \
            RS485_EVENT_LOG_ERROR("p_private or p_event is NULL, file: %s, line: %d", __FILE__, __LINE__); \
            return RS485_HANDLER_ERROR; \
        } \
    } while (0)
#define  CHECK_REG_PLACE(reg_place) \
    do { \
        if ((reg_place >= REG_QUANTITY) || (reg_place < 0)) { \
            RS485_EVENT_LOG_ERROR("reg_place error, file: %s, line: %d", __FILE__, __LINE__); \
            return RS485_HANDLER_ERROR; \
        } \
    } while (0)
//******************************** Defines **********************************//

//******************************** 类型定义 *********************************//
/**
 * @brief 寄存器存储位置与Modbus地址映射结构体
 */
typedef struct
{
    reg_storage_place_t reg_place;            /* 内部寄存器存储位置/索引 */
    uint16_t            modbus_start_reg_adr; /* Modbus起始寄存器地址 */
    uint16_t            modbus_end_reg_adr;   /* Modbus结束寄存器地址(对于多寄存器值) */
} reg_matchup_t;

/**
 * @brief RS485事件处理器私有数据结构
 */
typedef struct rs485_event_handler_private_data_t
{
    rs485_reg_t*               p_reg_data;           /* 寄存器数据数组指针 */
    Rs485_Device*              p_rs485_dev;          /* RS485设备指针 */
    rs485_ope_fun_t            *p_ope_fun;           /* 操作函数指针 */
    reg_matchup_t              *p_reg_matchup_table;  /* 寄存器匹配表指针 */
    rs485_event_handler_map_t  *p_event_handler_map;  /* RS485事件处理器映射表指针 */
    uint8_t                   event_mapping_count;   /* 事件映射表项数量 */
} rs485_event_handler_private_data_t;
//******************************** 类型定义 *********************************//

//******************************** 变量 ************************************//
/* 全局寄存器数据数组 */
static rs485_reg_t g_rs485_reg[REG_QUANTITY] = {0};

/* 全局寄存器匹配表 */
static reg_matchup_t g_reg_matchup_table[REG_QUANTITY] = {0};
/* RS485事件处理器映射表 */
static rs485_event_handler_map_t g_rs485_event_handler_map[RS485_EVENT_QUANTITY]; 
/* RS485事件处理器私有数据 */
static rs485_event_handler_private_data_t g_rs485_event_data = 
{
    .p_reg_data          = g_rs485_reg,
    .p_rs485_dev         = NULL,
    .p_reg_matchup_table = g_reg_matchup_table,
    .p_event_handler_map = g_rs485_event_handler_map,
};

/* RS485事件处理器实例 */
static rs485_event_handler_t g_rs485_event =
{
    .inited                    = false,
    .rs485_event_handle_thread = NULL,
    .rs485_output_msg_thread   = NULL,
    .rs485_event_handle_queue  = NULL,
    .rs485_event_handle_timer  = NULL,
    .p_os_interfac             = NULL, 
    .p_sys_config              = NULL,
    .private_data              = &g_rs485_event_data,
};
//******************************** 变量 ************************************//

//******************************** 前向声明 ********************************//
static rs485_handler_ret_t save_reg_value(rs485_event_handler_private_data_t *p_private,
                                         uint16_t reg_place);

static rs485_handler_ret_t process_reg_value(rs485_event_handler_private_data_t *p_private,
                                           uint16_t reg_place);
static uint16_t rs485_get_reg_place(uint16_t reg_adr);
static void rs485_event_handle_thread(void *argument);
static void rs485_output_msg_thread(void *argument);
static rs485_handler_ret_t rs485_reg_value_init(rs485_event_handler_t *phandler);
//******************************** 前向声明 ********************************//

//******************************** 函数 ************************************//



//******************************** 事件处理函数 ********************************//
/******************************************************************************
 * @brief 保存寄存器值到flash存储器
 * 
 * @param reg_place 寄存器存储位置/索引
 * 
 * @return rs485_handler_ret_t 状态码
 *****************************************************************************/
// rs485_handler_ret_t rs485_save_reg_value(uint16_t reg_place)
// {
//     rs485_event_handler_private_data_t *p_private = g_rs485_event.private_data;
    
//     /* 0. 检查参数是否有效 */
//     if(NULL == p_private)
//     {
//         RS485_EVENT_LOG_ERROR("p_private is NULL, file: %s, line: %d", __FILE__, __LINE__);
//         return RS485_HANDLER_ERROR;
//     }
//     if(reg_place >= REG_QUANTITY)
//     {
//         RS485_EVENT_LOG_ERROR("reg_place error, file: %s, line: %d", __FILE__, __LINE__);
//         return RS485_HANDLER_ERROR;
//     }
    
//     flash_event_t flash_event;
    
//     /* 1. 根据寄存器位置保存寄存器数据 */
//     switch(reg_place)
//     {
//         /* 1.3 保存设备地址寄存器数据 */
//         case DEV_ADR_REG:
//         {
//             flash_event.type = SAVE_DEV_ADR_EVENT;
//             flash_event.dev_adr = 
//                         (uint32_t)p_private->p_reg_data[reg_place].reg_data.u8_data;
//             p_private->p_rs485_dev->Adr = 
//                         (uint8_t)p_private->p_reg_data[reg_place].reg_data.u8_data;
//         }break;
        
//         /* 1.4 不存在的寄存器 */
//         default:
//         {
//             RS485_EVENT_LOG_ERROR("reg num error, file: %s, line: %d", __FILE__, __LINE__);
//             return RS485_HANDLER_ERROR;
//         }
//     }
    
//     /* 2. 发送闪存保存事件 */
//     if(FLASH_HANDLER_OK != flash_event_send(&flash_event))
//     {
//         return RS485_HANDLER_ERROR;
//     }
    
//     return RS485_HANDLER_OK;
// }

/******************************************************************************
 * @brief 处理寄存器值(特定寄存器的特殊处理)
 * 
 * @param p_private 私有数据指针
 * @param reg_place 寄存器存储位置/索引
 * 
 * @return rs485_handler_ret_t 状态码
 *****************************************************************************/
static rs485_handler_ret_t rs485_process_reg_value(
                                rs485_event_handler_private_data_t *p_private,
                                                            uint16_t reg_place)
{
    /* 0. 检查参数是否有效 */
    if(NULL == p_private)
    {
        RS485_EVENT_LOG_ERROR("p_private is NULL, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_ERROR;
    }
    /* 1. 根据寄存器位置处理寄存器数据 */
    /* 可读可写寄存器，保存到flash */
    if(READABLE_WRITEABLE == p_private->p_reg_data[reg_place].property)
    {
        if(DEV_ADR_REG == reg_place)
        {
            p_private->p_rs485_dev->Adr = 
                        (uint8_t)p_private->p_reg_data[reg_place].reg_data.u8_data;
        }
        return p_private->p_ope_fun->rs485_save_reg_value(reg_place, 
                                    &p_private->p_reg_data[reg_place].reg_data);
    }
    /* 可写寄存器，不保存到flash */
    else if(WRITEABLE_NO_SAVE == p_private->p_reg_data[reg_place].property)
    {

    }
    else
    {
        RS485_EVENT_LOG_ERROR("reg_place error, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_ERROR;
    }
    
    return RS485_HANDLER_OK;
}

/******************************************************************************
 * @brief 处理采集数据输入事件
 * 
 * @param reg_place 寄存器存储位置
 * @param p_event 事件指针
 * 
 * @return rs485_handler_ret_t 状态码
 *****************************************************************************/
static rs485_handler_ret_t rs485_handle_collect_data_input(void *phandler,
                                                    rs485_event_t *p_event)
{
    rs485_event_handler_private_data_t *p_private = phandler;
    
    /* 检查参数 */
    CHECK_PARAMETERS(p_private, p_event);
    CHECK_REG_PLACE(p_event->reg_num);
    /* 更新寄存器数据 */
    p_private->p_reg_data[p_event->reg_num].reg_data = p_event->reg_data;
    memcpy(&p_private->p_reg_data[p_event->reg_num].reg_data,
           &p_event->reg_data,
           sizeof(rs485_reg_data_t));
    return RS485_HANDLER_OK;
}

/******************************************************************************
 * @brief 处理读取寄存器数据事件
 * 
 * @param reg_place 寄存器存储位置
 * @param p_event 事件指针
 * 
 * @return rs485_handler_ret_t 状态码
 *****************************************************************************/
static rs485_handler_ret_t rs485_handle_read_reg_data( void *phandler, 
                                                rs485_event_t *p_event) 
{
    rs485_event_handler_private_data_t *p_private = phandler;
    uint16_t reg_place = rs485_get_reg_place(p_event->reg_num);
    /* 检查参数 */
    CHECK_PARAMETERS(p_private, p_event);
    
    /* 32位或浮点型寄存器将被分割成两个16位寄存器 */
    if(REG_LEN == p_private->p_reg_data[reg_place].reg_stru.reg_len)
    {
        /* 检查是否读取分割的第一个寄存器 */
        if(p_private->p_reg_matchup_table[reg_place].modbus_start_reg_adr == p_event->reg_num)
        {
            p_event->read_reg_data_callback(
                p_private->p_reg_data[reg_place].reg_data.u32_data >> 16, 
                p_event->read_data);       
        }
        /* 检查是否读取分割的第二个寄存器 */
        else if(p_private->p_reg_matchup_table[reg_place].modbus_end_reg_adr == p_event->reg_num)
        {
            p_event->read_reg_data_callback(
                p_private->p_reg_data[reg_place].reg_data.u32_data & 0x0000FFFF, 
                p_event->read_data);
        }
        else
        {
            RS485_EVENT_LOG_ERROR("reg num error: [%d], file: %s, line: %d",
                  p_event->reg_num, __FILE__, __LINE__);
            return RS485_HANDLER_ERROR;
        }
    }
    /* 8位或16位寄存器，直接读取 */
    else
    {
        p_event->read_reg_data_callback(
            p_private->p_reg_data[reg_place].reg_data.u16_data, 
            p_event->read_data); 
    }
    
    return RS485_HANDLER_OK;
}

/******************************************************************************
 * @brief 处理写入寄存器数据事件
 * 
 * @param reg_place 寄存器存储位置
 * @param p_event 事件指针
 * 
 * @return rs485_handler_ret_t 状态码
 *****************************************************************************/
static rs485_handler_ret_t rs485_handle_write_reg_data( void *phandler,
                                                 rs485_event_t *p_event)
{
    rs485_event_handler_private_data_t *p_private = phandler;
    uint16_t reg_place = rs485_get_reg_place(p_event->reg_num);
    /* 检查参数 */
//    CHECK_PARAMETERS(p_private, p_event);
    
    if((READABLE_WRITEABLE == p_private->p_reg_data[reg_place].property) ||
       (WRITEABLE_NO_SAVE == p_private->p_reg_data[reg_place].property))
    {
        /* 8位或16位寄存器，写入一次 */
        if((RGE_8BIT == p_private->p_reg_data[reg_place].data_bit) ||
           (RGE_16BIT == p_private->p_reg_data[reg_place].data_bit))
        {
            p_private->p_reg_data[reg_place].reg_data = p_event->reg_data;
            return rs485_process_reg_value(p_private, reg_place);
        }
        /* 32位或浮点型寄存器，需要写入两次 */
        else if((RGE_32BIT == p_private->p_reg_data[reg_place].data_bit) ||
                (REG_FLOAT == p_private->p_reg_data[reg_place].data_bit))
        {
            if(p_private->p_reg_matchup_table[reg_place].modbus_start_reg_adr == p_event->reg_num)
            {
                p_private->p_reg_data[reg_place].reg_data.u32_data = 
                                        (p_event->reg_data.u16_data << 16);
                return RS485_HANDLER_OK;
            }
            else if(p_private->p_reg_matchup_table[reg_place].modbus_end_reg_adr == p_event->reg_num)
            {
                p_private->p_reg_data[reg_place].reg_data.u32_data |= 
                                        p_event->reg_data.u16_data;
                return rs485_process_reg_value(p_private, reg_place);
            }
        }
    }
    else
    {
        RS485_EVENT_LOG_ERROR("reg %d property error, can't write, file: %s, line: %d", 
              reg_place, __FILE__, __LINE__);
    }
    
    return RS485_HANDLER_ERROR;
}
//******************************** 事件处理函数 ********************************//

/******************************************************************************
 * @brief 处理RS485事件
 * 
 * @param phandle 处理器句柄
 * @param reg_place 寄存器位置
 * @param pevent 事件指针
 * 
 * @return rs485_handler_ret_t 状态码
 *****************************************************************************/
static rs485_handler_ret_t process_rs485_event(rs485_event_handler_t *phandle, rs485_event_t *pevent)
 {
    RS485_EVENT_LOG_DEUBG("process_rs485_event start");
    
    if(NULL == pevent)
    {
        RS485_EVENT_LOG_ERROR("event is NULL");
        return RS485_HANDLER_ERRORRESOURCE;
    }
    
    rs485_event_handler_private_data_t *p_private_data = phandle->private_data;
    
    /* 查找注册的处理函数 */
    for(uint8_t i = 0; i < p_private_data->event_mapping_count; i++)
    {
        if(p_private_data->p_event_handler_map[i].event_type == pevent->event_type)
        {
            /* 检查函数是否注册 */
            if(NULL != p_private_data->p_event_handler_map[i].handler)
            {
                /* 执行函数 */
                return p_private_data->p_event_handler_map[i].handler(p_private_data, pevent);
            }
            else
            {
                RS485_EVENT_LOG_ERROR("process_rs485_event handler not register,event_type:%d file:%s,line:%d", 
                      pevent->event_type, __FILE__, __LINE__);
                return RS485_HANDLER_ERRORRESOURCE;
            }
        }
    }
    
    RS485_EVENT_LOG_ERROR("process_rs485_event not find handler,event_type:%d", pevent->event_type);
    return RS485_HANDLER_ERRORRESOURCE;
}

/******************************************************************************
 * @brief RS485事件处理线程
 * 
 * @param argument 线程参数(指向rs485_event_handler_t的指针)
 *****************************************************************************/
static void rs485_event_handle_thread(void *argument)
{
    rs485_event_handler_t *p_handler = (rs485_event_handler_t *)argument;
    rs485_event_handler_private_data_t *pdata = p_handler->private_data;
    rs485_event_t event;
    uint16_t reg_place = 0;
    if (RS485_HANDLER_OK != rs485_reg_value_init(&g_rs485_event))
	{
		RS485_EVENT_LOG_ERROR("rs485_reg_value_init error, file: %s, line: %d", __FILE__, __LINE__);
        return ;
	}
    while(1)
    {
        if (RS485_RTOS_PDTRUE == g_rs485_event.p_os_interfac->rtos_queue_receive(
            p_handler->rs485_event_handle_queue, &event, MY_MAX_DELAY))
        {
            if(INVALID_REG_ADRESS == reg_place)
            {
                RS485_EVENT_LOG_ERROR("invalid reg num error: [%d], file: %s, line: %d",
                      event.reg_num, __FILE__, __LINE__);
            }
            else
            {
                if(RS485_HANDLER_OK != process_rs485_event(p_handler, &event))
                {
                    RS485_EVENT_LOG_ERROR("process_rs485_event error, file: %s, line: %d", __FILE__, __LINE__);
                }
            }
        }
    }
}

/******************************************************************************
 * @brief RS485输出消息线程，用于Modbus RTU通信
 * 
 * @param argument 线程参数(指向rs485_event_handler_t的指针)
 *****************************************************************************/
static void rs485_output_msg_thread(void *argument)
{
    rs485_event_handler_t *p_handler = (rs485_event_handler_t *)argument;
    rs485_event_handler_private_data_t *pdata = p_handler->private_data;
    
    while(1)
    {
        if(COMPLETE == Modbus_RTU_cmd_Recv(pdata->p_rs485_dev, MY_MAX_DELAY))
        {
            Modbus_rtu_Reply(pdata->p_rs485_dev);
        }
    }
}

/******************************************************************************
 * @brief 从flash存储器初始化寄存器值
 * 
 * @return int8_t 状态码(0：成功，负值：错误)
 *****************************************************************************/
static rs485_handler_ret_t rs485_reg_value_init(rs485_event_handler_t *phandler)
{
    if(NULL == phandler)
    {
        RS485_EVENT_LOG_ERROR("phandler is NULL, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_ERRORPARAMETER;
    }
    rs485_event_handler_private_data_t *p_private = phandler->private_data;
    rs485_reg_t reg_value; 
    if(RS485_HANDLER_OK != p_private->p_ope_fun->rs485_read_reg_value(DEV_ADR_REG, &reg_value.reg_data))
    {
        return RS485_HANDLER_ERROR;
    }
    g_rs485_event_data.p_reg_data[DEV_ADR_REG].reg_data.u8_data = reg_value.reg_data.u8_data;
    g_rs485_event_data.p_rs485_dev->Adr = (uint8_t)reg_value.reg_data.u8_data;
    
    return RS485_HANDLER_OK;
}

/******************************************************************************
 * @brief 初始化RS485事件处理器
 * 
 * @param p_sys_config 系统配置指针
 * 
 * @return rs485_handler_ret_t 状态码
 *****************************************************************************/
/******************************************************************************
 * @brief 初始化RS485事件处理器
 * 
 * @param pinput 输入参数结构体，包含必要的系统配置和接口
 * 
 * @return rs485_handler_ret_t 状态码
 *****************************************************************************/
rs485_handler_ret_t rs485_event_handler_init(rs485_handler_input_t *pinput)
{
    rs485_handler_ret_t ret_code = RS485_HANDLER_OK;
    
    /* 0. 检查输入参数是否为空 */
    if (NULL == pinput)
    {
        RS485_EVENT_LOG_ERROR("pinput is NULL, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_ERRORPARAMETER;
    }
    
    /* 1. 检查必要的接口和配置是否为空 */
    if ((NULL == pinput->p_sys_config) || 
        (NULL == pinput->p_os_interface) ||
        (NULL == pinput->p_ope_fun))
    {
        RS485_EVENT_LOG_ERROR("One of required interfaces is NULL, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_ERRORPARAMETER;
    }
    
    /* 2. 检查参数有效性 */
    if ((0 == pinput->p_sys_config->rs485_event_thread_stack_size) ||
        (pinput->p_sys_config->rs485_event_thread_proirity > (MY_MAX_PRIORITY - 1)) ||
        (0 == pinput->p_sys_config->rs485_output_msg_thread_stack_size) ||
        (pinput->p_sys_config->rs485_output_msg_thread_proirity > (MY_MAX_PRIORITY - 1)) ||
        (0 == pinput->p_sys_config->rs485_event_queue_size))
    {
        RS485_EVENT_LOG_ERROR("Invalid configuration parameters, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_ERRORPARAMETER;
    }
    
    /* 3. 检查是否已初始化 */
    if (true == g_rs485_event.inited)
    {
        RS485_EVENT_LOG_ERROR("RS485 event handler already initialized, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_ERRORRESOURCE;
    }
    
    /* 4. 配置全局句柄 */
    g_rs485_event.p_sys_config = pinput->p_sys_config;
    g_rs485_event.p_os_interfac = pinput->p_os_interface;
    g_rs485_event_data.p_ope_fun = pinput->p_ope_fun;

    
    /* 5. 创建事件队列 */
    if (RS485_RTOS_PDTRUE != g_rs485_event.p_os_interfac->rtos_queue_create(
        &g_rs485_event.rs485_event_handle_queue,
        pinput->p_sys_config->rs485_event_queue_size,
        sizeof(rs485_event_t)))
    {
        RS485_EVENT_LOG_ERROR("Failed to create rs485 event queue, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_ERROR;
    }
    
    /* 6. 创建事件处理线程 */
    if (RS485_RTOS_PDTRUE != g_rs485_event.p_os_interfac->rtos_task_create(
        rs485_event_handle_thread,
        "rs485_event_handle_thread",
        pinput->p_sys_config->rs485_event_thread_stack_size,
        &g_rs485_event,
        pinput->p_sys_config->rs485_event_thread_proirity,
        &g_rs485_event.rs485_event_handle_thread))
    {
        /* 释放已分配资源 */
        g_rs485_event.p_os_interfac->rtos_queue_delete(g_rs485_event.rs485_event_handle_queue);
        RS485_EVENT_LOG_ERROR("Failed to create rs485 event thread, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_ERROR;
    }
    
    /* 7. 创建输出消息线程 */
    if (RS485_RTOS_PDTRUE != g_rs485_event.p_os_interfac->rtos_task_create(
        rs485_output_msg_thread,
        "rs485_output_msg_thread",
        pinput->p_sys_config->rs485_output_msg_thread_stack_size,
        &g_rs485_event,
        pinput->p_sys_config->rs485_output_msg_thread_proirity,
        &g_rs485_event.rs485_output_msg_thread))
    {
        /* 释放已分配资源 */
        g_rs485_event.p_os_interfac->rtos_queue_delete(g_rs485_event.rs485_event_handle_queue);
        g_rs485_event.p_os_interfac->rtos_task_delete(g_rs485_event.rs485_event_handle_thread);
        RS485_EVENT_LOG_ERROR("Failed to create rs485 output message thread, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_ERROR;
    }
    
    /* 8. 初始化RS485设备 */
    ADDRS485DEVICE();
    g_rs485_event_data.p_rs485_dev = get_rs485_dev("rs485_slave_dev");
    
    if (NULL == g_rs485_event_data.p_rs485_dev)
    {
        /* 释放已分配资源 */
        g_rs485_event.p_os_interfac->rtos_queue_delete(g_rs485_event.rs485_event_handle_queue);
        g_rs485_event.p_os_interfac->rtos_task_delete(g_rs485_event.rs485_event_handle_thread);
        g_rs485_event.p_os_interfac->rtos_task_delete(g_rs485_event.rs485_output_msg_thread);
        RS485_EVENT_LOG_ERROR("Failed to get rs485 device, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_ERROR;
    }
    
    if (RS485_DEV_OK != g_rs485_event_data.p_rs485_dev->Rs485DeviceInit(g_rs485_event_data.p_rs485_dev))
    {
        /* 释放已分配资源 */
        g_rs485_event.p_os_interfac->rtos_queue_delete(g_rs485_event.rs485_event_handle_queue);
        g_rs485_event.p_os_interfac->rtos_task_delete(g_rs485_event.rs485_event_handle_thread);
        g_rs485_event.p_os_interfac->rtos_task_delete(g_rs485_event.rs485_output_msg_thread);
        RS485_EVENT_LOG_ERROR("Failed to initialize rs485 device, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_ERROR;
    }
    
    /* 9. 设置私有数据 */
    g_rs485_event.private_data = &g_rs485_event_data;
    
    /* 10. 注册事件处理函数 */
    if (RS485_HANDLER_OK != rs485_event_fun_init())
    {
        /* 释放已分配资源 */
        g_rs485_event.p_os_interfac->rtos_queue_delete(g_rs485_event.rs485_event_handle_queue);
        g_rs485_event.p_os_interfac->rtos_task_delete(g_rs485_event.rs485_event_handle_thread);
        g_rs485_event.p_os_interfac->rtos_task_delete(g_rs485_event.rs485_output_msg_thread);
        RS485_EVENT_LOG_ERROR("Failed to register rs485 event handlers, file: %s, line: %d", __FILE__, __LINE__);
        return RS485_HANDLER_ERROR;
    }
    
    /* 11. 初始化寄存器值 */
//    if (RS485_HANDLER_OK != rs485_reg_value_init(&g_rs485_event))
//    {
//        /* 释放已分配资源 */
//        g_rs485_event.p_os_interfac->rtos_queue_delete(g_rs485_event.rs485_event_handle_queue);
//        g_rs485_event.p_os_interfac->rtos_task_delete(g_rs485_event.rs485_event_handle_thread);
//        g_rs485_event.p_os_interfac->rtos_task_delete(g_rs485_event.rs485_output_msg_thread);
//        RS485_EVENT_LOG_ERROR("Failed to initialize register values, file: %s, line: %d", __FILE__, __LINE__);
//        return RS485_HANDLER_ERROR;
//    }
    
    /* 12. 设置初始化标志并初始化Modbus */
    g_rs485_event.inited = true;
    modbus_init();
    
    RS485_EVENT_LOG_DEUBG("RS485 event handler initialized successfully");
    return RS485_HANDLER_OK;
}

/******************************************************************************
 * @brief 发送事件到RS485事件队列
 * 
 * @param event 要发送的RS485事件
 * 
 * @return rs485_handler_ret_t 状态码
 *****************************************************************************/
rs485_handler_ret_t rs485_event_handle_send(rs485_event_t event)
{
    if(false == g_rs485_event.inited)
    {
        return RS485_HANDLER_ERRORRESOURCE;
    }
    
    if (RS485_RTOS_PDTRUE != g_rs485_event.p_os_interfac->rtos_queue_send(
            g_rs485_event.rs485_event_handle_queue, &event, MY_MAX_DELAY))
    {
        return RS485_HANDLER_ERROR;
    }
    
    return RS485_HANDLER_OK;
}

/******************************************************************************
 * @brief 注册定时器回调函数
 * 
 * @param timer_callbcakfun 定时器回调函数
 * @param timerperiod 定时器周期（以tick为单位）
 * 
 * @return int8_t 状态码（0：成功，负值：错误）
 *****************************************************************************/
int8_t register_timer(void (*timer_callbcakfun)(void*),
                     uint32_t timerperiod)
{
    if(false == g_rs485_event.inited)
    {
        return -1;
    }
    
    
    if( RS485_RTOS_PDTRUE== g_rs485_event.p_os_interfac->rtos_timer_create(
                                                    "rs485_event_handle_timer",
                                                    timerperiod,
                                                    NULL,
                                                    timer_callbcakfun,
                                                    &g_rs485_event.rs485_event_handle_timer
                                                ))
    {
        return -2;	
    }
                                                        
    if(NULL == g_rs485_event.rs485_event_handle_timer)
    {
        return -2;
    }
    
    return 0;
}

/******************************************************************************
 * @brief 启动定时器
 * 
 * @return int8_t 状态码（0：成功，负值：错误）
 *****************************************************************************/
int8_t timer_start(void)
{
    if(false == g_rs485_event.inited)
    {
        return -1;
    }
    
    if(RS485_RTOS_PDTRUE != g_rs485_event.p_os_interfac->rtos_timer_start(g_rs485_event.rs485_event_handle_timer, 0))
    {
        return -1;
    }
    
    return 0;
}

/******************************************************************************
 * @brief 停止定时器
 * 
 * @return int8_t 状态码（0：成功，负值：错误）
 *****************************************************************************/
int8_t timer_stop(void)
{
    if(false == g_rs485_event.inited)
    {
        return -1;
    }
    
    if(RS485_RTOS_PDTRUE != g_rs485_event.p_os_interfac->rtos_timer_stop(g_rs485_event.rs485_event_handle_timer, 0))
    {
        return -1;
    }
    
    return 0;
}

/******************************************************************************
 * @brief 在RS485寄存器表中注册寄存器消息
 * 
 * @param reg_adr 寄存器存储位置
 * @param reg_msg 寄存器信息
 * 
 * @return int8_t 状态码（0：成功，负值：错误）
 *****************************************************************************/
int8_t rs485_instance_reg_msg(reg_storage_place_t reg_adr, rs485_reg_t reg_msg)
{
    if(reg_adr >= REG_QUANTITY)
    {
        return -1;
    }
    
    g_rs485_reg[reg_adr] = reg_msg;
    g_reg_matchup_table[reg_adr].reg_place = reg_adr;
    g_reg_matchup_table[reg_adr].modbus_start_reg_adr = reg_msg.reg_stru.reg_num;
    g_reg_matchup_table[reg_adr].modbus_end_reg_adr = 
    reg_msg.reg_stru.reg_num + reg_msg.reg_stru.reg_len - 1;
        
    return 0;
}

/******************************************************************************
 * @brief 从Modbus寄存器地址获取寄存器存储位置
 * 
 * @param reg_adr Modbus寄存器地址
 * 
 * @return uint16_t 寄存器存储位置，如果未找到则返回INVALID_REG_ADRESS
 *****************************************************************************/
static uint16_t rs485_get_reg_place(uint16_t reg_adr)
{
    if(reg_adr >= INVALID_REG_ADRESS)
    {
        RS485_EVENT_LOG_ERROR("reg_adr error, file: %s, line: %d", __FILE__, __LINE__);
        return INVALID_REG_ADRESS;
    }
    
    for(uint16_t i = 0; i < REG_QUANTITY; i++)
    {
        if((reg_adr >= g_reg_matchup_table[i].modbus_start_reg_adr) &&
           (reg_adr <= g_reg_matchup_table[i].modbus_end_reg_adr))
        {
            return g_reg_matchup_table[i].reg_place;
        }
    }
    
    return INVALID_REG_ADRESS;
}

// 添加rs485_event_fun_init函数定义
rs485_handler_ret_t rs485_event_fun_init(void)
{
    g_rs485_event_data.event_mapping_count = 0;
    
    // 注册采集数据输入事件处理函数
    g_rs485_event_data.p_event_handler_map[g_rs485_event_data.event_mapping_count].event_type = COLLECT_DATA_INPUT_EVENT;
    g_rs485_event_data.p_event_handler_map[g_rs485_event_data.event_mapping_count].handler = rs485_handle_collect_data_input;
    g_rs485_event_data.event_mapping_count++;
    
    // 注册读取寄存器数据事件处理函数
    g_rs485_event_data.p_event_handler_map[g_rs485_event_data.event_mapping_count].event_type = READ_REG_DATA_EVENT;
    g_rs485_event_data.p_event_handler_map[g_rs485_event_data.event_mapping_count].handler = rs485_handle_read_reg_data;
    g_rs485_event_data.event_mapping_count++;
    
    // 注册写入寄存器数据事件处理函数
    g_rs485_event_data.p_event_handler_map[g_rs485_event_data.event_mapping_count].event_type = WRITE_REG_DATA_EVENT;
    g_rs485_event_data.p_event_handler_map[g_rs485_event_data.event_mapping_count].handler = rs485_handle_write_reg_data;
    g_rs485_event_data.event_mapping_count++;
    
    return RS485_HANDLER_OK;
}
//******************************** 函数 ************************************//