#include <stdio.h>


#include "FreeRTOS.h"
#include "task.h"
#include "queue.h"
#include "semphr.h"
#include "timers.h"
#include "cmsis_os.h"

#include "mcu_flash_driver.h"
#include "flash_event_handler.h"
#include "rs485_event_handler.h"

#define MAX_PRIORITY    56
#define DEFAULT_DEV_ADR 1
#define FLASH_NO_WRITE 0xFFFFFFFF
#define SEND_QUEUE_TIMEOUT_MS 5

#define RTOS_QUEUE_CREATE \
        g_flash_event_handler.private_data->p_os_interface->rtos_queue_create
#define RTOS_QUEUE_DELETE \
        g_flash_event_handler.private_data->p_os_interface->rtos_queue_delete
#define RTOS_QUEUE_SEND \
        g_flash_event_handler.private_data->p_os_interface->rtos_queue_send
#define RTOS_QUEUE_RECEIVE \
        g_flash_event_handler.private_data->p_os_interface->rtos_queue_receive
#define RTOS_TASK_CREATE \
        g_flash_event_handler.private_data->p_os_interface->rtos_task_create
typedef struct flash_event_handler_private_data_t
{
    bool                         inited;
    flash_event_mapping_t       *p_event_mapping;
    uint8_t                      event_mapping_count;
    flash_os_interface_t        *p_os_interface;
    internal_flash_store_t      *p_flash_store;
}flash_event_handler_private_data_t;

static flash_event_mapping_t g_flash_event_mapping[FLASH_EVENT_QUANTITY];   /* 事件映射表 */

// 全局句柄
static flash_event_handler_t g_flash_event_handler = 
{
    .flash_event_thread = NULL,
    .flash_event_queue  = NULL,
    .private_data       = NULL,
};

static flash_event_handler_private_data_t g_flash_handler_private_data = 
{
    .inited              = false,
    .p_event_mapping     = g_flash_event_mapping,
    .event_mapping_count = 0,
    .p_os_interface      = NULL,
    .p_flash_store       = NULL,
};

/******************************************************************************
 * @brief 事件处理函数注册
 * 
 * @param  type                   事件类型
 * @param  handler                事件处理函数
 * 
 * @return flash_handler_ret_t 
 * return FLASH_HANDLER_OK           注册成功
 * return FLASH_HANDLER_ERROR        注册失败
 * return FLASH_HANDLER_ERRORNOMEMORY 内存不足
 * return FLASH_HANDLER_ERRORRESOURCE 资源错误
 * 
 * @note 1. 注册事件处理函数
 *      2. 注册的事件处理函数会被存储在事件映射表中，用于后续事件处理
 *      3. 事件处理函数的数量不能超过FLASH_EVENT_QUANTITY
 *****************************************************************************/
flash_handler_ret_t flash_register_handler(flash_event_type_t type, flash_event_cb_t handler)
{
    log_i("flash_register_handler start");
    /* 1.检查全局句柄是否挂载 */
    if(NULL == g_flash_event_handler.private_data) 
    {
        return FLASH_HANDLER_ERRORRESOURCE;
    }
    /* 2.检查输入参数是否有效 */
    if ((type >= FLASH_EVENT_QUANTITY) || (handler == NULL)) 
    {
        log_e("flash_register_handler input parameter error,file:%s,line:%d",
                                                      __FILE__, __LINE__);
        return FLASH_HANDLER_ERRORNOMEMORY;
    }
    /* 3.注册事件处理函数 */
    flash_event_handler_private_data_t *p_private_data = 
                (flash_event_handler_private_data_t *)g_flash_event_handler.private_data;
    p_private_data->p_event_mapping[type].event_type = type;
    p_private_data->p_event_mapping[type].handler = handler;
    p_private_data->event_mapping_count++;
    log_i("flash_register_handler end");
    return FLASH_HANDLER_OK;
}

/******************************************************************************
 * @brief 事件处理函数
 * 
 * @param  phandle                处理句柄
 * @param  pevent                 事件
 * 
 * @return flash_handler_ret_t
 * 
 * @note 1. 根据事件类型调用对应的处理函数
 *****************************************************************************/
static flash_handler_ret_t process_flash_event(flash_event_handler_t *phandle, 
                                               flash_event_t *pevent)
{
    log_i("process_flash_event start");
    if(NULL == pevent)
    {
        log_e("event is NULL");
        return FLASH_HANDLER_ERRORRESOURCE;
    }
    
    flash_event_handler_private_data_t *p_private_data = 
                                (flash_event_handler_private_data_t *)phandle->private_data;
    
    /* 查找注册的处理函数 */
    for(uint16_t i = 0; i < p_private_data->event_mapping_count; i++)
    {
        if(p_private_data->p_event_mapping[i].event_type == pevent->type)
        {
            /* 检查函数是否注册 */
            if(NULL != p_private_data->p_event_mapping[i].handler)
            {
                /* 执行函数 */
                return p_private_data->p_event_mapping[i].handler(phandle->private_data->p_flash_store, pevent);
            }
            else
            {
                log_e("process_flash_event handler not register,event_type:%d file:%s,line:%d", 
                                        pevent->type, __FILE__, __LINE__);
                return FLASH_HANDLER_ERRORRESOURCE;
            }
        }
    }
    log_e("process_flash_event not find handler,event_type:%d", pevent->type);
    return FLASH_HANDLER_ERRORRESOURCE;
}
/******************************************************************************
 * @brief Flash事件处理线程
 * 
 * @param  params                 线程参数
 * 
 * @return void
 * 
 * @note 1. 循环接收事件并处理
 *****************************************************************************/
static void flash_event_handler_thread(void  *params) 
{
    log_i("flash_event_handler_thread start");
    flash_event_handler_t* handle = params;
    flash_event_t event;
    
    while (1) {
        // 接收事件，使用外部传入的 RTOS 接口
        if (FLASH_RTOS_PDTRUE == handle->private_data->p_os_interface->rtos_queue_receive(
            handle->flash_event_queue, 
            &event, 
            portMAX_DELAY
        )) {
            if (FLASH_HANDLER_OK != process_flash_event(handle, &event)) {
                log_e("Failed to process flash event\r\n"); 
            }
        } else {
            log_e("Failed to receive flash event\r\n");
        }
    }
}
/******************************************************************************
 * @brief 初始化Flash事件处理
 * 
 * @param  p_config               配置参数
 * @param  p_os_interface         RTOS 操作接口
 * 
 * @return flash_handler_ret_t
 *****************************************************************************/
flash_handler_ret_t flash_event_handler_init(flash_event_input_t *p_input) 
{
    log_i("flash_event_handler_init start");
    // 检查输入参数
    if (
        (NULL == p_input)                                ||
        (0    == p_input->p_config->flash_event_thread_stack_size) ||
        (0    == p_input->p_config->flash_event_queue_size)        ||
        (p_input->p_config->flash_event_thread_proirity > MAX_PRIORITY)
    ) 
    {
        log_e("Invalid flash event system config or RTOS interface\r\n");
        return FLASH_HANDLER_ERROR;
    }
    if (true == g_flash_event_handler.private_data->inited) {
        return FLASH_HANDLER_ERROR;
    }
    
    // 初始化Flash存储
    g_flash_handler_private_data.p_flash_store = get_internal_flash_store();
    if (!g_flash_handler_private_data.p_flash_store) 
    {
        return FLASH_HANDLER_ERROR;
    }
    g_flash_handler_private_data.p_flash_store->internal_flash_init(g_flash_handler_private_data.p_flash_store);
    
    // 挂载接口
    g_flash_event_handler.private_data                 = &g_flash_handler_private_data;
    g_flash_event_handler.p_config                     = p_input->p_config;
    g_flash_event_handler.pf_flash_event_cb_init       = p_input->pf_flash_event_cb_init;
    g_flash_event_handler.private_data->p_os_interface = p_input->pos_interface;           // 挂载 RTOS 接口

    // 创建事件队列，使用外部传入的 RTOS 接口
    if (FLASH_RTOS_PDFALSE == RTOS_QUEUE_CREATE(
        &g_flash_event_handler.flash_event_queue, 
        g_flash_event_handler.p_config->flash_event_queue_size, 
        sizeof(flash_event_t)
    )) 
    {
        log_e("Failed to create flash event queue\r\n");
        return FLASH_HANDLER_ERROR;
    }
    
    // 注册事件处理函数
    if (FLASH_HANDLER_OK != flash_event_register_cb_init()) {
        log_e("Failed to register handlers\r\n");
        RTOS_QUEUE_DELETE(g_flash_event_handler.flash_event_queue);
        return FLASH_HANDLER_ERROR;
    }
    
    // 创建事件处理线程，使用外部传入的 RTOS 接口
    if (FLASH_RTOS_PDFALSE == RTOS_TASK_CREATE(
        flash_event_handler_thread,
        "flash_event_handler_thread",
        g_flash_event_handler.p_config->flash_event_thread_stack_size,
        &g_flash_event_handler,
        g_flash_event_handler.p_config->flash_event_thread_proirity,
        &g_flash_event_handler.flash_event_thread
    )) {
        RTOS_QUEUE_DELETE(g_flash_event_handler.flash_event_queue);
        return FLASH_HANDLER_ERROR;
    }
    if(FLASH_HANDLER_OK != g_flash_event_handler.pf_flash_event_cb_init())
    {
        log_e("Failed to flash_event_cb_init\r\n");
        return FLASH_HANDLER_ERROR;
    }
    // 标记初始化完成
    g_flash_handler_private_data.inited = true;
    
    log_i("flash_event_handler_init end");
    return FLASH_HANDLER_OK;
}

/******************************************************************************
 * @brief 发送Flash事件
 * 
 * @param  event                  事件
 * 
 * @return flash_handler_ret_t
 *****************************************************************************/
flash_handler_ret_t flash_event_send(flash_event_t* event)
{
    log_i("flash_event_send start");
    // 检查是否初始化
    if (true != g_flash_event_handler.private_data->inited) {
        log_e("flash_event_send handler not inited,file:%s,line:%d",
            __FILE__, __LINE__);
        return FLASH_HANDLER_ERROR;
    }
    
    // 发送事件，使用外部传入的 RTOS 接口
    if (FLASH_RTOS_PDFALSE == RTOS_QUEUE_SEND(
        g_flash_event_handler.flash_event_queue, 
        event, 
        SEND_QUEUE_TIMEOUT_MS
    )) {
        log_e("flash_event_send send data error,file:%s,line:%d",
            __FILE__, __LINE__);
        return FLASH_HANDLER_ERROR;
    }
    log_i("flash_event_send end");
    return FLASH_HANDLER_OK;
}


