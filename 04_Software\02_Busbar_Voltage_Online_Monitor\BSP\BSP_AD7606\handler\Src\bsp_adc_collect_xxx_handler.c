/******************************************************************************
 * @file bsp_adc_collect_xxx_handler.c
 * @brief 
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-22
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 
 * @par dependencies
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/

//******************************** Includes *********************************//
#include "bsp_adc_collect_xxx_handler.h"
#include <stdio.h>
//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define ADC_BUFFER_SIZE        (384)
#define MY_MAX_DELAY          (0xFFFFFFFF)
#define RTOS_QUEUE_CREAT      p_handler_inst->p_adc_collect_handler_os_interface->rtos_queue_create
#define RTOS_QUEUE_DELETE     p_handler_inst->p_adc_collect_handler_os_interface->rtos_queue_delete
#define RTOS_SEMAPHORE_CREAT  p_handler_inst->p_adc_collect_handler_os_interface->rtos_semphorebinary_create
#define RTOS_MUTEX_CREAT      p_handler_inst->p_adc_collect_handler_os_interface->rtos_mutex_create
#define RTOS_SEMAPHORE_DELETE p_handler_inst->p_adc_collect_handler_os_interface->rtos_semphore_delete
#define RTOS_TASK_CREAT       p_handler_inst->p_adc_collect_handler_os_interface->rtos_task_create
#define RTOS_GET_TASK_HANDLE  p_handler_inst->p_adc_collect_handler_os_interface->rtos_task_get_handle
#define ADC_COLLECT_EVENT_QUEUE_SIZE (128)
typedef enum
{
    ADC_COLLECT_NOT_INITED = 0,
    ADC_COLLECT_INITED     = 1,
}inited_status_t;

//******************************** Defines **********************************//

//******************************** Variable *********************************//

typedef struct
{
    uint8_t                     adc_data[16];
}ad7606_collect_data_t;

//adc采集私有数据声明
typedef struct adc_collect_handler_private_data_t 
{
    inited_status_t         inited_status;
    adc_collect_set_range_t sampling_range;
    ring_buff_t             ring_buff;    
}adc_collect_handler_private_data_t;

static ad7606_collect_data_t g_ad7606_collect_data_buffer[ADC_BUFFER_SIZE];

static bsp_adc_collect_xxx_handler_t *gp_adc_collect_xxx_handler = NULL;
//******************************** Variable *********************************//


//******************************** Functions ********************************//

void adc_collect_set_event_thread(void *argument);

/******************************************************************************
 * @brief 将adc采集处理句柄挂载到全局指针变量
 * 
 * @param  p_handler adc采集句柄       
 * 
 *****************************************************************************/
static void _mount_handler(bsp_adc_collect_xxx_handler_t *p_handler)
{
    ADC_COLLECT_LOG_DEUBG("mount bsp_adc_collect_xxx_handler_t");
    if(NULL == p_handler)
    {
        ADC_COLLECT_LOG_ERROR("mount bsp_adc_collect_xxx_handler_t failed, file: %s, line: %d",\
                                                            __FILE__, __LINE__);
        return;
    }
    gp_adc_collect_xxx_handler = p_handler;
}

static int8_t __adc_collect_event_thread_notify(void)
{
//    ADC_COLLECT_LOG_IRQ("__adc_collect_event_thread_notify start");
    /* 0.检查adc采集全局句柄指针是否已挂载 */
    if(NULL == gp_adc_collect_xxx_handler)
    {
        ADC_COLLECT_LOG_IRQ("__adc_collect_event_thread_notify failed, gp_adc_collect_xxx_handler\
                                    is NULL,file: %s, line: %d", __FILE__, __LINE__);       
        return -1;
    }
    /* 1.检查是否完成初始化*/
    if(ADC_COLLECT_INITED!= gp_adc_collect_xxx_handler->p_private_data->inited_status)
    {
        ADC_COLLECT_LOG_IRQ("__adc_collect_event_thread_notify failed, handler is not inited,\
                                            file: %s, line: %d", __FILE__, __LINE__);
        return -1;
    }
    /* 2.通知事件线程 */
    if(ADC_COLLECT_RTOS_PDFALSE == gp_adc_collect_xxx_handler->\
        p_adc_collect_handler_os_interface->rtos_task_notifiy_give_fromisr(
                            gp_adc_collect_xxx_handler->adc_collect_event_thread))
    {
        ADC_COLLECT_LOG_IRQ("__adc_collect_event_thread_notify failed, rtos_task_notifiy_give_fromisr\
                                    failed, file: %s, line: %d", __FILE__, __LINE__);
        return -1;
    }
//    ADC_COLLECT_LOG_IRQ("__adc_collect_event_thread_notify success");
    return 0;
}
#ifdef ADC_COLLECT_USE_INTERRUPT
/******************************************************************************
 * @brief 中断回调函数
 * 
 * @param  params           
 * 
 *****************************************************************************/
static int8_t adc_collect_irq_callback(void *params)
{
//	static uint32_t timer_count = 0;
    /* 0. 检查输入参数是否有效 */
    if(NULL == params)
    {
        ADC_COLLECT_LOG_IRQ(  "gp_adc_collect_xxx_handler is NULL, file: %s, line: %d",\
                                                        __FILE__, __LINE__);
        return -1;
    }
    bsp_adc_collect_xxx_handler_t *p_handler_inst = (bsp_adc_collect_xxx_handler_t *)params;
    /* 1.检查是否完成初始化*/
    if(ADC_COLLECT_NOT_INITED == p_handler_inst->\
                                        p_private_data->inited_status)
    {
        ADC_COLLECT_LOG_IRQ("adc_collect_irq_callback failed, handler is not \
                                inited, file: %s, line: %d", __FILE__, __LINE__);
        return -1;
    }
    /* 2.启动采集*/
    if(AD7606_OK != p_handler_inst->p_bsp_ad7606_instance->pfstartconvst(
                                        p_handler_inst->p_bsp_ad7606_instance)
     )
     {
         ADC_COLLECT_LOG_IRQ("adc_collect_irq_callback failed, start convst failed,\
                                     file: %s, line: %d", __FILE__, __LINE__);
        return -1;
     }
//    ADC_COLLECT_LOG_IRQ("adc_collect_irq_callback success, \
//                                file: %s, line: %d", __FILE__, __LINE__);
//	 timer_count++;
//	 ADC_COLLECT_LOG_INFO(" adc timer enter count:%d",timer_count );
    return 0;
}
#endif //END OF ADC_COLLECT_USE_INTERRUPT
/******************************************************************************
 * @brief adc采集处理句柄初始化函数
 * 
 * @param  p_handler_inst 采集句柄  
 * 
 * @return adc_collect_xxx_ret_code_t 
 * @retval ADC_COLLCET_SUCCESS 成功
 * @retval ADC_COLLCET_ERROR  失败
 * @retval ADC_COLLCET_ERRORSOURCE  失败
 *****************************************************************************/
static adc_collect_xxx_ret_code_t bsp_adc_collect_xxx_handler_init(
                                bsp_adc_collect_xxx_handler_t *p_handler_inst
                                                                    )
{
    int8_t ret;
    uint32_t init_time = 0;
    static ring_buff_input_t adc_collect_ring_buff_input = 
    {
        .buff_size  = sizeof(g_ad7606_collect_data_buffer) / sizeof(ad7606_collect_data_t),
        .item_size  = sizeof(ad7606_collect_data_t),
        .mode       = RING_BUFF_EXTEND_MODE,
        .pring_buff = g_ad7606_collect_data_buffer,
    };
    /* 1. 检查输入对象是否有效 */
    ADC_COLLECT_LOG_DEUBG("bsp_adc_collect_xxx_handler_init start");
    if(NULL == p_handler_inst)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_init failed, \
                                    file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERROR;
    }
    if(ADC_COLLECT_INITED == p_handler_inst->p_private_data->inited_status)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_init failed, handler instance is \
        already ADC_COLLECT_INITED, file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERROR;
    }
    /* 2.记录初始化时间 */
    init_time = p_handler_inst->p_system_timebase_interface->pfget_timetick_ms();
    /* 3. 创建事件队列、信号量、线程*/
#ifdef ADC_COLLECT_HANDLER_RTOS_SUPPORTING
#ifdef ADC_COLLECT_USE_INTERRUPT  
    /* 3.1 获取线程句柄 */
    if(ADC_COLLECT_RTOS_PDFALSE == RTOS_GET_TASK_HANDLE(
                                        &p_handler_inst->adc_collect_event_thread)
    )
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_init failed to get task handle,\
                                     file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
#endif //END OF ADC_COLLECT_USE_INTERRUPT
    /* 3.2 创建事件队列 */
    if((ADC_COLLECT_RTOS_PDFALSE == RTOS_QUEUE_CREAT( 
                &p_handler_inst->adc_collect_event_queue, 
                p_handler_inst->p_system_config->adc_collect_event_queue_size, 
                sizeof(adc_collect_xxx_output_event_t)))
#ifdef ADC_COLLECT_USE_INTERRUPT                                        
    || (ADC_COLLECT_RTOS_PDFALSE == RTOS_QUEUE_CREAT(
            &p_handler_inst->adc_collect_set_event_queue,
            p_handler_inst->p_system_config->adc_collect_set_event_queue_size,
            sizeof(adc_collect_xxx_set_event_t)))
#endif //END OF ADC_COLLECT_USE_INTERRUPT
     )
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_init failed to create event queue,\
                                     file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
#ifdef ADC_COLLECT_USE_INTERRUPT
    /* 3.3 创建信号量 */
    if( ADC_COLLECT_RTOS_PDFALSE == RTOS_SEMAPHORE_CREAT(
                                    &p_handler_inst->event_handler_semphore))
    {
        RTOS_QUEUE_DELETE(p_handler_inst->adc_collect_event_queue);
        RTOS_QUEUE_DELETE(p_handler_inst->adc_collect_set_event_queue);
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_init failed to create semphore,\
                                     file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
    /* 3.3.1 创建互斥量 */
    if( ADC_COLLECT_RTOS_PDFALSE == RTOS_MUTEX_CREAT(
                                    &p_handler_inst->adc_collect_handler_mutex))
    {
        RTOS_QUEUE_DELETE(p_handler_inst->adc_collect_event_queue);
        RTOS_QUEUE_DELETE(p_handler_inst->adc_collect_set_event_queue);
        RTOS_SEMAPHORE_DELETE(&p_handler_inst->event_handler_semphore);
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_init failed to create set_event_semphore,\
                                     file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
#endif
#ifdef ADC_COLLECT_USE_INTERRUPT
    /* 3.4 创建线程 */
    if(ADC_COLLECT_RTOS_PDFALSE == RTOS_TASK_CREAT( 
                    adc_collect_set_event_thread,
                    "adc_collect_set_event_thread", 
                    p_handler_inst->p_system_config->adc_collect_set_event_thread_stack_size,
                    p_handler_inst,
                    p_handler_inst->p_system_config->adc_collect_set_event_thread_proirity,
                    &p_handler_inst->adc_collect_set_event_thread))
    {
        RTOS_QUEUE_DELETE(p_handler_inst->adc_collect_event_queue);
        RTOS_QUEUE_DELETE(p_handler_inst->adc_collect_set_event_queue);
        RTOS_SEMAPHORE_DELETE(&p_handler_inst->event_handler_semphore);
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_init failed to create task,\
                                     file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
#endif //END OF ADC_COLLECT_USE_INTERRUPT

#endif //END OF ADC_COLLECT_HANDLER_RTOS_SUPPORTING

#ifdef ADC_COLLECT_USE_INTERRUPT
    /* 4. 初始化及使能中断 */
    p_handler_inst->p_system_interrupt_interface->pfinit();
    p_handler_inst->p_system_interrupt_interface->pfenable_peripheral_clock();
    p_handler_inst->p_system_interrupt_interface->pfenable_irq();
    /* 4.1 挂载中断回调函数到外部接口 */
    (*p_handler_inst->p_adc_collect_callback_fun->timer_irq_callbackfun) = 
                                                        adc_collect_irq_callback;
    (*p_handler_inst->p_adc_collect_callback_fun->p_params) = p_handler_inst;
#endif //END OF ADC_COLLECT_USE_INTERRUPT
    /* 5.实例化环形缓冲区 */
    ret = ring_buff_instance(
                                            &adc_collect_ring_buff_input,
                           p_handler_inst->pring_buff_dynamic_allocation,
                                p_handler_inst->pring_buff_rtos_critical,
                              &p_handler_inst->p_private_data->ring_buff
    );
    if(ret)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_init failed to instance ring buffer,\
                                     file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
    /* 6. 实例化ad7606驱动 */
    ret = bsp_ad7606_driver_instance(
                                    "AD7606",
                                    p_handler_inst->p_ad7606_driver_input,
                                &p_handler_inst->p_private_data->ring_buff,
                                         __adc_collect_event_thread_notify,
                                    p_handler_inst->p_bsp_ad7606_instance
                                    );
    if(ret)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_init failed to instance \
            ad7606 driver ret: %d, file: %s, line: %d", ret, __FILE__, __LINE__);
        return ADC_COLLCET_ERROR;
    }
    /* 6.1 执行ad7606的初始化*/
    ret = p_handler_inst->p_bsp_ad7606_instance->\
                                pfinit(p_handler_inst->p_bsp_ad7606_instance);
    if(ret)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_init failed to execute \
            ad7606 init ret: %d, file: %s, line: %d", ret, __FILE__, __LINE__);
        return ADC_COLLCET_ERROR;
    }
    ret = p_handler_inst->p_bsp_ad7606_instance->\
                            pfset_sampling_range(p_handler_inst->p_bsp_ad7606_instance,
                                                (ad7606_sampling_range_t)DEFAULT_ADC_RANGE);
    /* 7. 输出初始化时间 */
    init_time = p_handler_inst->p_system_timebase_interface->\
                                                pfget_timetick_ms() - init_time;
    ADC_COLLECT_LOG_DEUBG("bsp_adc_collect_xxx_handler_init success, init_time: [%d] ms", init_time);
    return ADC_COLLCET_OK;
}

/******************************************************************************
 * @brief 实例化handler句柄
 * 
 * @param  p_handler  adc采集句柄      
 * @param  p_input_arg 输入成员     
 * 
 * @return adc_collect_xxx_ret_code_t 
 * @retval ADC_COLLCET_OK 成功
 * @retval ADC_COLLCET_ERRORSOURCE 失败
 *****************************************************************************/
static adc_collect_xxx_ret_code_t bsp_adc_collect_xxx_handler_inst(
                                bsp_adc_collect_xxx_handler_t       *p_handler,
                                adc_collect_handler_input_all_arg_t *p_input_arg
                                            )
{
    adc_collect_xxx_ret_code_t ret_code = ADC_COLLCET_OK;
    ADC_COLLECT_LOG_DEUBG("bsp_adc_collect_xxx_handler_inst start");
    /* 1.检查输入对象是否有效 */
    if(NULL == p_handler)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_inst failed, \
                                    file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
    /* 1.1 检查输入对象内容是否有效 */
    if( (NULL == p_input_arg) || 
        (NULL == p_input_arg->p_ad7606_driver_input))
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_inst failed input argument is NULL,\
                                     file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
    if(
        (NULL == p_input_arg->p_ad7606_driver_input->pgpio_interface)     ||
        (NULL == p_input_arg->p_ad7606_driver_input->pspi_interface)      ||
        (NULL == p_input_arg->p_ad7606_driver_input->pdynamic_allocation) ||
        (NULL == p_input_arg->p_ad7606_driver_input->pbusy_irq_callback)
       )
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_inst failed ad7606 input argument\
                                is NULL,file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
#ifdef ADC_COLLECT_USE_INTERRUPT
    if(NULL == p_input_arg->p_system_interrupt_interface)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_inst failed system_interrupt_interface\
                                 is NULL,file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
    if(NULL == p_input_arg->p_adc_collect_callback_fun)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_inst failed adc_collect_callback_fun\
                                is NULL, file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
#endif //END OF ADC_COLLECT_USE_INTERRUPT
    if(NULL == p_input_arg->p_system_timebase_interface)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_inst failed \
                                        system_timebase_interface is NULL,\
                                        file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
    if(NULL == p_input_arg->p_adc_collect_handler_os_interface)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_inst failed \
                                    adc_collect_handler_os_interface is NULL,\
                                    file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
    if(NULL == p_input_arg->pring_buff_rtos_critical)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_inst failed \
                                    pring_buff_rtos_critical is NULL,\
                                    file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
    if(NULL == p_input_arg->pring_buff_dynamic_allocation)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_inst failed \
                                    pring_buff_dynamic_allocation is NULL,\
                                    file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
    if(NULL == p_input_arg->p_adc_collect_timer_collect)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_inst failed \
                                    p_adc_collect_timer_collect is NULL,\
                                    file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
    if((NULL == p_input_arg->p_system_config) ||
      (p_input_arg->p_system_config->adc_collect_set_event_thread_proirity 
                                            >= OS_TASK_PRIORITYMAX))
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_inst failed \
                                    p_system_config is NULL,\
                                    file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORSOURCE;
    }
    /* 2. 实例化handler句柄 */
    p_handler->p_ad7606_driver_input              = p_input_arg->p_ad7606_driver_input;
#ifdef ADC_COLLECT_USE_INTERRUPT
    p_handler->p_system_interrupt_interface       = p_input_arg->p_system_interrupt_interface;
    p_handler->p_adc_collect_callback_fun         = p_input_arg->p_adc_collect_callback_fun;
#endif //END OF ADC_COLLECT_USE_INTERRUPT
    p_handler->p_system_timebase_interface        = p_input_arg->p_system_timebase_interface;
    p_handler->p_adc_collect_handler_os_interface = p_input_arg->p_adc_collect_handler_os_interface;
    p_handler->pring_buff_rtos_critical           = p_input_arg->pring_buff_rtos_critical;
    p_handler->pring_buff_dynamic_allocation      = p_input_arg->pring_buff_dynamic_allocation;
    p_handler->p_adc_collect_timer_collect        = p_input_arg->p_adc_collect_timer_collect;
    p_handler->p_system_config                    = p_input_arg->p_system_config;
    /* 3. 初始化成员变量 */
    p_handler->pring_buff_rtos_critical->pf_rtos_critical_enter();
    ret_code =  bsp_adc_collect_xxx_handler_init(p_handler);
    
    if(ADC_COLLCET_OK!= ret_code)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_inst init failed ret code: [%d],\
                         file: %s, line: %d", ret_code, __FILE__, __LINE__);
        return ret_code;
    }
    /* 4. 设置私有数据初始化内容 */
    p_handler->p_private_data->sampling_range = DEFAULT_ADC_RANGE;
    p_handler->p_private_data->inited_status = ADC_COLLECT_INITED;
	p_handler->pring_buff_rtos_critical->pf_rtos_critical_exit();
    ADC_COLLECT_LOG_DEUBG("bsp_adc_collect_xxx_handler_inst end of success,");
    return ADC_COLLCET_OK;
}

/******************************************************************************
 * @brief adc_collect事件处理函数
 * 
 * @param  p_handler_inst  adc_collect句柄 
 * @param  p_event         adc_collect事件
 * 
 * @return adc_collect_xxx_ret_code_t 
 * @retval ADC_COLLCET_OK         成功
 * @retval ADC_COLLCET_ERROR      失败
 *****************************************************************************/
static adc_collect_xxx_ret_code_t process_adc_collect_xxx_event(
                                bsp_adc_collect_xxx_handler_t *p_handler_inst,
                                adc_collect_xxx_set_event_t       *p_event)
{
    ADC_COLLECT_LOG_DEUBG("process_adc_collect_xxx_event start");
    int8_t   ret        = 0;
    /* 1. 检查输入对象是否有效 */
    if(NULL == p_handler_inst)
    {
        ADC_COLLECT_LOG_ERROR("process_adc_collect_xxx_event failed, p_handler_inst is NULL,\
                                     file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERROR;
    }
    if(ADC_COLLECT_NOT_INITED == p_handler_inst->p_private_data->inited_status)
    {
        ADC_COLLECT_LOG_ERROR("process_adc_collect_xxx_event failed, handler not inited, \
                                    file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERROR;
    }
    /* 2. 根据事件类型进行处理 */
    switch(p_event->event_data)
    {
#ifdef ADC_COLLECT_USE_INTERRUPT
        case ADC_COLLECT_CLEAR_DATA_EVENT:
        {
            /* 2.1 清空ring buffer */
            /* 获取互斥量 */
            ret = p_handler_inst->p_adc_collect_handler_os_interface->\
                    rtos_semphore_take(p_handler_inst->adc_collect_handler_mutex, 
                                            MY_MAX_DELAY);
            if(ADC_COLLECT_RTOS_PDTRUE != ret)
            {
                ADC_COLLECT_LOG_ERROR("process_adc_collect_xxx_event failed, take mutex failed,\
                                     file: %s, line: %d", __FILE__, __LINE__);
            }
            else
            {
                /* 清空ring buffer */
                ret = p_handler_inst->p_private_data->ring_buff.ring_buff_reset(
                                        &p_handler_inst->p_private_data->ring_buff);
                if(RING_BUFF_OK != ret)
                {
                    ADC_COLLECT_LOG_ERROR("process_adc_collect_xxx_event failed, clear ring buffer failed,\
                                     file: %s, line: %d", __FILE__, __LINE__);
                    p_handler_inst->p_adc_collect_handler_os_interface->\
                        rtos_semphore_give(p_handler_inst->adc_collect_handler_mutex);
                    return ADC_COLLCET_ERROR;
                }
                /* 清空任务通知 */
                ret = p_handler_inst->p_adc_collect_handler_os_interface->\
                        rtos_task_notifiy_clear(p_handler_inst->adc_collect_event_thread);
                if(ADC_COLLECT_RTOS_PDTRUE != ret)
                {
                    ADC_COLLECT_LOG_ERROR("process_adc_collect_xxx_event failed, \
                        clear task notifiy failed,file: %s, line: %d", __FILE__, __LINE__);
                }
                p_handler_inst->p_adc_collect_handler_os_interface->\
                    rtos_semphore_give(p_handler_inst->adc_collect_handler_mutex);
                ADC_COLLECT_LOG_DEUBG("process_adc_collect_xxx_event clear data event success");
            }
        }break;
#else
        case ADC_COLLECT_DATA_READ_EVENT:
        {
            int16_t data[AD7606_CHANNEL_QUANTITY];
            float   AD7606_VOLTAGE_RANGE = 0;
           
            //计算采集的电压值
            //读取数据
            ret = p_handler_inst->p_bsp_ad7606_instance->pfread_data(   
                                    p_handler_inst->p_bsp_ad7606_instance, 
                                    data,
                                    (ad7606_channels_t)p_event->start_chennel,
                                    p_event->channels);
			 //启动ADC采集
			p_handler_inst->p_bsp_ad7606_instance->pfstartconvst(p_handler_inst->p_bsp_ad7606_instance);
            switch(p_handler_inst->p_private_data->sampling_range)
            {
                //以下采样范围的电压值为mv
                case ADC_COLLECT_RANGE_5V:
                {
                    AD7606_VOLTAGE_RANGE = 5000.0;
                }break;
                case ADC_COLLECT_RANGE_10V:
                {
                    AD7606_VOLTAGE_RANGE = 10000.0;
                }break;
                default:
                {
                    ADC_COLLECT_LOG_ERROR("process_adc_collect_xxx_event failed, unknown sampling range:[%d],\
                            file: %s, line: %d", p_handler_inst->p_private_data->sampling_range,\
                                                                            __FILE__, __LINE__);
                    return ADC_COLLCET_ERROR;
                }
            }
            for(uint8_t i = 0; i < p_event->channels; i++)
            {
                p_event->p_data[i] = (float)data[i] * AD7606_VOLTAGE_RANGE / AD7606_ADC_FS;
            }
            //计算回调函数执行时间
            time_stamp =    p_handler_inst->\
                        p_system_timebase_interface->pfget_timetick_ms();
            p_event->pf_event_callback_fun(p_event->p_input_pram);
            time_stamp = p_handler_inst->p_system_timebase_interface->\
                                            pfget_timetick_ms() - time_stamp;
            ADC_COLLECT_LOG_DEUBG("adc collect data read event callback fun execute \
                                                    time: %d ms", time_stamp);
        }break;
#endif //END OF ADC_COLLECT_USE_INTERRUPT
        case ADC_COLLECT_SET_RANGE:
        {
            
            ret = p_handler_inst->p_bsp_ad7606_instance->pfset_sampling_range(  
                                        p_handler_inst->p_bsp_ad7606_instance,
                                     (ad7606_sampling_range_t)p_event->range);
            p_handler_inst->p_private_data->sampling_range = p_event->range;
       
        }break;
        case ADC_COLLECT_SET_OVER_SAMPLING:
        {
            ret = p_handler_inst->p_bsp_ad7606_instance->pfset_sampling_mode(  
                                        p_handler_inst->p_bsp_ad7606_instance,
                                (ad7606_sampling_mode_t)p_event->ovrsampling);
        }break;
        default:
        {
            ADC_COLLECT_LOG_ERROR("process_adc_collect_xxx_event failed, unknown event type:[%d],\
                     file: %s, line: %d", p_event->event_data, __FILE__, __LINE__);
            return ADC_COLLCET_ERROR;
        }
    }
    ADC_COLLECT_LOG_DEUBG("process_adc_collect_xxx_event success, ret:[%d]", ret);
    return ADC_COLLCET_OK;
}

extern void set_pc9_low(void);
extern void set_pc9_high(void);
/******************************************************************************
 * @brief adc采集线程，队列输出数据
 * 
 * @param  argument         
 * 
 *****************************************************************************/
void adc_collect_event_thread(void *argument)
{
    ADC_COLLECT_LOG_DEUBG("adc_collect_event_thread start");
     /* 0. 定义相关变量 */
    adc_collect_handler_input_all_arg_t *p_input_arg = 
                                (adc_collect_handler_input_all_arg_t*)argument;
    bsp_adc_collect_xxx_handler_t       adc_collect_handler = {0};
    static adc_collect_handler_private_data_t  private_data = 
    {
        .inited_status = ADC_COLLECT_NOT_INITED
    };
    uint8_t                             ret_code;
    uint32_t                            time_stamp = 0;
    ad7606_collect_data_t               collect_data;
    bsp_ad7606_driver_t                 bsp_ad7606_inst;
    adc_collect_xxx_output_event_t      event = 
    {
        .start_chennel = ADC_COLLECT_CHANNEL_1,
        .channels      = VALID_ADC_CHANNEL_NUM
    };
    float                               AD7606_VOLTAGE_RANGE = 0;
     /* 1. 检查输入参数 */
    if(NULL == p_input_arg)
    {
        ADC_COLLECT_LOG_ERROR("adc_collect_event_thread input argument is NULL,\
                        file: %s, line: %d", __FILE__, __LINE__);
        return;
    }
    /* 2. 挂载变量到handler，实例化handler句柄 */
    adc_collect_handler.p_bsp_ad7606_instance  = &bsp_ad7606_inst;
    adc_collect_handler.p_private_data         = &private_data;
    /* 2.1 实例化handler句柄*/

    ret_code = bsp_adc_collect_xxx_handler_inst(
                                            &adc_collect_handler,
                                            p_input_arg
                                                );
    /* 2.2. 挂载handler */
    if(ADC_COLLCET_OK == ret_code)
    {
        _mount_handler(&adc_collect_handler);
    }
    else
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_xxx_handler_inst failed ret code: [%d],\
                     file: %s, line: %d",ret_code, __FILE__, __LINE__);
        return;
    }
//    MPU_Config((uint32_t*)&private_data.inited_status);
    /* 3. 启动定时器开始采集 */
    ret_code = adc_collect_handler.p_adc_collect_timer_collect->\
                                                pfstart_timer_collect();
    /* 4. 执行线程处理 */
    while (1)
    {
#ifdef ADC_COLLECT_USE_INTERRUPT
        /* 4.1 等待采集完成通知*/
        ret_code = adc_collect_handler.p_adc_collect_handler_os_interface->\
                    rtos_task_notifiy_take(MY_MAX_DELAY);
        if(ADC_COLLECT_RTOS_PDTRUE != ret_code)
        {
            ADC_COLLECT_LOG_ERROR("adc_collect_event_thread failed, wait for interrupt failed,\
                            file: %s, line: %d", __FILE__, __LINE__);
        }
        else
        {
            /* 获取互斥量 */
            adc_collect_handler.p_adc_collect_handler_os_interface->\
            rtos_semphore_take( adc_collect_handler.adc_collect_handler_mutex,
                                MY_MAX_DELAY);
            /* 4.2读取环形缓冲区数据，进行处理*/
            ret_code = adc_collect_handler.p_private_data->\
                                            ring_buff.pfring_buff_read( 
                        &adc_collect_handler.p_private_data->ring_buff,
                                                         &collect_data,
                                                                    1);
            adc_collect_handler.p_adc_collect_handler_os_interface->\
            /* 释放互斥量 */
            rtos_semphore_give( adc_collect_handler.adc_collect_handler_mutex);
           
            if(RING_BUFF_OK != ret_code)
             {
               ADC_COLLECT_LOG_ERROR("adc_collect_event_thread failed, read ring buffer failed,\
                            file: %s, line: %d", __FILE__, __LINE__);
             }
             else
             {
                time_stamp = adc_collect_handler.p_system_timebase_interface->pfget_timetick_ms();
                /* 4.3 获取电压量程 */
                switch(adc_collect_handler.p_private_data->sampling_range)
                {
                    //以下采样范围的电压值为mv
                    case ADC_COLLECT_RANGE_5V:
                    {
                        AD7606_VOLTAGE_RANGE = 5000.0;
                    }break;
                    case ADC_COLLECT_RANGE_10V:
                    {
                        AD7606_VOLTAGE_RANGE = 10000.0;
                    }break;
                    default:
                    {
                        ADC_COLLECT_LOG_ERROR("process_adc_collect_xxx_event failed, unknown sampling range:[%d],\
                                file: %s, line: %d", adc_collect_handler.p_private_data->sampling_range,
                                                                                __FILE__, __LINE__);
                    }
                }
                /* 4.4 计算采集的电压值*/
                for(uint8_t i = 0; i < VALID_ADC_CHANNEL_NUM; i++)
                {
                    event.data[i] = 
                    (float)((int16_t)((collect_data.adc_data[i*2] << 8) + collect_data.adc_data[i*2+1])) * \
                                                            AD7606_VOLTAGE_RANGE / AD7606_ADC_FS;
                }
                /* 4.5 发送数据到队列上*/
                if(ADC_COLLECT_RTOS_PDFALSE == adc_collect_handler.\
                            p_adc_collect_handler_os_interface->rtos_queue_send(
                                    adc_collect_handler.adc_collect_event_queue,
                                                                         &event,
                                                                              0)
                   )
                {
                    ADC_COLLECT_LOG_ERROR("adc_collect_event_thread failed, send event to queue failed,\
                            file: %s, line: %d", __FILE__, __LINE__);
                }
                time_stamp = adc_collect_handler.p_system_timebase_interface->\
                                            pfget_timetick_ms() - time_stamp;
                
                ADC_COLLECT_LOG_DEUBG("adc collect data read event callback fun execute \
                                                    time: %d ms", time_stamp);
             }
        }
        
#else
        /* 4.1 接收事件 */
        ret_code = adc_collect_handler.p_adc_collect_handler_os_interface->\
            rtos_queue_receive( adc_collect_handler.adc_collect_event_queue,
                                &event,
                                MY_MAX_DELAY);
        ADC_COLLECT_LOG_DEUBG("adc_collect_event_thread recv event ret code: [%d]", ret_code);
        if(ADC_COLLECT_INITED != adc_collect_handler.p_private_data->inited_status)
        {
            ADC_COLLECT_LOG_ERROR("adc_collect_event_thread failed, handler not inited,\
                            file: %s, line: %d", __FILE__, __LINE__);
        }
        /* 4.2 处理事件 */
        process_adc_collect_xxx_event(&adc_collect_handler, &event);
#endif
    }
    
}



#ifdef ADC_COLLECT_USE_INTERRUPT
void adc_collect_set_event_thread(void *argument)
{
    ADC_COLLECT_LOG_DEUBG("adc_collect_set_event_thread start");
    uint8_t ret_code;
    adc_collect_xxx_set_event_t event;
    bsp_adc_collect_xxx_handler_t *p_handler = (bsp_adc_collect_xxx_handler_t*)argument;
    /* 0. 检查输入参数 */
    if(NULL == argument)
    {
        ADC_COLLECT_LOG_ERROR("adc_collect_set_event_thread failed, input argument is NULL,\
                        file: %s, line: %d", __FILE__, __LINE__);
        return;
    }

    /* 1.检查handler是否已经初始化 */
    if( ADC_COLLECT_NOT_INITED == p_handler->p_private_data->inited_status)
    {
        ADC_COLLECT_LOG_ERROR("adc_collect_set_event_thread failed, handler not inited,\
                        file: %s, line: %d", __FILE__, __LINE__);
        return;
    }
    /* 2. 等待事件通知 */
    while(1)
    {
        /* 2.1 接收事件 */
        ret_code = p_handler->p_adc_collect_handler_os_interface->\
            rtos_queue_receive( p_handler->adc_collect_set_event_queue,
                                &event,
                                MY_MAX_DELAY);
        ADC_COLLECT_LOG_DEUBG("adc_collect_event_thread recv event ret code: [%d]", ret_code);
        /* 4.2 处理事件 */
        process_adc_collect_xxx_event(p_handler, &event);
    }
}
#endif //END OF ADC_COLLECT_USE_INTERRUPT

adc_collect_xxx_ret_code_t bsp_adc_collect_read_or_set(adc_collect_xxx_set_event_t* p_event)
{
    ADC_COLLECT_LOG_DEUBG("bsp_adc_collect_read_or_set start");
    /* 0.判断输入参数是否合法 */
    if(NULL == p_event)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_read_or_set failed, input argument is NULL,\
                        file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORPARAMETER;
    }
    
    /* 1.检查handler是否已经初始化 */
    if( ADC_COLLECT_NOT_INITED == 
        (gp_adc_collect_xxx_handler->p_private_data->inited_status))
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_read_or_set failed, handler not inited,\
                        file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERROR;
    }
    /* 2.发送事件到队列 */
    if(ADC_COLLECT_RTOS_PDFALSE ==
        (gp_adc_collect_xxx_handler->p_adc_collect_handler_os_interface->\
        rtos_queue_send(
#ifdef ADC_COLLECT_USE_INTERRUPT
                        gp_adc_collect_xxx_handler->adc_collect_set_event_queue,
#else
                        gp_adc_collect_xxx_handler->adc_collect_event_queue,
#endif //END OF ADC_COLLECT_USE_INTERRUPT
                        p_event,
                        MY_MAX_DELAY)))
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_read_or_set failed, send event to queue failed,\
                        file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERROR;
    }
    ADC_COLLECT_LOG_DEUBG("bsp_adc_collect_read_or_set end");
    return ADC_COLLCET_OK;
}

adc_collect_xxx_ret_code_t bsp_adc_collect_output_data(adc_collect_xxx_output_event_t* p_event)
{
    ADC_COLLECT_LOG_DEUBG("bsp_adc_collect_output_data start");
    /* 0.判断输入参数是否合法 */
    if(NULL == p_event)
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_output_data failed, input argument is NULL,\
                        file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERRORPARAMETER;
    }
    /* 1.检查handler是否已经初始化 */
    if( ADC_COLLECT_INITED != 
        (gp_adc_collect_xxx_handler->p_private_data->inited_status))
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_output_data failed, handler not inited,\
                        file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERROR;
    }
    /* 2.发送事件到队列 */
    if(ADC_COLLECT_RTOS_PDFALSE ==
        (gp_adc_collect_xxx_handler->p_adc_collect_handler_os_interface->\
         rtos_queue_receive(
                        gp_adc_collect_xxx_handler->adc_collect_event_queue,
                        p_event,
                        MY_MAX_DELAY)))
    {
        ADC_COLLECT_LOG_ERROR("bsp_adc_collect_output_data failed, recv event from queue failed,\
                        file: %s, line: %d", __FILE__, __LINE__);
        return ADC_COLLCET_ERROR;
    }
    ADC_COLLECT_LOG_DEUBG("bsp_adc_collect_output_data end");
    return ADC_COLLCET_OK;
}