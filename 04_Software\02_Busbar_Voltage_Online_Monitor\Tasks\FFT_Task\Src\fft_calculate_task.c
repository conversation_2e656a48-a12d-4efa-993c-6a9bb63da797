/******************************************************************************
 * @file fft_calculate_task.c
 * @brief 
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-18
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 
 * @par dependencies
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/
//******************************** Includes *********************************//
#include <stdio.h>
#include "fft_calculate_task.h"
//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define FFT_CALCU_OS_TASK_PRIORITYMAX   (56)
#define MY_MAX_DELAY    (uint32_t)0xffffffffUL
#define RTOS_QUEUE_CREATE     p_fft_calcu_handler->fft_calcu_os_interface->rtos_queue_create
#define RTOS_SEMAPHORE_CREATE p_fft_calcu_handler->fft_calcu_os_interface->rtos_semphorebinary_create
#define RTOS_TASK_CREATE      p_fft_calcu_handler->fft_calcu_os_interface->rtos_task_create
#define MY_PRIORIRTY  (24+2)


//使用复数FFT，使用实数FFT定义成0
#define CONFIG_USE_CFFT 1    //是否使用复数FFT
#define CONFIG_ADD_WINDOW 0  //是否加窗
#define FFT_USE_WINDOW_CALCU 1  //是否使用窗口计算
#define FFT_INPUT_USE_FIR 0     //是否使用低通滤波器

#define THD_MAX_COUNT   10    //计算谐波失真度时的谐波次数

#define MIN_FRE_HZ  45.0f
#define MAX_FRE_HZ  65.0f

/* 同步FFT计算相关定义 */

#define SYNC_TIMEOUT_MS     10   //同步等待超时时间

/* 事件组位定义 */
#define FFT_AC_READY_BIT         (1 << 0)  // AC电压数据准备完成
#define FFT_UA_READY_BIT         (1 << 1)  // UA相电压数据准备完成
#define FFT_UB_READY_BIT         (1 << 2)  // UB相电压数据准备完成
#define FFT_UC_READY_BIT         (1 << 3)  // UC相电压数据准备完成
#define FFT_SYNC_READY_BITS      (FFT_AC_READY_BIT | FFT_UA_READY_BIT | FFT_UB_READY_BIT | FFT_UC_READY_BIT)
#define FFT_SYNC_TIMEOUT_BIT     (1 << 4)  // 同步超时标志
#define FFT_SYNC_RESET_BIT       (1 << 5)  // 同步重置标志

/* 事件处理线程事件组位定义 */
#define FFT_AC_CALC_BIT          (1 << 6)  // AC电压FFT计算请求
#define FFT_UA_CALC_BIT          (1 << 7)  // UA相电压FFT计算请求
#define FFT_UB_CALC_BIT          (1 << 8)  // UB相电压FFT计算请求
#define FFT_UC_CALC_BIT          (1 << 9)  // UC相电压FFT计算请求
#define FFT_SYNC_CALC_BIT        (1 << 10) // 同步FFT计算请求
#define FFT_CLEAR_DATA_BIT       (1 << 11) // 清除数据请求
#define FFT_ALL_CALC_BITS        (FFT_AC_CALC_BIT | FFT_UA_CALC_BIT | FFT_UB_CALC_BIT | \
                                  FFT_UC_CALC_BIT | FFT_SYNC_CALC_BIT | FFT_CLEAR_DATA_BIT)



// 定义采样频率
#define SAMPLING_FREQUENCE  3200.0f
// 定义FFT采样长度
#define FFT_LENGTH          1024
// 定义ADC采样量程
#define ADC_FS              32767
// 定义栅栏补充量,将中心值前后10个点作为栅栏补偿
#define FENCE_VALUE         0
// 定义最小频率索引
#define MIN_FRE_INDEX       ((uint32_t)(MIN_FRE_HZ/(SAMPLING_FREQUENCE/FFT_LENGTH)))
// 定义最大频率索引 
#define MAX_FRE_INDEX       ((uint32_t)(MAX_FRE_HZ/(SAMPLING_FREQUENCE/FFT_LENGTH)))
//重叠采样点数
#define OVERLAP_SIZE        (512)



typedef enum
{
    FFT_CALCU_NOT_INITED = 0,
    FFT_CALCU_INITED     = 1,
}inited_status_t;
//******************************** Defines **********************************//

//******************************** Variable *********************************//
#if SYNC_FFT_ENABLE
/* 同步FFT计算结果结构 */
typedef struct sync_fft_result
{
    float ac_amplitude;          // AC电压幅值
    float ac_phase;              // AC电压相位
    float ac_frequency;          // AC电压频率
    float ua_amplitude;          // UA相电压幅值
    float ua_phase;              // UA相电压相位
    float ua_frency;             // UA相电压频率
    float ua_3rd_harmonic;       // UA相3次谐波幅值
    float ua_5th_harmonic;       // UA相5次谐波幅值
    float ua_7th_harmonic;       // UA相7次谐波幅值
    float ua_9th_harmonic;       // UA相9次谐波幅值
    float ub_amplitude;          // UB相电压幅值
    float ub_phase;              // UB相电压相位
    float ub_frency;             // UB相电压频率
    float ub_3rd_harmonic;       // UB相3次谐波幅值
    float ub_5th_harmonic;       // UB相5次谐波幅值
    float ub_7th_harmonic;       // UB相7次谐波幅值
    float ub_9th_harmonic;       // UB相9次谐波幅值
    float uc_amplitude;          // UC相电压幅值
    float uc_phase;              // UC相电压相位
    float uc_frency;             // UC相电压频率
    float uc_3rd_harmonic;       // UC相3次谐波幅值
    float uc_5th_harmonic;       // UC相5次谐波幅值
    float uc_7th_harmonic;       // UC相7次谐波幅值
    float uc_9th_harmonic;       // UC相9次谐波幅值
    float ua_ac_phase_diff;      // UA相对AC的相位差
    float ub_ac_phase_diff;      // UB相对AC的相位差
    float uc_ac_phase_diff;      // UC相对AC的相位差
    uint32_t timestamp;          // 时间戳
    uint8_t data_valid;          // 数据有效标志
}sync_fft_result_t;

/* 同步采样数据结构（使用事件组） */
typedef struct sync_data_status
{
    void *sync_event_group;      // 同步事件组句柄
    uint32_t ac_timestamp;       // AC电压数据时间戳
    uint32_t ua_timestamp;       // UA相电压数据时间戳
    uint32_t ub_timestamp;       // UB相电压数据时间戳
    uint32_t uc_timestamp;       // UC相电压数据时间戳
    uint32_t sync_start_time;    // 同步开始时间
    uint32_t sync_timeout_count; // 同步超时计数
}sync_data_status_t;
#endif /* SYNC_FFT_ENABLE */

// 定义一个结构体，用于存储FFT数据
typedef struct 
{
	// 如果使用复数FFT
	#if CONFIG_USE_CFFT
    float               fft_inputbuf[FFT_LENGTH]; // FFT输入缓冲区，用于接收数据
	// 否则
	#else
    float               fft_inputbuf[FFT_LENGTH];   // FFT输入缓冲区
	#endif
    fft_data_property_t data_property;              // 数据属性,用于区分采集到数据类型
    
    uint16_t            index;                      // 输入索引，用于表示当前采集数据的位置
    float               sampling_rate;              // 采样率
    float               now_frequency;              // 当前频率
    float               dc_component;               // 直流分量
    float               base_band_amp;              // 基频幅度
    float               base_band_phase;            // 基频相位
    float               thd;                        // 谐波失真度
    float               thd_arrt[10];               //计算THD的数组，最多计算10次谐波

    /* 时间戳相关信息 */
    uint32_t            capture_count;              // 过零点捕获计数
    uint32_t            t_zero;                     // 过零点时间戳
    uint32_t            t_sample0;                  // 采样起始时间戳
    uint32_t            frame_id;                   // 帧ID
    bool                timestamp_valid;            // 时间戳有效标志
}fft_calcu_data_t;

typedef struct fft_calcu_private_data_t
{
    inited_status_t  inited_status;
    fft_data_type_t  fft_data_type;
    fft_calcu_type_t fft_calcu_type;
    float            *pfft_output_buf;      // FFT输出缓冲区，用于存储FFT结果
#if !CONFIG_USE_CFFT
    float            *pfft_mag_buf;          // FFT输出缓冲区，用于存储幅度结果,rfft使用
#endif /* CONFIG_USE_CFFT */
    fft_calcu_data_t *pfft_data;
    window_type_t    frequecny_win;           // 计算频率时加的窗口类型
    window_type_t    base_band_amp_win;       // 计算基频幅度时加的窗口类型
    window_type_t    thd_win;                 // 计算THD时加的窗口类型
#if SYNC_FFT_ENABLE
    sync_data_status_t sync_status;           // 同步状态
    sync_fft_result_t  sync_result;           // 同步计算结果
    
#endif /* SYNC_FFT_ENABLE */
}fft_calcu_private_data_t;

static float g_fft_output_buf[FFT_LENGTH];  // FFT输出缓冲区，用于存储FFT结果
#if !CONFIG_USE_CFFT
static float g_fft_mag_buf[FFT_LENGTH];  // FFT输出缓冲区，用于存储幅度结果
#endif /* CONFIG_USE_CFFT */
static fft_calcu_data_t g_fft_data_buf[FFT_INPUT_QUANTITY];
static fft_calcu_handler_t *gp_fft_calcu_handler;

//******************************** Variable *********************************//

//************************** Function DECLARATION ***************************//
//以下为各种窗函数的生成函数
int Factorial(int num);//阶乘
float Besseli(float x);//0阶贝塞尔
float Kaiser(float beta, int n, float* win_out);//Kaiser窗
float FlattopWin(int n, float* win_out);//Flattop窗
float Hanning(int n, float* win_out);//Hanning窗
float Hamming(int n, float* win_out);//Hamming窗
float rectangle(int n, float* win_out);//矩形窗
void Window(float* input, float* win_in, int n);//加窗函数
// 计算栅栏补偿函数
float32_t calculate_fence_compensation(float32_t* output_mag, uint16_t central_value, uint16_t fence_value);
// 计算相位函数
float32_t calculate_phase_radian(float32_t* input_data, uint16_t frequence_index, float cmp_value);
int8_t fft_irq_callback_fun(void *arg);

#if SYNC_FFT_ENABLE
// 同步FFT计算相关函数
static fft_calcu_ret_t sync_fft_check_ready(fft_calcu_handler_t *p_handler);
static fft_calcu_ret_t sync_fft_calculate(fft_calcu_handler_t *p_handler);
static fft_calcu_ret_t sync_fft_send_result(fft_calcu_handler_t *p_handler);
static void sync_fft_reset_status(fft_calcu_handler_t *p_handler);
#endif /* SYNC_FFT_ENABLE */
//************************** Function DECLARATION ***************************//


//************************** Function IMPLEMENTATION ************************//

void fft_collection_data_thread(void *params)
{
    FFT_CALCU_LOG_DEUBG("fft_collection_data_thread start");
    /* 0. 定义相关变量 */
    fft_calcu_handler_t *phandler = (fft_calcu_handler_t*)params;
    fft_calcu_private_data_t *p_private_data = NULL;
    float collect_data_limit = 0;      
    fft_input_data_type_t input_data[FFT_INPUT_QUANTITY] = 
    {
        {
            .data_value    = 0,
        },
        {
            .data_value    = 0,
        }
    };
#if FFT_INPUT_USE_FIR
    static float lpf_output_data[FFT_LENGTH] = {0};
#endif
    /* 1.检查输入对象是否有效 */
    if(NULL == phandler)
    {
        FFT_CALCU_LOG_ERROR("input_data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return;
    }
    if(FFT_CALCU_INITED != phandler->private_data->inited_status)
    {
        FFT_CALCU_LOG_ERROR("fft_calcu_handler has not inited,file:%s,line:%d",__FILE__,__LINE__);
        return;
    }
    /* 1.1 变量初始化 */
    phandler->fft_get_data_interface->fft_get_collect_data_limit(&collect_data_limit);
    FFT_CALCU_LOG_DEUBG("collect_data_limit:%f",collect_data_limit);
    p_private_data = (fft_calcu_private_data_t*)phandler->private_data;
#ifdef FFT_CALCU_USING_IRQ
    // 4.2 启动定时器开启采集
    phandler->fft_timer_collect_interface->fft_start_timer_collect();
#endif /* FFT_CALCU_USING_IRQ */
    while(1)
    {
#ifdef FFT_CALCU_USING_IRQ
        /* 2. 等待信号量 */
        if(FFT_CALCU_RTOS_PDTRUE != phandler->fft_calcu_os_interface->\
        rtos_semphore_take(phandler->fft_start_collection_semaphore,MY_MAX_DELAY))
        {
            FFT_CALCU_LOG_ERROR("wait fft_start_collection_semaphore failed,file:%s,line:%d",
                                                                __FILE__,__LINE__);
        }

        else
        {
#endif /* FFT_CALCU_USING_IRQ */
        /* 3.获取FFT数据并进行处理 */
        if(FFT_CALCU_OK !=phandler->fft_get_data_interface->fft_get_collect_data(
                                                                        input_data,
                                                     p_private_data->fft_data_type)
        )
        {
            FFT_CALCU_LOG_ERROR("get fft data failed,file:%s,line:%d",__FILE__,__LINE__);
        }
        else
        {    
            for(uint8_t i = 0; i < FFT_INPUT_QUANTITY; i++)
            {
                /*4.检查数据类型是否一致*/
                if(p_private_data->pfft_data[i].data_property == input_data[i].data_property)
                {
                    /*  进入临界区保护数据写入 */
                    phandler->fft_calcu_critical_interface->rtos_enter_critical();
                    /* 4.1 检查数据是否超过限制*/
                    if(input_data[i].data_value > collect_data_limit)
                    {
                        /* 4.1.1 数据超过限制，检查数据,并更新限制*/
                        FFT_CALCU_LOG_DEUBG("input_data[%d] is over limit:%f,",
                                                    i,input_data[i].data_value);
                        phandler->fft_get_data_interface->\
                                    fft_check_data_valid(input_data[i].data_property,input_data[i].data_value);
                        phandler->fft_get_data_interface->\
                                    fft_get_collect_data_limit(&collect_data_limit);
                        /* 4.1.2 从头开始采集数据，同步采集，采集的索引都置为0 */
                        for(uint8_t j = 0; j < FFT_INPUT_QUANTITY; j++)
                        {
                            p_private_data->pfft_data[j].index = 0;
                        }
                        break;
                    }
                    /*5.将数据存入fft_inputbuf中*/
                    if(p_private_data->pfft_data[i].index < FFT_LENGTH)
                    {
                        
                        p_private_data->pfft_data[i].fft_inputbuf[p_private_data->pfft_data[i].index]
                        = input_data[i].data_value;

                        //数据全部采集完成时，再进行FFT计算
                        if((FFT_LENGTH - 1 == p_private_data->pfft_data[i].index))
                        {
                            /* 5.1 对采集数据进行滤波 */
#if FFT_INPUT_USE_FIR
                            phandler->plpf_fir->pflpf_iir(
                                                            phandler->plpf_fir,
                                     p_private_data->pfft_data[i].fft_inputbuf,
                                                            lpf_output_data,
                                                             FFT_LENGTH);
                            memcpy( p_private_data->pfft_data[i].fft_inputbuf,
                                    lpf_output_data,
                                    FFT_LENGTH*sizeof(float));
#endif /* FFT_INPUT_USE_FIR */

#if SYNC_FFT_ENABLE
                            /* 5.2 同步FFT处理 */
                            uint32_t current_time = phandler->fft_calcu_system_timebase_interface->pfget_timetick_ms();
                            uint32_t event_bit = 0;

                            /* 根据数据类型设置对应的事件位和时间戳 */
                            switch(p_private_data->pfft_data[i].data_property)
                            {
                                case FFT_INPUT_VOLTAGE_AC:
                                    p_private_data->sync_status.ac_timestamp = current_time;
                                    phandler->fft_calcu_os_interface->rtos_event_group_set_bits(
                                        p_private_data->sync_status.sync_event_group,
                                        FFT_AC_READY_BIT);
                                    event_bit = FFT_AC_CALC_BIT;
                                    FFT_CALCU_LOG_DEUBG("AC voltage data ready at %d ms", current_time);
                                    break;

                                case FFT_INPUT_VOLTAGE_UA:
                                    p_private_data->sync_status.ua_timestamp = current_time;
                                    phandler->fft_calcu_os_interface->rtos_event_group_set_bits(
                                        p_private_data->sync_status.sync_event_group,
                                        FFT_UA_READY_BIT);
                                    event_bit = FFT_UA_CALC_BIT;
                                    FFT_CALCU_LOG_DEUBG("UA voltage data ready at %d ms", current_time);
                                    break;

                                case FFT_INPUT_VOLTAGE_UB:
                                    p_private_data->sync_status.ub_timestamp = current_time;
                                    phandler->fft_calcu_os_interface->rtos_event_group_set_bits(
                                        p_private_data->sync_status.sync_event_group,
                                        FFT_UB_READY_BIT);
                                    event_bit = FFT_UB_CALC_BIT;
                                    FFT_CALCU_LOG_DEUBG("UB voltage data ready at %d ms", current_time);
                                    break;

                                case FFT_INPUT_VOLTAGE_UC:
                                    p_private_data->sync_status.uc_timestamp = current_time;
                                    phandler->fft_calcu_os_interface->rtos_event_group_set_bits(
                                        p_private_data->sync_status.sync_event_group,
                                        FFT_UC_READY_BIT);
                                    event_bit = FFT_UC_CALC_BIT;
                                    FFT_CALCU_LOG_DEUBG("UC voltage data ready at %d ms", current_time);
                                    break;

                                default:
                                    FFT_CALCU_LOG_ERROR("Unknown data property: %d", p_private_data->pfft_data[i].data_property);
                                    break;
                            }

                            /* 检查是否可以进行同步计算 */
                            if(FFT_CALCU_OK == sync_fft_check_ready(phandler))
                            {
                                /* 使用事件组发送同步计算通知 */
                                phandler->fft_calcu_os_interface->rtos_event_group_set_bits(
                                    phandler->fft_handler_event_group,
                                    FFT_SYNC_CALC_BIT);
                                FFT_CALCU_LOG_DEUBG("Sync FFT calculation triggered");
                            }
//                            else if(event_bit != 0)
//                            {
//                                /* 发送单独的FFT计算事件 */
//                                phandler->fft_calcu_os_interface->rtos_event_group_set_bits(
//                                    phandler->fft_handler_event_group,
//                                    event_bit);
//                                FFT_CALCU_LOG_DEUBG("Individual FFT calculation event set: 0x%x", event_bit);
//                            }
#else
                            /* 6.一次采集完成，通知计算线程*/
                            phandler->fft_calcu_os_interface->rtos_queue_send(
                                                    phandler->fft_finish_collection_queue,
                                                    &p_private_data->pfft_data[i].data_property,
                                                    MY_MAX_DELAY);
#endif /* SYNC_FFT_ENABLE */
                        }
                    }
                    else
                    {
#if CONFIG_USE_SLIDING_WINDOW
                        /* 将采集数据往前移动OVERLAP_SIZE个点*/
                        memmove(p_private_data->pfft_data[i].fft_inputbuf,
                                &p_private_data->pfft_data[i].fft_inputbuf[OVERLAP_SIZE],
                                (FFT_LENGTH - OVERLAP_SIZE) * sizeof(float));
                        /* 更新数据索引*/
                        p_private_data->pfft_data[i].index -= OVERLAP_SIZE;
#else
                        p_private_data->pfft_data[i].index = 0;
#endif /* CONFIG_USE_SLIDING_WINDOW */
                        p_private_data->pfft_data[i].fft_inputbuf[p_private_data->pfft_data[i].index]
                        = input_data[i].data_value;
                    }
                    p_private_data->pfft_data[i].index++;
                    phandler->fft_calcu_critical_interface->rtos_exit_critical();
                }
                else
                {
                    FFT_CALCU_LOG_ERROR("data_property is not same,file:%s,line:%d",
                                                            __FILE__,__LINE__);
                }
            }
        }
#ifdef FFT_CALCU_USING_IRQ
        }
#endif /* FFT_CALCU_USING_IRQ */
    }
}

fft_calcu_ret_t __fft_calcu_mount_handler(fft_calcu_handler_t *p_fft_calcu_handler)
{
    FFT_CALCU_LOG_DEUBG("__fft_calcu_mount_handler start");
    /* 1. 检查输入对象是否有效 */
    if(NULL == p_fft_calcu_handler)
    {
        FFT_CALCU_LOG_ERROR("input_data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERRORRESOURCE;
    }
    gp_fft_calcu_handler = p_fft_calcu_handler;
    return FFT_CALCU_OK;
}
/******************************************************************************
 * @brief 
 * 
 * @param  p_fft_calcu_handler
 * 
 * @return fft_calcu_ret_t 
 *****************************************************************************/
fft_calcu_ret_t fft_calcu_handler_init(fft_calcu_handler_t *p_fft_calcu_handler)
{
    FFT_CALCU_LOG_DEUBG("fft_calcu_handler_init start");
    /* 1. 检查输入对象是否有效 */
    if(NULL == p_fft_calcu_handler)
    {
        FFT_CALCU_LOG_ERROR("input_data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERRORRESOURCE;
    }
    if(FFT_CALCU_INITED == p_fft_calcu_handler->private_data->inited_status)
    {
        FFT_CALCU_LOG_ERROR("fft_calcu_handler has inited,file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
    /* 2. 创建事件队列、信号量、线程*/
    p_fft_calcu_handler->fft_calcu_os_interface->rtos_task_get_handle(
                            &p_fft_calcu_handler->fft_event_handler_thread);
    if(NULL == p_fft_calcu_handler->fft_event_handler_thread)
    {
        FFT_CALCU_LOG_ERROR("get current task handle failed,file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
#ifdef FFT_CALCU_USING_IRQ
    if(FFT_CALCU_RTOS_PDTRUE != RTOS_SEMAPHORE_CREATE(
                            &p_fft_calcu_handler->fft_start_collection_semaphore))
    {
        FFT_CALCU_LOG_ERROR("create fft_start_collection_semaphore failed,file:%s,line:%d",
                                                            __FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
#endif /* FFT_CALCU_USING_IRQ */
    if(FFT_CALCU_RTOS_PDTRUE != RTOS_QUEUE_CREATE(
                                &p_fft_calcu_handler->fft_finish_collection_queue,
    p_fft_calcu_handler->pfft_calcu_system_config->fft_finish_collection_queue_size,
                                                        sizeof(fft_data_property_t)))
    {
#ifdef FFT_CALCU_USING_IRQ
        p_fft_calcu_handler->fft_calcu_os_interface->rtos_semphore_delete(
                            &p_fft_calcu_handler->fft_start_collection_semaphore);
#endif /* FFT_CALCU_USING_IRQ */
        FFT_CALCU_LOG_ERROR("create fft_event_handler_queue failed,file:%s,line:%d",
                                                            __FILE__,__LINE__);
        return FFT_CALCU_ERROR;
     }
    if(FFT_CALCU_RTOS_PDTRUE != RTOS_TASK_CREATE(
                                                          fft_collection_data_thread,
                                                        "fft_collection_data_thread",
p_fft_calcu_handler->pfft_calcu_system_config->fft_collection_data_thread_stack_size,
                                                               p_fft_calcu_handler,
p_fft_calcu_handler->pfft_calcu_system_config->fft_collection_data_thread_proirity,
                                  &p_fft_calcu_handler->fft_collection_data_thread))
    {
#ifdef FFT_CALCU_USING_IRQ
        p_fft_calcu_handler->fft_calcu_os_interface->rtos_semphore_delete(
                            &p_fft_calcu_handler->fft_start_collection_semaphore);
#endif /* FFT_CALCU_USING_IRQ */
        p_fft_calcu_handler->fft_calcu_os_interface->rtos_queue_delete(
                            &p_fft_calcu_handler->fft_finish_collection_queue);
        FFT_CALCU_LOG_ERROR("create fft_collection_data_thread failed,file:%s,line:%d",
                                                                __FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
#ifdef FFT_CALCU_USING_IRQ
    /* 3.挂载中断回调函数 */
    (*p_fft_calcu_handler->fft_calcu_callback_fun->params) = p_fft_calcu_handler;
    (*p_fft_calcu_handler->fft_calcu_callback_fun->pfirq_callbackfun) =
                                                        fft_irq_callback_fun;
#endif /* FFT_CALCU_USING_IRQ */   
    /* 4.挂载fft_data到私有数据 */
    p_fft_calcu_handler->private_data->pfft_data       = g_fft_data_buf;
    p_fft_calcu_handler->private_data->pfft_output_buf = g_fft_output_buf;
#if !CONFIG_USE_CFFT
    p_fft_calcu_handler->private_data->pfft_mag_buf    = g_fft_mag_buf;
#endif  /* CONFIG_USE_CFFT */

#if SYNC_FFT_ENABLE
    /* 5. 创建同步事件组 */
    if(FFT_CALCU_RTOS_PDTRUE != p_fft_calcu_handler->fft_calcu_os_interface->rtos_event_group_create(
        &p_fft_calcu_handler->private_data->sync_status.sync_event_group))
    {
        /* 清理已创建的资源 */
#ifdef FFT_CALCU_USING_IRQ
        p_fft_calcu_handler->fft_calcu_os_interface->rtos_semphore_delete(
            p_fft_calcu_handler->fft_start_collection_semaphore);
#endif /* FFT_CALCU_USING_IRQ */
        p_fft_calcu_handler->fft_calcu_os_interface->rtos_queue_delete(
            p_fft_calcu_handler->fft_finish_collection_queue);
        FFT_CALCU_LOG_ERROR("Failed to create sync event group");
        return FFT_CALCU_ERROR;
    }

    /* 创建事件处理线程事件组 */
    if(FFT_CALCU_RTOS_PDTRUE != p_fft_calcu_handler->fft_calcu_os_interface->rtos_event_group_create(
        &p_fft_calcu_handler->fft_handler_event_group))
    {
        /* 清理已创建的资源 */
#ifdef FFT_CALCU_USING_IRQ
        p_fft_calcu_handler->fft_calcu_os_interface->rtos_semphore_delete(
            p_fft_calcu_handler->fft_start_collection_semaphore);
#endif /* FFT_CALCU_USING_IRQ */
        p_fft_calcu_handler->fft_calcu_os_interface->rtos_queue_delete(
            p_fft_calcu_handler->fft_finish_collection_queue);
        p_fft_calcu_handler->fft_calcu_os_interface->rtos_event_group_delete(
            p_fft_calcu_handler->private_data->sync_status.sync_event_group);
        FFT_CALCU_LOG_ERROR("Failed to create handler event group");
        return FFT_CALCU_ERROR;
    }

    /* 初始化同步状态 */
    p_fft_calcu_handler->private_data->sync_status.ac_timestamp = 0;
    p_fft_calcu_handler->private_data->sync_status.ua_timestamp = 0;
    p_fft_calcu_handler->private_data->sync_status.ub_timestamp = 0;
    p_fft_calcu_handler->private_data->sync_status.uc_timestamp = 0;
    p_fft_calcu_handler->private_data->sync_status.sync_start_time = 0;
    p_fft_calcu_handler->private_data->sync_status.sync_timeout_count = 0;

    FFT_CALCU_LOG_DEUBG("Event groups created successfully");
#endif /* SYNC_FFT_ENABLE */

    FFT_CALCU_LOG_DEUBG("fft_calcu_handler_init end");
    return FFT_CALCU_OK;
}
/******************************************************************************
 * @brief 
 * 
 * @param  p_fft_calcu_handler
 * @param  p_input_data     
 * 
 * @return fft_calcu_ret_t 
 *****************************************************************************/
fft_calcu_ret_t fft_calcu_handler_inst(
                                        fft_calcu_handler_t *p_fft_calcu_handler, 
                                        fft_calcu_input_data_t *p_input_data)
{
    FFT_CALCU_LOG_DEUBG("fft_calcu_xxx_handler_inst start");
    /* 0. 定义相关变量 */
    fft_calcu_ret_t ret_code = FFT_CALCU_OK;
    /* 1.检查输入对象是否有效 */
    if((NULL == p_fft_calcu_handler) || 
       (NULL == p_input_data))
    {
        FFT_CALCU_LOG_ERROR("input_data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERRORRESOURCE;
    }
    /* 1.1 检查输入对象内容是否有效 */
    if(
#ifdef FFT_CALCU_USING_IRQ
        (NULL == p_input_data->fft_calcu_callback_fun)              ||
#endif /* FFT_CALCU_USING_IRQ */
        (NULL == p_input_data->fft_calcu_os_interface)              || 
        (NULL == p_input_data->fft_calcu_system_timebase_interface) || 
        (NULL == p_input_data->fft_collect_msg)                     || 
        (NULL == p_input_data->fft_get_data_interface)              ||
#ifdef FFT_CALCU_USING_IRQ
        (NULL == p_input_data->fft_timer_collect_interface)         ||
#endif /* FFT_CALCU_USING_IRQ */
        (NULL == p_input_data->fft_calcu_critical_interface)        ||
        (NULL == p_input_data->plpf_fir)
       )
       {
        FFT_CALCU_LOG_ERROR("input_data content is NULL,file:%s,line:%d",__FILE__,__LINE__);
         return FFT_CALCU_ERRORPARAMETER;
       }
    /*1.2 检查输入对象成员是否有效 */
#ifdef FFT_CALCU_USING_IRQ
    /* fft_calcu_callback_fun_t 成员检查 */
    if((NULL == p_input_data->fft_calcu_callback_fun->pfirq_callbackfun) ||
       (NULL == p_input_data->fft_calcu_callback_fun->params))
    {
        FFT_CALCU_LOG_ERROR("input_data->fft_calcu_callback_fun->pfirq_callbackfun is NULL,\
                                            file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
#endif /* FFT_CALCU_USING_IRQ */
    /* fft_calcu_os_interface_t 成员检查 */
    if(
        (NULL == p_input_data->fft_calcu_os_interface->rtos_semphorebinary_create)  ||
        (NULL == p_input_data->fft_calcu_os_interface->rtos_semphore_delete)        ||
        (NULL == p_input_data->fft_calcu_os_interface->rtos_semphore_give_formisr)  ||
        (NULL == p_input_data->fft_calcu_os_interface->rtos_semphore_take)          ||
        (NULL == p_input_data->fft_calcu_os_interface->rtos_task_notifiy_take)      ||
        (NULL == p_input_data->fft_calcu_os_interface->rtos_task_notifiy_give)      ||
        (NULL == p_input_data->fft_calcu_os_interface->rtos_task_create)            ||
        (NULL == p_input_data->fft_calcu_os_interface->rtos_event_group_clear_bits) ||
        (NULL == p_input_data->fft_calcu_os_interface->rtos_event_group_create)     ||
        (NULL == p_input_data->fft_calcu_os_interface->rtos_event_group_delete)     ||
        (NULL == p_input_data->fft_calcu_os_interface->rtos_event_group_set_bits)   ||
        (NULL == p_input_data->fft_calcu_os_interface->rtos_event_group_wait_bits)
       )
       {
        FFT_CALCU_LOG_ERROR("input_data->fft_calcu_os_interface content is NULL,\
                                            file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERROR;
       }
    /* fft_calcu_system_timebase_interface_t 成员检查 */
    if(NULL == p_input_data->fft_calcu_system_timebase_interface->pfget_timetick_ms)
    {
        FFT_CALCU_LOG_ERROR("input_data->fft_calcu_system_timebase_interface->pfget_timetick_ms is NULL,\
                                            file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
    /* fft_collect_msg_t 成员检查 */
    if(NULL == p_input_data->fft_collect_msg->fft_input_data_property)
    {
        FFT_CALCU_LOG_ERROR("input_data->fft_collect_msg->fft_input_data_qty is NULL,\
                                            file:%s,line:%d",__FILE__,__LINE__);
    }
    /* fft_get_data_interface_t 成员检查 */
    if(
        (NULL == p_input_data->fft_get_data_interface->fft_check_data_valid)       ||
        (NULL == p_input_data->fft_get_data_interface->fft_get_collect_data)       ||
        (NULL == p_input_data->fft_get_data_interface->fft_get_collect_data_limit) ||
        (NULL == p_input_data->fft_get_data_interface->fft_result_data_send)
        )
    {
        FFT_CALCU_LOG_ERROR("input_data->fft_get_data_interface content is NULL,\
                                            file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
#ifdef FFT_CALCU_USING_IRQ
    /* fft_timer_collect_interface_t 成员检查 */
    if(
        (NULL == p_input_data->fft_timer_collect_interface->fft_start_timer_collect) ||
        (NULL == p_input_data->fft_timer_collect_interface->fft_stop_timer_collect)
        )
    {
        FFT_CALCU_LOG_ERROR("input_data->fft_timer_collect_interface content is NULL,\
                                            file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
#endif /* FFT_CALCU_USING_IRQ */
    /* fft_calcu_critical_interface_t 成员检查 */
    if(
        (NULL == p_input_data->fft_calcu_critical_interface->rtos_enter_critical) ||
        (NULL == p_input_data->fft_calcu_critical_interface->rtos_exit_critical)
        )
    {
        FFT_CALCU_LOG_ERROR("input_data->fft_calcu_critical_interface content is NULL,\
                                            file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
    /* fft_calcu_system_config_t 成员检查 */
    if(
        (NULL == p_input_data->pfft_calcu_system_config)                               ||
        (p_input_data->pfft_calcu_system_config->fft_collection_data_thread_proirity >
                                                        FFT_CALCU_OS_TASK_PRIORITYMAX) ||
        (0 == p_input_data->pfft_calcu_system_config->\
                                                fft_collection_data_thread_stack_size) ||
        (0 == p_input_data->pfft_calcu_system_config->fft_finish_collection_queue_size)
    )
    {
        FFT_CALCU_LOG_ERROR("input_data->pfft_calcu_system_config content is NULL or invalid,\
                                            file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
    /* 2. 实例化handler句柄 */
#ifdef FFT_CALCU_USING_IRQ
    p_fft_calcu_handler->fft_calcu_callback_fun              = p_input_data->fft_calcu_callback_fun;
#endif /* FFT_CALCU_USING_IRQ */
    p_fft_calcu_handler->fft_calcu_os_interface              = p_input_data->fft_calcu_os_interface;
    p_fft_calcu_handler->fft_calcu_system_timebase_interface = p_input_data->fft_calcu_system_timebase_interface;
    p_fft_calcu_handler->fft_get_data_interface              = p_input_data->fft_get_data_interface;
#ifdef FFT_CALCU_USING_IRQ
    p_fft_calcu_handler->fft_timer_collect_interface         = p_input_data->fft_timer_collect_interface;
#endif /* FFT_CALCU_USING_IRQ */
    p_fft_calcu_handler->fft_calcu_critical_interface        = p_input_data->fft_calcu_critical_interface;
    p_fft_calcu_handler->pfft_calcu_system_config            = p_input_data->pfft_calcu_system_config;
    p_fft_calcu_handler->plpf_fir                            = p_input_data->plpf_fir;
    /* 2.1 检查handler是否已经实例化 */
    if(
#ifdef FFT_CALCU_USING_IRQ
       (NULL == p_fft_calcu_handler->fft_calcu_callback_fun)              ||
#endif /* FFT_CALCU_USING_IRQ */
       (NULL == p_fft_calcu_handler->fft_calcu_os_interface)              ||
       (NULL == p_fft_calcu_handler->fft_calcu_system_timebase_interface) ||
       (NULL == p_fft_calcu_handler->fft_get_data_interface)              ||
#ifdef FFT_CALCU_USING_IRQ
       (NULL == p_fft_calcu_handler->fft_timer_collect_interface)         ||
#endif /* FFT_CALCU_USING_IRQ */
       (NULL == p_fft_calcu_handler->fft_calcu_critical_interface)        ||
       (NULL == p_fft_calcu_handler->pfft_calcu_system_config)
       )
    {
        FFT_CALCU_LOG_ERROR("fft_calcu_handler has not inited,file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
    p_input_data->fft_calcu_critical_interface->rtos_enter_critical();
    /* 3. 初始化成员变量 */
    ret_code =  fft_calcu_handler_init(p_fft_calcu_handler);
    if(FFT_CALCU_OK != ret_code)
    {
        FFT_CALCU_LOG_ERROR("fft_calcu_handler_init failed,file:%s,line:%d",__FILE__,__LINE__);
        return ret_code;
    }
    /* 4. 设置私有数据初始化内容 */
    p_fft_calcu_handler->private_data->fft_calcu_type = p_input_data->fft_collect_msg->fft_calcu_type;
    p_fft_calcu_handler->private_data->fft_data_type  = p_input_data->fft_collect_msg->fft_data_type;
    for(uint8_t i = 0; i < FFT_INPUT_QUANTITY; i++)
    {
        p_fft_calcu_handler->private_data->pfft_data[i].index         = 0;
        p_fft_calcu_handler->private_data->pfft_data[i].sampling_rate = SAMPLING_FREQUENCE / FFT_LENGTH;
        p_fft_calcu_handler->private_data->pfft_data[i].data_property = p_input_data->fft_collect_msg->fft_input_data_property[i];
        
    }
    /* 4.1 设置窗口类型 */
    p_fft_calcu_handler->private_data->frequecny_win     = HANNING_WIN;
    p_fft_calcu_handler->private_data->base_band_amp_win = FLATTOP_WIN;
    p_fft_calcu_handler->private_data->thd_win           = NONE;
    /* 4.2 设置初始化 */
    p_fft_calcu_handler->private_data->inited_status     = FFT_CALCU_INITED;
    FFT_CALCU_LOG_DEUBG("fft_calcu_xxx_handler_inst end");
    p_input_data->fft_calcu_critical_interface->rtos_exit_critical();
    return ret_code;
}

#if FFT_USE_WINDOW_CALCU
fft_calcu_ret_t fft_calculate_window(fft_calcu_handler_t *pfft_calcu_handler,
                             fft_data_property_t fft_data_property,
                             char fft_type)
{
    static        float window_input[FFT_LENGTH] = {0};   // 窗函数数据
    static        float win[FFT_LENGTH]          = {0};   // 窗函数数据
    window_type_t win_type                       = NONE;
    int           frequency_index                = 0;
    float         base_band_amp                  = 0;
#if CONFIG_USE_CFFT
    static float fft_tempbuf[FFT_LENGTH*2];
#else 
    arm_rfft_fast_instance_f32   S;
    static float fft_tempbuf[FFT_LENGTH];
#endif /* CONFIG_USE_CFFT */
    
    FFT_CALCU_LOG_DEUBG("fft_calculate start");
    /* 0.检查输入对象是否有效 */
    if(NULL == pfft_calcu_handler)
    {
        FFT_CALCU_LOG_ERROR("pfft_calcu_handler is NULL,file:%s,line:%d",
                                                            __FILE__,__LINE__);
        return FFT_CALCU_ERRORRESOURCE;
    }
    /* 1.检查handler是否已经初始化 */
    if(FFT_CALCU_INITED != pfft_calcu_handler->private_data->inited_status)
    {
        FFT_CALCU_LOG_ERROR("fft_calcu_handler has not inited,file:%s,line:%d",
                                                            __FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }

    /* 2.设置窗函数类型 */
    switch(fft_type)
    {
        case 'f':
        {
            win_type = pfft_calcu_handler->private_data->frequecny_win;
   
        }break;
            
        case 'v':
        {
            win_type = pfft_calcu_handler->private_data->base_band_amp_win;
        }break;
        case 't':
        {
            win_type = pfft_calcu_handler->private_data->thd_win;
        }break;
        default:
        {
            win_type = NONE;
        }break;        
    }
    /* 3.计算直流分量*/
    float amp_sum = 0;
    for(uint16_t i = 0; i < FFT_LENGTH; i++)
    {
        amp_sum += pfft_calcu_handler->private_data->\
                                    pfft_data[fft_data_property].fft_inputbuf[i];
    }
    pfft_calcu_handler->private_data->\
            pfft_data[fft_data_property].dc_component = amp_sum / FFT_LENGTH;
    /* 4.进行窗函数计算 */
    /* 4.1 将输入数据与直流分量相减*/
    for(uint16_t i = 0; i < FFT_LENGTH; i++)
    {
        window_input[i] = 
        pfft_calcu_handler->private_data->pfft_data[fft_data_property].fft_inputbuf[i] - 
        pfft_calcu_handler->private_data->pfft_data[fft_data_property].dc_component;
    }
    /* 4.2 计算窗函数数据，并加窗 */
    float k;
    switch(win_type)
    {
        case NONE:
        {
            k = rectangle(FFT_LENGTH, win);
            Window(window_input, win, FFT_LENGTH);//加窗
        }break;
        case HANNING_WIN:
        {
            k = Hanning(FFT_LENGTH, win);
            Window(window_input, win, FFT_LENGTH);//加窗
        }break;
        case HAMMING_WIN:
        {
            k = Hamming(FFT_LENGTH, win);
            Window(window_input, win, FFT_LENGTH);//加窗
        }break;
        case FLATTOP_WIN:
        {
            k = FlattopWin(FFT_LENGTH, win);
            Window(window_input, win, FFT_LENGTH);//加窗
        }break;
        default:
        {
            k = 1;
            break;
        }
    }
    /* 5. 计算FFT结果 */
    /* 5.1 设置进行fft计算数据*/
#if CONFIG_USE_CFFT
    for(uint16_t i = 0; i < FFT_LENGTH; i++)
    {
        fft_tempbuf[2 * i] = window_input[i];
        fft_tempbuf[2 * i + 1] = 0;
    }
#else
    arm_rfft_fast_init_f32(&S, FFT_LENGTH);
    memcpy(fft_tempbuf, window_input, sizeof(float) * FFT_LENGTH);
#endif /* CONFIG_USE_CFFT */
    /* 5.2 进行FFT计算 */
#if CONFIG_USE_CFFT
	arm_cfft_f32(&arm_cfft_sR_f32_len1024,
				 fft_tempbuf ,
				 0, 
				 1);
#else
    arm_rfft_fast_f32(&S, 
                    fft_tempbuf, 
                    pfft_calcu_handler->private_data->pfft_output_buf,
                    0);
#endif /* CONFIG_USE_CFFT */ 
    /* 5.3 计算幅值 */
#if CONFIG_USE_CFFT
    arm_cmplx_mag_f32(fft_tempbuf, 
                    pfft_calcu_handler->private_data->pfft_output_buf, 
                    FFT_LENGTH);
#else
    arm_cmplx_mag_f32(pfft_calcu_handler->private_data->pfft_output_buf, 
                    pfft_calcu_handler->private_data->pfft_mag_buf, 
                    FFT_LENGTH);
#endif /* CONFIG_USE_CFFT */ 
    /* 5.4 将FFT结果的第一个元素除以FFT_LENGTH  */
#if CONFIG_USE_CFFT
    pfft_calcu_handler->private_data->pfft_output_buf[0] /=  FFT_LENGTH;
#else
    pfft_calcu_handler->private_data->pfft_mag_buf[0] /=  FFT_LENGTH;
#endif /* CONFIG_USE_CFFT */              
    /* 5.5 求振幅,第一个点为直流分量，不需要计算 */
    for(uint16_t j = 1; j < FFT_LENGTH; j++)
    {
#if CONFIG_USE_CFFT
        pfft_calcu_handler->private_data->pfft_output_buf[j] /= (FFT_LENGTH / 2);
#else
        pfft_calcu_handler->private_data->pfft_mag_buf[j] /= (FFT_LENGTH / 2);
#endif /* CONFIG_USE_CFFT */ 
    }
    /*5.6 找到FFT结果的峰值索引 */
    frequency_index = find_array_maxindex(
#if CONFIG_USE_CFFT
                                        pfft_calcu_handler->private_data->pfft_output_buf, 
#else
                                        pfft_calcu_handler->private_data->pfft_mag_buf, 
#endif /* CONFIG_USE_CFFT */ 
                                        MIN_FRE_INDEX,
                                        MAX_FRE_INDEX);
    /* 5.7 计算振幅 */
    base_band_amp = 
    calculate_fence_compensation(
#if CONFIG_USE_CFFT
                pfft_calcu_handler->private_data->pfft_output_buf, 
#else
                pfft_calcu_handler->private_data->pfft_mag_buf, 
#endif /* CONFIG_USE_CFFT */ 
                frequency_index, 
                FENCE_VALUE);
    /* 6. 计算THD */
    int thd_point = thd_point = FFT_LENGTH / 2 / frequency_index;//决定计算THD的时候取几次谐波
    if (thd_point > THD_MAX_COUNT)
    {
        thd_point = THD_MAX_COUNT;//防止数组越界
    }
    switch(fft_type)
    {
        case 'f'://计算基频和基频相位
        {
            /*  计算相位 */
            pfft_calcu_handler->private_data->pfft_data[fft_data_property].base_band_phase =
            calculate_phase_radian(
#if CONFIG_USE_CFFT
                    fft_tempbuf, 
#else
                pfft_calcu_handler->private_data->pfft_output_buf, 
#endif /* CONFIG_USE_CFFT */ 
                    frequency_index,
                    0.5);
            /*  计算基频 */
            pfft_calcu_handler->private_data->pfft_data[fft_data_property].now_frequency = 
            pfft_calcu_handler->private_data->pfft_data[fft_data_property].\
                                                sampling_rate * frequency_index;

        }break;
        case 'v'://计算基频幅值
        {
            pfft_calcu_handler->private_data->pfft_data[fft_data_property].base_band_amp = 
            base_band_amp * k; 
        }break;
        case 't'://计算THD
        {
            pfft_calcu_handler->private_data->pfft_data[fft_data_property].thd_arrt[0] = 
            base_band_amp;
            float thd_sum = 0;

            for (int i = 2; i <= thd_point; i++)
            {
                float max2 = 0;
                for (int j = -2; j <= 2; j++)
                {
                    if(
#if CONFIG_USE_CFFT
                        pfft_calcu_handler->private_data->\
                                pfft_output_buf[frequency_index * i + j] > max2
#else 
                        pfft_calcu_handler->private_data->\
                                pfft_mag_buf[frequency_index * i + j] > max2
#endif /* CONFIG_USE_CFFT */ 
                    )
                    {
                        max2 = 
#if CONFIG_USE_CFFT
                        pfft_calcu_handler->private_data->\
                                pfft_output_buf[frequency_index * i + j];
#else 
                        pfft_calcu_handler->private_data->\
                                pfft_mag_buf[frequency_index * i + j];
#endif /* CONFIG_USE_CFFT */ 
                    }
                }
                pfft_calcu_handler->private_data->pfft_data[fft_data_property].\
                                                            thd_arrt[i - 1] = max2;
            }
            for (int i = 1; i < thd_point; i++)
            {
                thd_sum += 
                pfft_calcu_handler->private_data->pfft_data[fft_data_property].thd_arrt[i] * 
                pfft_calcu_handler->private_data->pfft_data[fft_data_property].thd_arrt[i];
            }
            pfft_calcu_handler->private_data->pfft_data[fft_data_property].thd = 
            sqrt(thd_sum) / pfft_calcu_handler->private_data->pfft_data[fft_data_property].thd_arrt[0] * 100;
        }break;
    }
    return FFT_CALCU_OK;
}

#else
fft_calcu_ret_t fft_calculate(fft_calcu_handler_t *pfft_calcu_handler,
                             fft_data_property_t fft_data_property)
{
#if CONFIG_ADD_WINDOW
    static        float window_input[FFT_LENGTH] = {0};   // 窗函数数据
    static        float win[FFT_LENGTH]          = {0};   // 窗函数数据
    float         k                              = 0;
#endif /* CONFIG_ADD_WINDOW */
#if CONFIG_USE_CFFT
    static float                 fft_tempbuf[FFT_LENGTH*2];
#else 
    arm_rfft_fast_instance_f32   S;
    static float                 fft_tempbuf[FFT_LENGTH];
#endif /* CONFIG_USE_CFFT */
    int                          frequency_index   = 0;
    FFT_CALCU_LOG_DEUBG("fft_calculate start");
    /* 0.检查输入对象是否有效 */
    if(NULL == pfft_calcu_handler)
    {
        FFT_CALCU_LOG_ERROR("pfft_calcu_handler is NULL,file:%s,line:%d",
                                                            __FILE__,__LINE__);
        return FFT_CALCU_ERRORRESOURCE;
    }
    /* 1.检查handler是否已经初始化 */
    if(FFT_CALCU_INITED != pfft_calcu_handler->private_data->inited_status)
    {
        FFT_CALCU_LOG_ERROR("fft_calcu_handler has not inited,file:%s,line:%d",
                                                            __FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
#if CONFIG_ADD_WINDOW
    for(uint16_t i = 0; i < FFT_LENGTH; i++)
    {
        window_input[i] = 
        pfft_calcu_handler->private_data->pfft_data[fft_data_property].fft_inputbuf[i];  
    }
    k = Hanning(FFT_LENGTH, win);
    Window(window_input, win, FFT_LENGTH);//加窗
#endif /* CONFIG_ADD_WINDOW */

#if !CONFIG_USE_CFFT
    arm_rfft_fast_init_f32(&S, FFT_LENGTH);
#endif
    /* 2. 复制FFT输入数据到缓存数组 */
    pfft_calcu_handler->fft_calcu_critical_interface->rtos_enter_critical();
#if CONFIG_USE_CFFT
#if CONFIG_ADD_WINDOW
    for(uint16_t i = 0; i < FFT_LENGTH; i++)
    {
        fft_tempbuf[2 * i] = window_input[i];
		fft_tempbuf[2 * i + 1] = 0;
    }
#else
    for(uint16_t i = 0; i < FFT_LENGTH; i++)
    {
        fft_tempbuf[2 * i] = 
        pfft_calcu_handler->private_data->pfft_data[fft_data_property].fft_inputbuf[i];
        fft_tempbuf[2 * i + 1] = 0;
    }
#endif /* CONFIG_ADD_WINDOW */
#else
    memcpy(fft_tempbuf, 
#if CONFIG_ADD_WINDOW
           window_input, 
#else
           pfft_calcu_handler->private_data->pfft_data[fft_data_property].fft_inputbuf, 
#endif /* CONFIG_ADD_WINDOW */
           sizeof(float) * FFT_LENGTH);
          
#endif /* CONFIG_USE_CFFT */
    pfft_calcu_handler->fft_calcu_critical_interface->rtos_exit_critical();
    /* 3. 进行FFT计算 */
#if CONFIG_USE_CFFT
	arm_cfft_f32(&arm_cfft_sR_f32_len1024,
				 fft_tempbuf ,
				 0, 
				 1);
#else
    arm_rfft_fast_f32(&S, 
                    fft_tempbuf, 
                    pfft_calcu_handler->private_data->pfft_output_buf,
                    0);
#endif /* CONFIG_USE_CFFT */ 
    /* 4. 计算幅值 */
#if CONFIG_USE_CFFT
    arm_cmplx_mag_f32(fft_tempbuf, 
                    pfft_calcu_handler->private_data->pfft_output_buf, 
                    FFT_LENGTH);
#else
    arm_cmplx_mag_f32(pfft_calcu_handler->private_data->pfft_output_buf, 
                    pfft_calcu_handler->private_data->pfft_mag_buf, 
                    FFT_LENGTH);
#endif /* CONFIG_USE_CFFT */ 
    /* 5. 找到FFT结果的峰值索引 */
#if CONFIG_USE_CFFT
                pfft_calcu_handler->private_data->pfft_output_buf[0] /=  FFT_LENGTH;
#else
                pfft_calcu_handler->private_data->pfft_mag_buf[0] /=  FFT_LENGTH;
#endif /* CONFIG_USE_CFFT */              
    /* 5.1 求振幅,第一个点为直流分量，不需要计算 */
    for(uint16_t j = 1; j < FFT_LENGTH; j++)
    {
#if CONFIG_USE_CFFT
        pfft_calcu_handler->private_data->pfft_output_buf[j] /= (FFT_LENGTH / 2);
#else
        pfft_calcu_handler->private_data->pfft_mag_buf[j] /= (FFT_LENGTH / 2);
#endif /* CONFIG_USE_CFFT */ 
    }
    /* 5.2 找到FFT结果的峰值索引 */
    frequency_index = find_array_maxindex(
#if CONFIG_USE_CFFT
                                         pfft_calcu_handler->private_data->pfft_output_buf, 
#else
                                         pfft_calcu_handler->private_data->pfft_mag_buf, 
#endif /* CONFIG_USE_CFFT */ 
                                        MIN_FRE_INDEX,
                                        MAX_FRE_INDEX);
    if((frequency_index < MIN_FRE_INDEX) || (frequency_index > MAX_FRE_INDEX))
    {
    // 在45HZ和65HZ之间找到峰值索引
    frequency_index = find_array_maxindex(
#if CONFIG_USE_CFFT
                                         pfft_calcu_handler->private_data->pfft_output_buf,
#else
                                         pfft_calcu_handler->private_data->pfft_mag_buf,
#endif /* CONFIG_USE_CFFT */ 
                                         MIN_FRE_INDEX,
                                         MAX_FRE_INDEX);
    }
    /* 5.3 计算频率 */
    pfft_calcu_handler->private_data->pfft_data[fft_data_property].now_frequency = 
    pfft_calcu_handler->private_data->pfft_data[fft_data_property].sampling_rate * 
    frequency_index;
    /* 5.4 计算振幅 */
    pfft_calcu_handler->private_data->pfft_data[fft_data_property].base_band_amp = 
#if CONFIG_ADD_WINDOW
    k *
#endif /* CONFIG_ADD_WINDOW */
     calculate_fence_compensation(
#if CONFIG_USE_CFFT
                                pfft_calcu_handler->private_data->pfft_output_buf, 
#else
                                pfft_calcu_handler->private_data->pfft_mag_buf, 
#endif /* CONFIG_USE_CFFT */ 
                                frequency_index, 
                                FENCE_VALUE);
    /* 5.5 计算相位 */
    pfft_calcu_handler->private_data->pfft_data[fft_data_property].base_band_phase = 
                calculate_phase_radian(
#if CONFIG_USE_CFFT
                                        fft_tempbuf, 
#else
                                        pfft_calcu_handler->private_data->pfft_output_buf, 
#endif /* CONFIG_USE_CFFT */ 
                                        frequency_index);
    return FFT_CALCU_OK;
}
#endif /* FFT_USE_WINDOW_CALCU */

fft_calcu_ret_t fft_result_data_process( fft_calcu_handler_t *pfft_calcu_handler,
                                         fft_data_property_t fft_data_property,
                                         fft_result_event_t *pfft_result_event)
{
    FFT_CALCU_LOG_DEUBG("fft_result_data_process start");
    /* 0. 检查输入对象是否有效 */
    if((NULL == pfft_calcu_handler) || (NULL == pfft_result_event))
    {
        FFT_CALCU_LOG_ERROR("pfft_calcu_handler is NULL,file:%s,line:%d",
                                                            __FILE__,__LINE__);
        return FFT_CALCU_ERRORRESOURCE; 
    }
    if( fft_data_property >= FFT_INPUT_QUANTITY)
    {
        FFT_CALCU_LOG_ERROR("fft_data_property is error,file:%s,line:%d",
                                                            __FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
    /* 1. 检查handler是否已经初始化 */
    if(FFT_CALCU_INITED!= pfft_calcu_handler->private_data->inited_status)
    {
        FFT_CALCU_LOG_ERROR("fft_calcu_handler has not inited,file:%s,line:%d",
                                                            __FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
    /* 2.计算数据 */
    if(FFT_INPUT_VOLTAGE_AC == fft_data_property)
    {
        pfft_result_event->data_property = fft_data_property;
        pfft_result_event->fft_result_data.fundation_data.fft_result_amplitude_data =
        pfft_calcu_handler->private_data->pfft_data[fft_data_property].base_band_amp;
        pfft_result_event->fft_result_data.fundation_data.fft_result_freq_data =
        pfft_calcu_handler->private_data->pfft_data[fft_data_property].now_frequency;
    }
    else if((FFT_INPUT_VOLTAGE_UA == fft_data_property) ||
            (FFT_INPUT_VOLTAGE_UB == fft_data_property) ||
            (FFT_INPUT_VOLTAGE_UC == fft_data_property))
    {
        pfft_result_event->data_property = fft_data_property;
        pfft_result_event->fft_result_data.three_phase_vol_data.fft_result_amplitude_data =
        pfft_calcu_handler->private_data->pfft_data[fft_data_property].base_band_amp;
        pfft_result_event->fft_result_data.three_phase_vol_data.fft_result_phase_data =
        pfft_calcu_handler->private_data->pfft_data[fft_data_property].base_band_phase;
        pfft_result_event->fft_result_data.three_phase_vol_data.fft_result_amp_3rd_data =
        pfft_calcu_handler->private_data->pfft_data[fft_data_property].thd_arrt[2];
        pfft_result_event->fft_result_data.three_phase_vol_data.fft_result_amp_5rd_data =
        pfft_calcu_handler->private_data->pfft_data[fft_data_property].thd_arrt[4];
        pfft_result_event->fft_result_data.three_phase_vol_data.fft_result_amp_7rd_data =
        pfft_calcu_handler->private_data->pfft_data[fft_data_property].thd_arrt[6];
        pfft_result_event->fft_result_data.three_phase_vol_data.fft_result_amp_9rd_data =
        pfft_calcu_handler->private_data->pfft_data[fft_data_property].thd_arrt[8];
    }
    return FFT_CALCU_OK;
}


void fft_event_handler_thread(void *arg)
{
    FFT_CALCU_LOG_DEUBG("fft_event_handler_thread start");
    /* 0. 定义相关变量 */
    fft_calcu_input_data_t       *p_input_data     = (fft_calcu_input_data_t*)arg;
    fft_calcu_handler_t          fft_calcu_handler = {0};
//    arm_cfft_radix4_instance_f32 sfft_instance;

    uint32_t                     comsum_time       = 0;
    fft_data_property_t          fft_data_property;
    fft_calcu_private_data_t private_data = 
    {
        .inited_status = FFT_CALCU_NOT_INITED,
    };
    fft_calcu_ret_t    ret_code     = FFT_CALCU_OK;
    fft_result_event_t result_event;
    /* 1. 检查输入参数 */
    if(NULL == p_input_data)
    {
        FFT_CALCU_LOG_ERROR("input_data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return ;
    }
    /* 2. 挂载变量到handler，实例化handler句柄 */
    fft_calcu_handler.private_data = &private_data;
    /* 2.1 实例化handler句柄*/
    ret_code = fft_calcu_handler_inst(
                                    &fft_calcu_handler,
                                    p_input_data
                                                );
    /* 3. 挂载handler */
    if(FFT_CALCU_OK == ret_code)
    {
        __fft_calcu_mount_handler(&fft_calcu_handler);
    }
    else
    {
        FFT_CALCU_LOG_ERROR("fft_calcu_handler_inst failed,ret_code:%d,file:%s,line:%d",
                                                ret_code,__FILE__,__LINE__);    
        return ;
    }

    /* 5. 执行线程处理 */
    while(1)
    {
#if SYNC_FFT_ENABLE
        /* 6. 使用事件组等待FFT计算请求 */
        uint32_t event_bits = fft_calcu_handler.fft_calcu_os_interface->rtos_event_group_wait_bits(
            fft_calcu_handler.fft_handler_event_group,
            FFT_ALL_CALC_BITS,             // 等待任何计算事件
            1,                              // 清除已设置的事件位
            0,                              // 不需要等待所有位
            MY_MAX_DELAY);                  // 永久等待

        FFT_CALCU_LOG_DEUBG("Received event bits: 0x%x", event_bits);

        /* 处理同步FFT计算请求 */
        if(event_bits & FFT_SYNC_CALC_BIT)
        {
            FFT_CALCU_LOG_DEUBG("Processing sync FFT calculation");
            comsum_time = fft_calcu_handler.fft_calcu_system_timebase_interface->pfget_timetick_ms();

            /* 执行同步FFT计算 */
            if(FFT_CALCU_OK == sync_fft_calculate(&fft_calcu_handler))
            {
                /* 发送同步计算结果 */
                sync_fft_send_result(&fft_calcu_handler);
            }
            /* 重置同步状态 */
            sync_fft_reset_status(&fft_calcu_handler);

            comsum_time = fft_calcu_handler.fft_calcu_system_timebase_interface->pfget_timetick_ms() - comsum_time;
            FFT_CALCU_LOG_DEUBG("Sync FFT calculation time: %d ms", comsum_time);
            continue;
        }

        /* 处理清除数据请求 */
        if(event_bits & FFT_CLEAR_DATA_BIT)
        {
            FFT_CALCU_LOG_DEUBG("Processing clear data request");
            fft_calcu_handler.fft_calcu_critical_interface->rtos_enter_critical();
            for(uint8_t j = 0; j < FFT_INPUT_QUANTITY; j++)
            {
                fft_calcu_handler.private_data->pfft_data[j].index = 0;
            }
            fft_calcu_handler.fft_calcu_critical_interface->rtos_exit_critical();

            /* 重置同步状态 */
            sync_fft_reset_status(&fft_calcu_handler);
            continue;
        }
#ifndef SYNC_FFT_ENABLE
        /* 处理单独的FFT计算请求 */
        fft_data_property_t fft_data_property;

        if(event_bits & FFT_AC_CALC_BIT)
            fft_data_property = FFT_INPUT_VOLTAGE_AC;
        else if(event_bits & FFT_UA_CALC_BIT)
            fft_data_property = FFT_INPUT_VOLTAGE_UA;
        else if(event_bits & FFT_UB_CALC_BIT)
            fft_data_property = FFT_INPUT_VOLTAGE_UB;
        else if(event_bits & FFT_UC_CALC_BIT)
            fft_data_property = FFT_INPUT_VOLTAGE_UC;
        else
        {
            FFT_CALCU_LOG_ERROR("Unknown event bits: 0x%x", event_bits);
            continue;
        }

        FFT_CALCU_LOG_DEUBG("Processing individual FFT calculation for data property: %d", fft_data_property)
#endif /* SYNC_FFT_ENABLE */
#else
        /* 6. 计算FFT结果 */
        if(FFT_CALCU_RTOS_PDTRUE != fft_calcu_handler.fft_calcu_os_interface->\
                rtos_queue_receive(fft_calcu_handler.fft_finish_collection_queue,
                                    &fft_data_property,
                                    MY_MAX_DELAY))
        {
            FFT_CALCU_LOG_ERROR("fft_calcu_os_interface->rtos_task_notifiy_take failed,\
                                            file:%s,line:%d",__FILE__,__LINE__);
            continue;
        }

        if(
#if CONFIG_USE_SLIDING_WINDOW
            (fft_data_property > FFT_INPUT_CLEAR_DATA) ||
#else
            (fft_data_property >= FFT_INPUT_QUANTITY) ||
#endif /* CONFIG_USE_SLIDING_WINDOW */
            (fft_data_property < 0))
        {
            FFT_CALCU_LOG_ERROR("fft_data_property is error:%d,file:%s,line:%d",
                                fft_data_property, __FILE__, __LINE__);
            continue;
        }

#if CONFIG_USE_SLIDING_WINDOW
        /* 清除数据,将数据指针清零 */
        if(FFT_INPUT_CLEAR_DATA == fft_data_property)
        {
            FFT_CALCU_LOG_DEUBG("fft clear data");
            fft_calcu_handler.fft_calcu_critical_interface->rtos_enter_critical();
            for(uint8_t j = 0; j < FFT_INPUT_QUANTITY; j++)
            {
                fft_calcu_handler.private_data->pfft_data[j].index = 0;
            }
            fft_calcu_handler.fft_calcu_critical_interface->rtos_exit_critical();
            continue;
        }
#endif /* CONFIG_USE_SLIDING_WINDOW */
#endif /* SYNC_FFT_ENABLE */
#ifndef SYNC_FFT_ENABLE
        /* 7. 处理FFT计算 */
        {
            FFT_CALCU_LOG_DEUBG("fft start calculate data result");
            comsum_time = fft_calcu_handler.fft_calcu_system_timebase_interface->\
                                                                    pfget_timetick_ms();
            /* 7.1 计算FFT结果 */
#if FFT_USE_WINDOW_CALCU
            ret_code = fft_calculate_window(
                                    &fft_calcu_handler,
                                    fft_data_property,
                                    'f');
            ret_code = fft_calculate_window(
                                    &fft_calcu_handler,
                                    fft_data_property,
                                    'v');
            ret_code = fft_calculate_window(
                                    &fft_calcu_handler,
                                    fft_data_property,
                                    't');
#else
            ret_code = fft_calculate(
                                    &fft_calcu_handler,
                                    fft_data_property);
#endif /* FFT_USE_WINDOW_CALCU */
            if(FFT_CALCU_OK != ret_code)
            {
                FFT_CALCU_LOG_ERROR("fft_calculate failed,ret_code:%d,file:%s,line:%d",
                                        ret_code,__FILE__,__LINE__);
            }
            if(FFT_CALCU_OK != fft_result_data_process( &fft_calcu_handler,
                                                        fft_data_property,
                                                        &result_event))
            {
                FFT_CALCU_LOG_ERROR("fft_result_data_process failed,ret_code:%d,file:%s,line:%d",
                                        ret_code,__FILE__,__LINE__);
            }
            comsum_time = fft_calcu_handler.fft_calcu_system_timebase_interface->pfget_timetick_ms() - comsum_time;
            FFT_CALCU_LOG_DEUBG("fft_event_handler_thread comsum_time:[%d]",comsum_time);
            // 发送事件到外部api上
            if(FFT_CALCU_OK !=
            fft_calcu_handler.fft_get_data_interface->\
                                            fft_result_data_send(&result_event))
            {
                FFT_CALCU_LOG_ERROR("fft_get_data_interface->fft_result_data_send failed,\
                                            file:%s,line:%d",__FILE__,__LINE__);
            }
        }
#endif /* SYNC_FFT_ENABLE */
    }
}

#if FFT_USE_WINDOW_CALCU
/******************************************************************************
 * @brief 清除FFT输入数据
 * -此函数只在使用滑动窗口算法时有效
 * 
 * @return fft_calcu_ret_t 
 * @retval FFT_CALCU_OK 成功
 * @retval FFT_CALCU_ERROR 失败
 * @retval FFT_CALCU_ERRORRESOURCE 资源错误
 *****************************************************************************/
fft_calcu_ret_t fft_data_input_clear(void)
{
    FFT_CALCU_LOG_DEUBG("fft_data_input_clear start");
    /* 0. 检查是否已经初始化*/
    if(FFT_CALCU_NOT_INITED == gp_fft_calcu_handler->private_data->inited_status)
    {
        FFT_CALCU_LOG_ERROR("fft input task not inited,file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERRORRESOURCE;
    }

#if SYNC_FFT_ENABLE
    /* 1. 使用事件组发送清除数据信号 */
    gp_fft_calcu_handler->fft_calcu_os_interface->rtos_event_group_set_bits(
        gp_fft_calcu_handler->fft_handler_event_group,
        FFT_CLEAR_DATA_BIT);
    FFT_CALCU_LOG_DEUBG("Clear data event set");
#else
    /* 1. 发送清除数据信号 */
    fft_data_property_t  fft_data_property = FFT_INPUT_CLEAR_DATA;
    if(FFT_CALCU_RTOS_PDTRUE !=
    gp_fft_calcu_handler->fft_calcu_os_interface->rtos_queue_send(
                                gp_fft_calcu_handler->fft_finish_collection_queue,
                                &fft_data_property,
                                0))
    {
        FFT_CALCU_LOG_ERROR("fft_calcu_os_interface->rtos_queue_send failed,\
                                            file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERROR;
    }
#endif /* SYNC_FFT_ENABLE */

    FFT_CALCU_LOG_DEUBG("fft_data_input_clear end");
    return FFT_CALCU_OK;
}
#endif /* FFT_USE_WINDOW_CALCU */


//fft_calcu_ret_t fft_reslut_data_receive(fft_result_event_t *event)
//{
//    FFT_CALCU_LOG_DEUBG("fft_reslut_data_receive start");
//    /* 0. 检查输入参数 */
//    if(NULL == event)
//    {
//        FFT_CALCU_LOG_ERROR("event is NULL,file:%s,line:%d",__FILE__,__LINE__);
//        return FFT_CALCU_ERRORPARAMETER;
//    }
//	/* 0.1 检查是否已经初始化*/
//	if(FFT_CALCU_NOT_INITED == gp_fft_calcu_handler->private_data->inited_status)
//	{
//		FFT_CALCU_LOG_ERROR("fft result task not inited,file:%s,line:%d",__FILE__,__LINE__);
//		return FFT_CALCU_ERRORRESOURCE;
//	}
//    /* 1. 接收数据 */
//    if(FFT_CALCU_RTOS_PDTRUE != 
//    gp_fft_calcu_handler->fft_calcu_os_interface->rtos_queue_receive(
//                                gp_fft_calcu_handler->fft_collection_result_queue,
//                                event,
//                                MY_MAX_DELAY))
//    {
//        FFT_CALCU_LOG_ERROR("fft_calcu_os_interface->rtos_queue_receive failed,\
//                                            file:%s,line:%d",__FILE__,__LINE__);
//        return FFT_CALCU_ERROR;
//    }
//    FFT_CALCU_LOG_DEUBG("fft_reslut_data_receive end");
//    return FFT_CALCU_OK;
//}
#ifdef FFT_CALCU_USING_IRQ
int8_t fft_irq_callback_fun(void *arg)
{
    FFT_CALCU_DEBUG_PR("fft_irq_callback_fun start");
    /* 检查输入参数 */
    if(NULL == arg)
    {
        FFT_CALCU_LOG_ERROR("arg is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return 1;
    }
    fft_calcu_handler_t *p_fft_calcu_handler = (fft_calcu_handler_t*)arg;
    if(FFT_CALCU_INITED != p_fft_calcu_handler->private_data->inited_status)
    {
        p_fft_calcu_handler->fft_timer_collect_interface->fft_stop_timer_collect();
        FFT_CALCU_DEBUG_PR("fft_calcu_handler is not inited,file:%s,line:%d",__FILE__,__LINE__);
        return 1;
    }
    /* 释放信号量 */
    if(FFT_CALCU_RTOS_PDTRUE != 
    p_fft_calcu_handler->fft_calcu_os_interface->rtos_semphore_give_formisr(
                                p_fft_calcu_handler->fft_start_collection_semaphore))
    {
        FFT_CALCU_DEBUG_PR("fft_calcu_os_interface->rtos_semphore_give_formisr failed,\
                                            file:%s,line:%d",__FILE__,__LINE__);
    }
    FFT_CALCU_DEBUG_PR("fft_irq_callback_fun end");
    return 0;
}
#endif /* FFT_CALCU_USING_IRQ */

/**
 * @brief  计算栅栏补偿值
 * @note   
 * @param  output_mag: FFT输出的结果值并已计算过幅值
 * @param  central_value: 中心点
 * @param  fence_value: 需要加入计算的点数
 * @retval 进行栅栏补偿后的幅值
 */
float32_t calculate_fence_compensation(float32_t* output_mag, uint16_t central_value, uint16_t fence_value)
{
    float32_t fence_compensation = 0;
    uint16_t i =  0 ;
    if(!output_mag)
    {
        return 0;
    }
    if(0 == fence_value)
    {
        fence_compensation = output_mag[central_value];
    }
    else
    {
        for(i = central_value - (fence_value/2); i < central_value + (fence_value/2); i++)
        {
            fence_compensation += pow(output_mag[i], 2);
        }
        fence_compensation = sqrt(fence_compensation);
    }
    return fence_compensation;
}

/**
 * @brief  计算相位
 * @note   
 * @param  input_data: 进行FFT运算过的数据
 * @param  frequence_index: 要计算的频率索引
 * @retval 相位值
 */
float32_t calculate_phase_radian(float32_t* input_data, uint16_t frequence_index, float cmp_value)
{
    // 定义实部和虚部
    float32_t ix,iy;
    // 定义相位
    float32_t phase = 0;
    float32_t mag;
    // 如果输入数据为空，返回0
    if(!input_data)
    {
        return 0.0;
    }
    // 如果频率索引大于FFT长度，返回0
    if(frequence_index > FFT_LENGTH)
    {
        return 0.0;
    }
    // 获取实部和虚部
    ix = input_data[frequence_index * 2];
    iy = input_data[frequence_index * 2 + 1];
    // 计算相位
    phase = atan2f(iy, ix);
    phase = phase * 180.0f / PI;
    // 返回相位
    return phase;
}






// 汉宁窗
void fft_hanning_window(float32_t* fft_input, uint32_t size)
{
    uint32_t i;
    for( i = 0; i < size; i++ )
    {
        fft_input[i] *= 0.5f * (1.0f - arm_cos_f32(2.0 * PI * i / (size - 1)));
    }
}

/*-------阶乘-------*/
int Factorial(int num)
{
	int a = 1;
	for (int i = num; i >= 1; i--)
	{
		a *= i;
	}
	return a;
}
/*--------0阶贝塞尔--------*/
float Besseli(float x)
{
	float a = 1.0;
	for (int i = 1; i < 20; i++)
	{
		a += pow((pow((x / 2), i) * 1.0 / Factorial(i)), 2);
	}
	return a;
}
/*--------Kaiser窗--------*/
float Kaiser(float beta, int n, float* win_out)
{
	for (int i = 0; i < n; i++)
	{
		win_out[i] = Besseli(beta * sqrt(1 - pow(2.0 * i / (n - 1) - 1, 2))) / Besseli(beta);
	}
	return 1;
}
/*--------Flattop窗--------*/
float FlattopWin(int n, float* win_out)
{
	float a0 = 0.21557895, a1 = 0.41663158, a2 = 0.277263158, a3 = 0.083578947, a4 = 0.006947368;
	for (int i = 0; i < n; i++)
	{
		win_out[i] = a0 - a1 * arm_cos_f32(2 * PI * i / (n - 1)) + a2 * arm_cos_f32(4 * PI * i / (n - 1))
			- a3 * arm_cos_f32(6 * PI * i / (n - 1)) + a4 * arm_cos_f32(8 * PI * i / (n - 1));
	}
	return 4.639;
}
/*--------Hanning--------*/
float Hanning(int n, float* win_out)
{
	for (int i = 0; i < n; i++)
	{
		win_out[i] = 0.5 * (1 - arm_cos_f32(2 * PI * i / (n - 1)));
	}
	return 2;
}
/*--------Hamming--------*/
float Hamming(int n, float* win_out)
{
	for (int i = 0; i < n; i++)
	{
		win_out[i] = 0.54 - 0.46 * arm_cos_f32(2 * PI * i / (n - 1));
	}
	return 1.852;
}
/*--------rectangle--------*/
float rectangle(int n, float* win_out)
{
	for (int i = 0; i < n; i++)
	{
		win_out[i] = 1;
	}
	return 1;
}
/*--------加窗--------*/
void Window(float* input, float* win_in, int n)
{
	for (int i = 0; i < n; i++)
	{
		input[i] *= win_in[i];
	}
}

#if SYNC_FFT_ENABLE
/******************************************************************************
 * @brief 检查同步FFT计算是否准备就绪
 *
 * @param p_handler FFT计算处理器
 * @return fft_calcu_ret_t 检查结果
 *****************************************************************************/
static fft_calcu_ret_t sync_fft_check_ready(fft_calcu_handler_t *p_handler)
{
    if(p_handler == NULL || p_handler->private_data == NULL)
    {
        return FFT_CALCU_ERRORPARAMETER;
    }

    sync_data_status_t *p_status = &p_handler->private_data->sync_status;
    uint32_t current_time = p_handler->fft_calcu_system_timebase_interface->pfget_timetick_ms();

    /* 使用事件组等待同步条件 */
    uint32_t event_bits = p_handler->fft_calcu_os_interface->rtos_event_group_wait_bits(
        p_status->sync_event_group,
        FFT_SYNC_READY_BITS,        // 等待所有电压数据都准备好
        0,                          // 不清除事件位
        1,                          // 等待所有位都设置
        0                           // 不阻塞，立即返回
    );

    /* 检查是否所有数据都准备好 */
    if((event_bits & FFT_SYNC_READY_BITS) == FFT_SYNC_READY_BITS)
    {
        /* 检查时间同步性 - 找到最早和最晚的时间戳 */
        uint32_t timestamps[4] = {
            p_status->ac_timestamp,
            p_status->ua_timestamp,
            p_status->ub_timestamp,
            p_status->uc_timestamp
        };

        uint32_t min_time = timestamps[0];
        uint32_t max_time = timestamps[0];

        for(int i = 1; i < 4; i++)
        {
            if(timestamps[i] < min_time) min_time = timestamps[i];
            if(timestamps[i] > max_time) max_time = timestamps[i];
        }

        uint32_t time_diff = max_time - min_time;

        if(time_diff <= SYNC_TIMEOUT_MS)
        {
            FFT_CALCU_LOG_DEUBG("Sync FFT ready: time_diff=%d ms", time_diff);
            return FFT_CALCU_OK;
        }
        else
        {
            FFT_CALCU_LOG_DEUBG("Sync timeout: time_diff=%d ms", time_diff);
            p_status->sync_timeout_count++;

            /* 超时，重置最早的数据对应的事件位 */
            if(min_time == p_status->ac_timestamp)
            {
                p_handler->fft_calcu_os_interface->rtos_event_group_clear_bits(
                    p_status->sync_event_group, FFT_AC_READY_BIT);
            }
            else if(min_time == p_status->ua_timestamp)
            {
                p_handler->fft_calcu_os_interface->rtos_event_group_clear_bits(
                    p_status->sync_event_group, FFT_UA_READY_BIT);
            }
            else if(min_time == p_status->ub_timestamp)
            {
                p_handler->fft_calcu_os_interface->rtos_event_group_clear_bits(
                    p_status->sync_event_group, FFT_UB_READY_BIT);
            }
            else if(min_time == p_status->uc_timestamp)
            {
                p_handler->fft_calcu_os_interface->rtos_event_group_clear_bits(
                    p_status->sync_event_group, FFT_UC_READY_BIT);
            }
        }
    }

    /* 检查是否有数据超时 */
    if((event_bits & FFT_AC_READY_BIT) &&
       (current_time - p_status->ac_timestamp > SYNC_TIMEOUT_MS))
    {
        FFT_CALCU_LOG_DEUBG("AC voltage data timeout, reset");
        p_handler->fft_calcu_os_interface->rtos_event_group_clear_bits(
            p_status->sync_event_group, FFT_AC_READY_BIT);
    }

    if((event_bits & FFT_UA_READY_BIT) &&
       (current_time - p_status->ua_timestamp > SYNC_TIMEOUT_MS))
    {
        FFT_CALCU_LOG_DEUBG("UA voltage data timeout, reset");
        p_handler->fft_calcu_os_interface->rtos_event_group_clear_bits(
            p_status->sync_event_group, FFT_UA_READY_BIT);
    }

    if((event_bits & FFT_UB_READY_BIT) &&
       (current_time - p_status->ub_timestamp > SYNC_TIMEOUT_MS))
    {
        FFT_CALCU_LOG_DEUBG("UB voltage data timeout, reset");
        p_handler->fft_calcu_os_interface->rtos_event_group_clear_bits(
            p_status->sync_event_group, FFT_UB_READY_BIT);
    }

    if((event_bits & FFT_UC_READY_BIT) &&
       (current_time - p_status->uc_timestamp > SYNC_TIMEOUT_MS))
    {
        FFT_CALCU_LOG_DEUBG("UC voltage data timeout, reset");
        p_handler->fft_calcu_os_interface->rtos_event_group_clear_bits(
            p_status->sync_event_group, FFT_UC_READY_BIT);
    }

    return FFT_CALCU_ERROR;
}

static fft_calcu_ret_t fft_calcuate_3rd(fft_calcu_handler_t *p_handler, 
                                        fft_data_property_t fft_data_property)

{
    fft_calcu_ret_t ret_code = FFT_CALCU_OK;
    if(NULL == p_handler)
    {
        FFT_CALCU_LOG_ERROR("p_handler is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return FFT_CALCU_ERRORPARAMETER;
    }
    if(fft_data_property >= FFT_INPUT_QUANTITY)
    {
        FFT_CALCU_LOG_ERROR("fft_data_property is error:%d,file:%s,line:%d",
                            fft_data_property, __FILE__, __LINE__);
        return FFT_CALCU_ERRORPARAMETER;
    }
    ret_code = fft_calculate_window(p_handler, fft_data_property,'f');
    if(ret_code == FFT_CALCU_OK)
    {
        ret_code = fft_calculate_window(p_handler, fft_data_property, 'v');
    }
    if(ret_code == FFT_CALCU_OK)
    {
        ret_code = fft_calculate_window(p_handler, fft_data_property, 't');
    }
    return ret_code;
}
/******************************************************************************
 * @brief 执行同步FFT计算
 *
 * @param p_handler FFT计算处理器
 * @return fft_calcu_ret_t 计算结果
 *****************************************************************************/
static fft_calcu_ret_t sync_fft_calculate(fft_calcu_handler_t *p_handler)
{
    if(p_handler == NULL || p_handler->private_data == NULL)
    {
        return FFT_CALCU_ERRORPARAMETER;
    }

    fft_calcu_ret_t ret_code = FFT_CALCU_OK;
    sync_fft_result_t *p_result = &p_handler->private_data->sync_result;
    uint32_t start_time = p_handler->fft_calcu_system_timebase_interface->pfget_timetick_ms();

    FFT_CALCU_LOG_DEUBG("Starting sync FFT calculation");

    /* 分别计算AC和三相电压的FFT */

    /* 1. 计算AC电压FFT */
#if FFT_USE_WINDOW_CALCU
    ret_code = fft_calculate_window(p_handler, FFT_INPUT_VOLTAGE_AC, 'f');
    if(ret_code == FFT_CALCU_OK)
    {
        ret_code = fft_calculate_window(p_handler, FFT_INPUT_VOLTAGE_AC, 'v');
    }
#else
    ret_code = fft_calculate(p_handler, FFT_INPUT_VOLTAGE_AC);
#endif /* FFT_USE_WINDOW_CALCU */

    if(ret_code != FFT_CALCU_OK)
    {
        FFT_CALCU_LOG_ERROR("AC voltage FFT calculation failed");
        return ret_code;
    }

    /* 获取AC电压FFT结果 */
    p_result->ac_amplitude = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_AC].base_band_amp;
    p_result->ac_phase = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_AC].base_band_phase;
    p_result->ac_frequency = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_AC].now_frequency;

    /* 2. 计算UA相电压FFT */
#if FFT_USE_WINDOW_CALCU
    ret_code = fft_calcuate_3rd(p_handler, FFT_INPUT_VOLTAGE_UA);

#else
    ret_code = fft_calculate(p_handler, FFT_INPUT_VOLTAGE_UA);
#endif /* FFT_USE_WINDOW_CALCU */

    if(ret_code != FFT_CALCU_OK)
    {
        FFT_CALCU_LOG_ERROR("UA voltage FFT calculation failed");
        return ret_code;
    }

    /* 获取UA相电压FFT结果 */
    p_result->ua_amplitude    = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UA].base_band_amp;
    p_result->ua_phase        = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UA].base_band_phase;
    p_result->ua_3rd_harmonic = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UA].thd_arrt[2];
    p_result->ua_5th_harmonic = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UA].thd_arrt[4];
    p_result->ua_7th_harmonic = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UA].thd_arrt[6];
    p_result->ua_9th_harmonic = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UA].thd_arrt[8];

    /* 3. 计算UB相电压FFT */
#if FFT_USE_WINDOW_CALCU
    ret_code = fft_calcuate_3rd(p_handler, FFT_INPUT_VOLTAGE_UB);
#else
    ret_code = fft_calculate(p_handler, FFT_INPUT_VOLTAGE_UB);
#endif /* FFT_USE_WINDOW_CALCU */

    if(ret_code != FFT_CALCU_OK)
    {
        FFT_CALCU_LOG_ERROR("UB voltage FFT calculation failed");
        return ret_code;
    }

    /* 获取UB相电压FFT结果 */
    p_result->ub_amplitude    = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UB].base_band_amp;
    p_result->ub_phase        = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UB].base_band_phase;
    p_result->ub_3rd_harmonic = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UB].thd_arrt[2];
    p_result->ub_5th_harmonic = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UB].thd_arrt[4];
    p_result->ub_7th_harmonic = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UB].thd_arrt[6];

    /* 4. 计算UC相电压FFT */
#if FFT_USE_WINDOW_CALCU
    ret_code = fft_calcuate_3rd(p_handler, FFT_INPUT_VOLTAGE_UC);
#else
    ret_code = fft_calculate(p_handler, FFT_INPUT_VOLTAGE_UC);
#endif /* FFT_USE_WINDOW_CALCU */

    if(ret_code != FFT_CALCU_OK)
    {
        FFT_CALCU_LOG_ERROR("UC voltage FFT calculation failed");
        return ret_code;
    }

    /* 获取UC相电压FFT结果 */
    p_result->uc_amplitude = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UC].base_band_amp;
    p_result->uc_phase = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UC].base_band_phase;
    p_result->uc_3rd_harmonic = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UC].thd_arrt[2];
    p_result->uc_5th_harmonic = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UC].thd_arrt[4];
    p_result->uc_7th_harmonic = p_handler->private_data->pfft_data[FFT_INPUT_VOLTAGE_UC].thd_arrt[6];

    /* 5. 计算相位差（相对于AC电压） */
    p_result->ua_ac_phase_diff = p_result->ua_phase - p_result->ac_phase;
    p_result->ub_ac_phase_diff = p_result->ub_phase - p_result->ac_phase;
    p_result->uc_ac_phase_diff = p_result->uc_phase - p_result->ac_phase;

    /* 相位差规范化到[-180°, 180°] */
    while(p_result->ua_ac_phase_diff > 180.0f) p_result->ua_ac_phase_diff -= 360.0f;
    while(p_result->ua_ac_phase_diff < -180.0f) p_result->ua_ac_phase_diff += 360.0f;

    while(p_result->ub_ac_phase_diff > 180.0f) p_result->ub_ac_phase_diff -= 360.0f;
    while(p_result->ub_ac_phase_diff < -180.0f) p_result->ub_ac_phase_diff += 360.0f;

    while(p_result->uc_ac_phase_diff > 180.0f) p_result->uc_ac_phase_diff -= 360.0f;
    while(p_result->uc_ac_phase_diff < -180.0f) p_result->uc_ac_phase_diff += 360.0f;

    p_result->ua_phase = p_result->ua_ac_phase_diff;
    p_result->ub_phase = p_result->ub_ac_phase_diff;
    p_result->uc_phase = p_result->uc_ac_phase_diff;

    /* 6. 设置时间戳和有效标志 */
    p_result->timestamp = start_time;
    p_result->data_valid = 1;

    uint32_t calc_time = p_handler->fft_calcu_system_timebase_interface->pfget_timetick_ms() - start_time;
    FFT_CALCU_LOG_DEUBG("Sync FFT calculation completed in %d ms", calc_time);
    FFT_CALCU_LOG_DEUBG("AC: %.2f V, %.2f°", p_result->ac_amplitude, p_result->ac_phase);
    FFT_CALCU_LOG_DEUBG("UA: %.2f V, %.2f° (diff: %.2f°)", p_result->ua_amplitude, p_result->ua_phase, p_result->ua_ac_phase_diff);
    FFT_CALCU_LOG_DEUBG("UB: %.2f V, %.2f° (diff: %.2f°)", p_result->ub_amplitude, p_result->ub_phase, p_result->ub_ac_phase_diff);
    FFT_CALCU_LOG_DEUBG("UC: %.2f V, %.2f° (diff: %.2f°)", p_result->uc_amplitude, p_result->uc_phase, p_result->uc_ac_phase_diff);

    return FFT_CALCU_OK;
}

/******************************************************************************
 * @brief 发送同步FFT计算结果
 *
 * @param p_handler FFT计算处理器
 * @return fft_calcu_ret_t 发送结果
 *****************************************************************************/
static fft_calcu_ret_t sync_fft_send_result(fft_calcu_handler_t *p_handler)
{
    if(p_handler == NULL || p_handler->private_data == NULL)
    {
        return FFT_CALCU_ERRORPARAMETER;
    }

    sync_fft_result_t *p_result = &p_handler->private_data->sync_result;
    fft_result_event_t result_event;

    if(!p_result->data_valid)
    {
        FFT_CALCU_LOG_ERROR("Sync FFT result is not valid");
        return FFT_CALCU_ERROR;
    }

    /* 发送AC电压结果 */
    result_event.data_property = FFT_INPUT_VOLTAGE_AC;
    result_event.fft_result_data.fundation_data.fft_result_amplitude_data = p_result->ac_amplitude;
    result_event.fft_result_data.fundation_data.fft_result_phase_data = p_result->ac_phase;
    result_event.fft_result_data.fundation_data.fft_result_freq_data = p_result->ac_frequency;

    if(FFT_CALCU_OK != p_handler->fft_get_data_interface->fft_result_data_send(&result_event))
    {
        FFT_CALCU_LOG_ERROR("Failed to send AC voltage FFT result");
        return FFT_CALCU_ERROR;
    }

    /* 发送UA相电压结果 */
    result_event.data_property = FFT_INPUT_VOLTAGE_UA;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amplitude_data = p_result->ua_amplitude;
    result_event.fft_result_data.three_phase_vol_data.fft_result_phase_data = p_result->ua_phase;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amp_3rd_data = p_result->ua_3rd_harmonic;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amp_5rd_data = p_result->ua_5th_harmonic;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amp_7rd_data = p_result->ua_7th_harmonic;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amp_9rd_data = p_result->ua_9th_harmonic;

    if(FFT_CALCU_OK != p_handler->fft_get_data_interface->fft_result_data_send(&result_event))
    {
        FFT_CALCU_LOG_ERROR("Failed to send UA voltage FFT result");
        return FFT_CALCU_ERROR;
    }

    /* 发送UB相电压结果 */
    result_event.data_property = FFT_INPUT_VOLTAGE_UB;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amplitude_data = p_result->ub_amplitude;
    result_event.fft_result_data.three_phase_vol_data.fft_result_phase_data = p_result->ub_phase;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amp_3rd_data = p_result->ub_3rd_harmonic;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amp_5rd_data = p_result->ub_5th_harmonic;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amp_7rd_data = p_result->ub_7th_harmonic;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amp_9rd_data = p_result->ub_9th_harmonic;

    if(FFT_CALCU_OK != p_handler->fft_get_data_interface->fft_result_data_send(&result_event))
    {
        FFT_CALCU_LOG_ERROR("Failed to send UB voltage FFT result");
        return FFT_CALCU_ERROR;
    }

    /* 发送UC相电压结果 */
    result_event.data_property = FFT_INPUT_VOLTAGE_UC;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amplitude_data = p_result->uc_amplitude;
    result_event.fft_result_data.three_phase_vol_data.fft_result_phase_data = p_result->uc_phase;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amp_3rd_data = p_result->uc_3rd_harmonic;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amp_5rd_data = p_result->uc_5th_harmonic;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amp_7rd_data = p_result->uc_7th_harmonic;
    result_event.fft_result_data.three_phase_vol_data.fft_result_amp_9rd_data = p_result->uc_9th_harmonic;

    if(FFT_CALCU_OK != p_handler->fft_get_data_interface->fft_result_data_send(&result_event))
    {
        FFT_CALCU_LOG_ERROR("Failed to send UC voltage FFT result");
        return FFT_CALCU_ERROR;
    }

    FFT_CALCU_LOG_DEUBG("Sync FFT results sent successfully");
    return FFT_CALCU_OK;
}

/******************************************************************************
 * @brief 重置同步FFT状态
 *
 * @param p_handler FFT计算处理器
 *****************************************************************************/
static void sync_fft_reset_status(fft_calcu_handler_t *p_handler)
{
    if(p_handler == NULL || p_handler->private_data == NULL)
    {
        return;
    }

    sync_data_status_t *p_status = &p_handler->private_data->sync_status;
    sync_fft_result_t *p_result = &p_handler->private_data->sync_result;

    /* 重置同步状态 - 清除所有事件位 */
    p_handler->fft_calcu_os_interface->rtos_event_group_clear_bits(
        p_status->sync_event_group,
        FFT_SYNC_READY_BITS | FFT_SYNC_TIMEOUT_BIT | FFT_SYNC_RESET_BIT);

    p_status->ac_timestamp = 0;
    p_status->ua_timestamp = 0;
    p_status->ub_timestamp = 0;
    p_status->uc_timestamp = 0;
    p_status->sync_start_time = 0;
    p_status->sync_timeout_count = 0;

    /* 重置结果 */
    p_result->data_valid = 0;

    FFT_CALCU_LOG_DEUBG("Sync FFT status reset");
}
#endif /* SYNC_FFT_ENABLE */
