/******************************************************************************
 * @file bsp_led_handler.c
 * @brief LED处理器实现文件，支持LED数量管理、注册、开关、定时、呼吸灯等功能
 * <AUTHOR>
 * @version 1.1
 * @date 2024-10-31
 *
 * @copyright Copyright (c) 2024
 *
 * Processing flow (事件驱动架构):
 * 1. LED处理器初始化 - 创建事件队列和处理任务
 * 2. LED驱动注册管理 - 通过事件队列或直接调用
 * 3. LED控制请求 - 统一通过事件队列发送
 * 4. LED任务处理 - led_handler_task统一处理所有事件和状态更新
 * 5. 线程安全保证 - 所有LED操作都在同一任务中执行
 *
 * @par Architecture Notes
 * - 所有LED控制操作都通过事件队列进行，确保线程安全
 * - bsp_led_handler_control()发送控制事件，不直接操作LED
 * - led_handler_task()负责处理所有事件和LED状态更新
 * - 内部函数_led_handler_control_internal()执行实际的LED控制逻辑
 *
 * @par dependencies
 * - bsp_led_handler.h
 * - bsp_led_driver.h
 * - RTOS接口
 *
 * @note 1 tab == 4 spaces!
 *
 *****************************************************************************/

//******************************** Includes *********************************//
#include "bsp_led_handler.h"
//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define LED_HANDLER_MAX_DEVICES        (4)            /* 最大LED设备数量 */
#define LED_HANDLER_TASK_DELAY_MS      (10)            /* 任务延时10ms */
#define LED_HANDLER_BREATH_STEPS       (100)           /* 呼吸灯步数 */

/* RTOS接口宏定义 */
#define RTOS_TASK_CREATE      p_handler_inst->p_led_os_interface->rtos_task_create
#define RTOS_TASK_DELETE      p_handler_inst->p_led_os_interface->rtos_task_delete
#define RTOS_QUEUE_CREATE     p_handler_inst->p_led_os_interface->rtos_queue_create
#define RTOS_QUEUE_DELETE     p_handler_inst->p_led_os_interface->rtos_queue_delete
#define RTOS_QUEUE_SEND       p_handler_inst->p_led_os_interface->rtos_queue_send
#define RTOS_QUEUE_RECEIVE    p_handler_inst->p_led_os_interface->rtos_queue_receive
#define RTOS_MUTEX_CREATE     p_handler_inst->p_led_os_interface->rtos_mutex_create
#define RTOS_MUTEX_DELETE     p_handler_inst->p_led_os_interface->rtos_mutex_delete
#define RTOS_MUTEX_TAKE       p_handler_inst->p_led_os_interface->rtos_mutex_take
#define RTOS_MUTEX_GIVE       p_handler_inst->p_led_os_interface->rtos_mutex_give

typedef enum
{
	LED_HANDLER_NOT_INITED = 0,
	LED_HANDLER_INITED     = 1,
}led_handler_inited_t;

/* LED状态结构体 */
typedef struct
{
    bsp_led_driver_t          *p_led_driver;        /* LED驱动指针 */
    led_control_mode_t        current_mode;         /* 当前实际状态（记录LED的实际运行状态） */
    led_control_param_t       control_param;        /* 控制参数（包含目标模式和参数） */

    /* 状态变量 */
    uint32_t                  last_update_time;     /* 上次更新时间 */
    uint32_t                  cycle_start_time;     /* 周期开始时间 */
    uint32_t                  blink_counter;        /* 闪烁计数器 */
    uint8_t                   current_brightness;   /* 当前亮度 */
    bool                      is_on;                /* 当前开关状态 */
    bool                      is_active;            /* 是否激活 */
    bool                      task_completed;       /* 任务是否完成（定时器到期、闪烁完成等） */

    /* 呼吸灯相关 */
    uint8_t                   breath_step;          /* 呼吸灯步数 */
    bool                      breath_increasing;    /* 呼吸灯递增标志 */

    Node                      list_node;            /* 链表节点 */
}led_state_t;

/* LED处理器私有数据 */
typedef struct bsp_led_handler_priv_data_t
{
    led_handler_inited_t      inited_status;        /* 初始化状态 */
    uint8_t                   registered_count;     /* 已注册LED数量 */
    PNode                     led_list;             /* LED状态链表 */
    uint32_t                  last_update_time;     /* 上次更新时间 */
}bsp_led_handler_priv_data_t;

/* 静态对象池 */
static led_state_t g_led_state_pool[LED_HANDLER_MAX_DEVICES];
static bool g_led_state_pool_used[LED_HANDLER_MAX_DEVICES] = {false};

//******************************** Defines **********************************//

//******************************** Functions ********************************//

/* 内部函数声明 */
static led_state_t* _allocate_led_state(void);
static void _free_led_state(led_state_t* p_led_state);
static led_state_t* _find_led_state_by_which(bsp_led_handler_t *p_handler, led_which_t led_which);

static void _update_led_state(led_state_t *p_led_state, uint32_t current_time);
static void _process_led_blink(led_state_t *p_led_state, uint32_t current_time);
static void _process_led_breath(led_state_t *p_led_state, uint32_t current_time);
static void _process_led_timer(led_state_t *p_led_state, uint32_t current_time);
static uint32_t _get_blink_on_time(uint32_t cycle_time, led_blink_proportion_t proportion);
static uint32_t _get_blink_off_time(uint32_t cycle_time, led_blink_proportion_t proportion);
static led_handler_ret_code_t _led_handler_control_internal(bsp_led_handler_t *p_led_handler, led_control_param_t *p_control_param);
/**
 * @brief 通知LED状态变化（内部函数）
 *
 * @param p_led_state LED状态指针
 * @param new_mode 新的LED模式
 */
static void _notify_led_state_change(led_state_t *p_led_state, led_control_mode_t new_mode)
{
    if (p_led_state != NULL &&
        p_led_state->control_param.event_callback != NULL) {

        led_which_t led_which;
        if (p_led_state->p_led_driver->pfget_led_which(p_led_state->p_led_driver, &led_which) == LED_DRIVER_OK) {
            p_led_state->control_param.event_callback(led_which, new_mode, p_led_state->control_param.p_callback_param);
        }
    }
}
/******************************************************************************
 * @brief 分配LED状态对象
 *
 * @return led_state_t* LED状态对象指针，NULL表示分配失败
 *****************************************************************************/
static led_state_t* _allocate_led_state(void)
{
    for (int i = 0; i < LED_HANDLER_MAX_DEVICES; i++) {
        if (!g_led_state_pool_used[i]) {
            g_led_state_pool_used[i] = true;
            // 清零结构体
            led_state_t *p_state = &g_led_state_pool[i];
            p_state->p_led_driver = NULL;
            p_state->current_mode = LED_MODE_OFF;
            p_state->last_update_time = 0;
            p_state->cycle_start_time = 0;
            p_state->blink_counter = 0;
            p_state->current_brightness = 0;
            p_state->is_on = false;
            p_state->is_active = false;
            p_state->task_completed = false;
            p_state->breath_step = 0;
            p_state->breath_increasing = true;
            return p_state;
        }
    }
    LED_HANDLER_LOG_ERROR("No free LED state slot available");
    return NULL;
}

/******************************************************************************
 * @brief 释放LED状态对象
 *
 * @param p_led_state LED状态对象指针
 *****************************************************************************/
static void _free_led_state(led_state_t* p_led_state)
{
    if (p_led_state == NULL) return;

    // 在对象池中查找并释放
    for (int i = 0; i < LED_HANDLER_MAX_DEVICES; i++) {
        if (&g_led_state_pool[i] == p_led_state) {
            g_led_state_pool_used[i] = false;
            break;
        }
    }
}

/******************************************************************************
 * @brief 根据LED编号查找LED状态
 *
 * @param p_handler LED处理器指针
 * @param led_which LED编号
 * @return led_state_t* LED状态指针，NULL表示未找到
 *****************************************************************************/
static led_state_t* _find_led_state_by_which(bsp_led_handler_t *p_handler, led_which_t led_which)
{
    if (p_handler == NULL || p_handler->p_private_data == NULL) {
        return NULL;
    }

    bsp_led_handler_priv_data_t *p_private_data = p_handler->p_private_data;
    PNode current = p_private_data->led_list;

    while (current != NULL) {
        led_state_t *p_state = container_of(current, led_state_t, list_node);
        if (p_state->p_led_driver != NULL) {
            led_which_t current_which;
            if (p_state->p_led_driver->pfget_led_which(p_state->p_led_driver, &current_which) == LED_DRIVER_OK) {
                if (current_which == led_which) {
                    return p_state;
                }
            }
        }
        current = GetNextNode(current);
    }

    return NULL;
}



/******************************************************************************
 * @brief 获取闪烁亮起时间
 *
 * @param cycle_time 周期时间
 * @param proportion 闪烁比例
 * @return uint32_t 亮起时间
 *****************************************************************************/
static uint32_t _get_blink_on_time(uint32_t cycle_time, led_blink_proportion_t proportion)
{
    switch (proportion) {
        case LED_BLINK_RATIO_1_9: return cycle_time / 10;      /* 10% */
        case LED_BLINK_RATIO_1_4: return cycle_time / 5;       /* 20% */
        case LED_BLINK_RATIO_1_1: return cycle_time / 2;       /* 50% */
        case LED_BLINK_RATIO_4_1: return cycle_time * 4 / 5;   /* 80% */
        case LED_BLINK_RATIO_9_1: return cycle_time * 9 / 10;  /* 90% */
        default: return cycle_time / 2;
    }
}

/******************************************************************************
 * @brief 获取闪烁熄灭时间
 *
 * @param cycle_time 周期时间
 * @param proportion 闪烁比例
 * @return uint32_t 熄灭时间
 *****************************************************************************/
static uint32_t _get_blink_off_time(uint32_t cycle_time, led_blink_proportion_t proportion)
{
    return cycle_time - _get_blink_on_time(cycle_time, proportion);
}

/******************************************************************************
 * @brief 处理LED闪烁模式
 *
 * @param p_led_state LED状态指针
 * @param current_time 当前时间
 *****************************************************************************/
static void _process_led_blink(led_state_t *p_led_state, uint32_t current_time)
{
    if (p_led_state == NULL || p_led_state->p_led_driver == NULL) {
        return;
    }

    uint32_t elapsed_time = current_time - p_led_state->cycle_start_time;
    uint32_t on_time = _get_blink_on_time(p_led_state->control_param.cycle_time_ms,
                                          p_led_state->control_param.proportion);
    uint32_t off_time = _get_blink_off_time(p_led_state->control_param.cycle_time_ms,
                                           p_led_state->control_param.proportion);

    /* 检查是否完成一个周期 */
    if (elapsed_time >= (on_time + off_time)) {
        p_led_state->cycle_start_time = current_time;
        p_led_state->blink_counter++;

        /* 检查是否达到闪烁次数限制 */
        if (p_led_state->control_param.blink_count > 0 &&
            p_led_state->blink_counter >= p_led_state->control_param.blink_count) {
            /* 闪烁完成，关闭LED并标记任务完成 */
            p_led_state->current_mode = LED_MODE_OFF;
            p_led_state->task_completed = true;  /* 标记任务完成 */
            p_led_state->is_on = false;
            p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_OFF);
            return;
        }
        elapsed_time = 0;
    }

    /* 控制LED开关 */
    if (elapsed_time < on_time) {
        if (!p_led_state->is_on) {
            p_led_state->is_on = true;
            p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_ON);
        }
    } else {
        if (p_led_state->is_on) {
            p_led_state->is_on = false;
            p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_OFF);
        }
    }
}

/******************************************************************************
 * @brief 处理LED呼吸灯模式
 *
 * @param p_led_state LED状态指针
 * @param current_time 当前时间
 *****************************************************************************/
static void _process_led_breath(led_state_t *p_led_state, uint32_t current_time)
{
    if (p_led_state == NULL || p_led_state->p_led_driver == NULL) {
        return;
    }

    uint32_t elapsed_time = current_time - p_led_state->last_update_time;
    uint32_t step_time = p_led_state->control_param.breath_period_ms / (LED_HANDLER_BREATH_STEPS * 2);

    if (elapsed_time >= step_time) {
        p_led_state->last_update_time = current_time;

        /* 更新呼吸灯步数 */
        if (p_led_state->breath_increasing) {
            p_led_state->breath_step++;
            if (p_led_state->breath_step >= LED_HANDLER_BREATH_STEPS) {
                p_led_state->breath_increasing = false;
                p_led_state->breath_step = LED_HANDLER_BREATH_STEPS;
            }
        } else {
            p_led_state->breath_step--;
            if (p_led_state->breath_step == 0) {
                p_led_state->breath_increasing = true;
            }
        }

        /* 计算当前亮度 */
        p_led_state->current_brightness = (p_led_state->breath_step * p_led_state->control_param.brightness) / LED_HANDLER_BREATH_STEPS;

        /* 设置LED亮度 */
        p_led_state->p_led_driver->pfset_brightness(p_led_state->p_led_driver, p_led_state->current_brightness);

        /* 如果亮度大于0则开启LED */
        if (p_led_state->current_brightness > 0) {
            if (!p_led_state->is_on) {
                p_led_state->is_on = true;
                p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_ON);
            }
        } else {
            if (p_led_state->is_on) {
                p_led_state->is_on = false;
                p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_OFF);
            }
        }
    }
}

/******************************************************************************
 * @brief 处理LED定时模式
 *
 * @param p_led_state LED状态指针
 * @param current_time 当前时间
 *****************************************************************************/
static void _process_led_timer(led_state_t *p_led_state, uint32_t current_time)
{
    if (p_led_state == NULL || p_led_state->p_led_driver == NULL) {
        return;
    }

    uint32_t elapsed_time = current_time - p_led_state->cycle_start_time;

    if (p_led_state->control_param.mode == LED_MODE_TIMER_ON) {
        /* 定时亮起模式 */
        if (elapsed_time >= p_led_state->control_param.timer_duration_ms) {
            /* 定时时间到，关闭LED并标记任务完成 */
            p_led_state->current_mode = LED_MODE_OFF;
            p_led_state->task_completed = true;  /* 标记任务完成 */
            p_led_state->is_on = false;
            p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_OFF);

            /* 通知状态变化 */
            _notify_led_state_change(p_led_state, LED_MODE_OFF);
        } else {
            /* 保持亮起状态，更新当前状态为TIMER_ON */
            p_led_state->current_mode = LED_MODE_TIMER_ON;
            if (!p_led_state->is_on) {
                p_led_state->is_on = true;
                p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_ON);
            }
        }
    } else if (p_led_state->control_param.mode == LED_MODE_TIMER_OFF) {
        /* 定时关闭模式 */
        if (elapsed_time >= p_led_state->control_param.timer_duration_ms) {
            /* 定时时间到，开启LED并标记任务完成 */
            p_led_state->current_mode = LED_MODE_ON;
            p_led_state->task_completed = true;  /* 标记任务完成 */
            p_led_state->is_on = true;
            p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_ON);
        } else {
            /* 保持关闭状态，更新当前状态为TIMER_OFF */
            p_led_state->current_mode = LED_MODE_TIMER_OFF;
            if (p_led_state->is_on) {
                p_led_state->is_on = false;
                p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_OFF);
            }
        }
    }
}

/******************************************************************************
 * @brief 更新LED状态
 *
 * @param p_led_state LED状态指针
 * @param current_time 当前时间
 *****************************************************************************/
static void _update_led_state(led_state_t *p_led_state, uint32_t current_time)
{
    if (p_led_state == NULL || !p_led_state->is_active) {
        return;
    }

    /*
     * 如果任务已完成（定时器到期、闪烁完成等），则保持当前状态不变
     * 直到用户发送新的控制命令（会重置task_completed标志）
     */
    if (p_led_state->task_completed) {
        /* 任务已完成，保持当前状态 */
        p_led_state->last_update_time = current_time;
        return;
    }

    /* 根据目标模式执行相应的控制逻辑 */
    switch (p_led_state->control_param.mode) {
        case LED_MODE_OFF:
            if (p_led_state->is_on) {
                p_led_state->is_on = false;
                p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_OFF);
            }
            /* 更新当前状态为OFF */
            p_led_state->current_mode = LED_MODE_OFF;
            break;

        case LED_MODE_ON:
            if (!p_led_state->is_on) {
                p_led_state->is_on = true;
                p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_ON);
            }
            /* 更新当前状态为ON */
            p_led_state->current_mode = LED_MODE_ON;
            break;

        case LED_MODE_BLINK:
            _process_led_blink(p_led_state, current_time);
            /* 注意：_process_led_blink可能会设置task_completed标志 */
            break;

        case LED_MODE_BREATH:
            _process_led_breath(p_led_state, current_time);
            /* 当前状态保持为BREATH，具体的亮度由_process_led_breath处理 */
            p_led_state->current_mode = LED_MODE_BREATH;
            break;

        case LED_MODE_TIMER_ON:
        case LED_MODE_TIMER_OFF:
            _process_led_timer(p_led_state, current_time);
            /* 注意：_process_led_timer可能会设置task_completed标志 */
            break;

        case LED_MODE_PWM:
            /* PWM模式，设置亮度 */
            p_led_state->p_led_driver->pfset_brightness(p_led_state->p_led_driver,
                                                        p_led_state->control_param.brightness);
            if (!p_led_state->is_on) {
                p_led_state->is_on = true;
                p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_ON);
            }
            /* 更新当前状态为PWM */
            p_led_state->current_mode = LED_MODE_PWM;
            break;

        default:
            break;
    }

    p_led_state->last_update_time = current_time;
}

/******************************************************************************
 * @brief LED处理器初始化
 *
 * @param p_led_handler LED处理器指针
 * @param p_led_handler_input 输入参数指针
 * @return led_handler_ret_code_t 返回码
 *****************************************************************************/
led_handler_ret_code_t bsp_led_handler_instance(
    bsp_led_handler_t           *p_led_handler,
    led_handler_input_all_arg_t *p_led_handler_input)
{
    LED_HANDLER_LOG_DEBUG("LED handler instance start");

    /* 0.检查输入参数 */
    if (p_led_handler == NULL || p_led_handler_input == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler instance error, input parameters are NULL, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERRORPARAMETER;
    }

    /* 1.检查输入参数内容 */
    if (p_led_handler_input->p_led_os_interface == NULL ||
        p_led_handler_input->p_led_dynamic_allocation == NULL ||
        p_led_handler_input->p_led_timebase_interface == NULL ||
        p_led_handler_input->p_system_config == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler instance error, input interface is NULL, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERRORRESOURCE;
    }

    /* 2.分配私有数据 */
    p_led_handler->p_private_data = (bsp_led_handler_priv_data_t*)
        p_led_handler_input->p_led_dynamic_allocation->pfmalloc(sizeof(bsp_led_handler_priv_data_t));
    if (p_led_handler->p_private_data == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler instance error, failed to allocate private data, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERRORNOMEMORY;
    }

    /* 3.初始化私有数据 */
    bsp_led_handler_priv_data_t *p_private_data = p_led_handler->p_private_data;
    p_private_data->inited_status = LED_HANDLER_NOT_INITED;
    p_private_data->registered_count = 0;
    p_private_data->led_list = NULL;
    p_private_data->last_update_time = 0;

    /* 4.设置接口指针 */
    p_led_handler->p_system_config = p_led_handler_input->p_system_config;
    p_led_handler->p_led_os_interface = p_led_handler_input->p_led_os_interface;
    p_led_handler->p_led_os_critical = p_led_handler_input->p_led_os_critical;
    p_led_handler->p_led_dynamic_allocation = p_led_handler_input->p_led_dynamic_allocation;
    p_led_handler->p_led_timebase_interface = p_led_handler_input->p_led_timebase_interface;

    /* 5.创建事件队列 */
    if(LED_HANDLER_PDTRUE != p_led_handler->p_led_os_interface->rtos_queue_create(
                                                   &p_led_handler->led_handler_event_queue,
                                                   p_led_handler->p_system_config->led_handler_event_queue_size,
                                                   sizeof(led_handler_event_t)))
    {
        LED_HANDLER_LOG_ERROR("LED handler create event queue failed, file:%s, line:%d", __FILE__, __LINE__);
        p_led_handler_input->p_led_dynamic_allocation->pffree(p_led_handler->p_private_data);
        return LED_HANDLER_ERROR;
    }

    /* 6.创建互斥量 */
    if(LED_HANDLER_PDTRUE != p_led_handler->p_led_os_interface->rtos_mutex_create(&p_led_handler->led_handler_mutex))
    {
        LED_HANDLER_LOG_ERROR("LED handler create mutex failed, file:%s, line:%d", __FILE__, __LINE__);
        p_led_handler->p_led_os_interface->rtos_queue_delete(p_led_handler->led_handler_event_queue);
        p_led_handler_input->p_led_dynamic_allocation->pffree(p_led_handler->p_private_data);
        return LED_HANDLER_ERROR;
    }

    /* 7.创建LED处理任务 */
    if(LED_HANDLER_PDTRUE != p_led_handler->p_led_os_interface->rtos_task_create(
                          led_handler_task,
                          "LED_Handler",
                          p_led_handler->p_system_config->led_handler_task_stack_size,
                          p_led_handler,
                          p_led_handler->p_system_config->led_handler_task_priority,
                          &p_led_handler->led_handler_task))
    {
        LED_HANDLER_LOG_ERROR("LED handler create task failed, file:%s, line:%d", __FILE__, __LINE__);
        p_led_handler->p_led_os_interface->rtos_mutex_delete(p_led_handler->led_handler_mutex);
        p_led_handler->p_led_os_interface->rtos_queue_delete(p_led_handler->led_handler_event_queue);
        p_led_handler_input->p_led_dynamic_allocation->pffree(p_led_handler->p_private_data);
        return LED_HANDLER_ERROR;
    }

    /* 8.标记初始化完成 */
    p_private_data->inited_status = LED_HANDLER_INITED;

    LED_HANDLER_LOG_DEBUG("LED handler instance end");
    return LED_HANDLER_OK;
}

/******************************************************************************
 * @brief LED驱动注册
 *
 * @param p_led_handler LED处理器指针
 * @param p_led_driver LED驱动指针
 * @return led_handler_ret_code_t 返回码
 *****************************************************************************/
led_handler_ret_code_t bsp_led_handler_register_driver(
    bsp_led_handler_t *p_led_handler,
    bsp_led_driver_t  *p_led_driver)
{
    LED_HANDLER_LOG_DEBUG("LED handler register driver start");

    /* 0.检查输入参数 */
    if (p_led_handler == NULL || p_led_driver == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler register driver error, input parameters are NULL, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERRORPARAMETER;
    }

    if (p_led_handler->p_private_data == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler register driver error, private data is NULL, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERRORRESOURCE;
    }

    /* 1.检查是否已初始化 */
    bsp_led_handler_priv_data_t *p_private_data = p_led_handler->p_private_data;
    if (p_private_data->inited_status != LED_HANDLER_INITED) {
        LED_HANDLER_LOG_ERROR("LED handler register driver error, handler not initialized, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERROR;
    }

    /* 2.获取LED编号 */
    led_which_t led_which;
    if (p_led_driver->pfget_led_which(p_led_driver, &led_which) != LED_DRIVER_OK) {
        LED_HANDLER_LOG_ERROR("LED handler register driver error, get led which failed, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERROR;
    }

    /* 3.检查是否已注册 */
    if (_find_led_state_by_which(p_led_handler, led_which) != NULL) {
        LED_HANDLER_LOG_ERROR("LED handler register driver error, LED %d already registered, file:%s, line:%d", led_which, __FILE__, __LINE__);
        return LED_HANDLER_ERROR;
    }

    /* 4.分配LED状态对象 */
    led_state_t *p_led_state = _allocate_led_state();
    if (p_led_state == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler register driver error, allocate led state failed, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERRORNOMEMORY;
    }

    /* 5.初始化LED状态 */
    p_led_state->p_led_driver = p_led_driver;
    p_led_state->current_mode = LED_MODE_OFF;
    p_led_state->is_active = false;
    p_led_state->is_on = false;
    p_led_state->task_completed = false;
    p_led_state->last_update_time = 0;
    p_led_state->cycle_start_time = 0;
    p_led_state->blink_counter = 0;
    p_led_state->current_brightness = 0;
    p_led_state->breath_step = 0;
    p_led_state->breath_increasing = true;

    /* 6.加入链表 */
    ADDLinked_List(&p_private_data->led_list, &p_led_state->list_node);
    p_private_data->registered_count++;

    LED_HANDLER_LOG_DEBUG("LED handler register driver end, LED %d registered", led_which);
    return LED_HANDLER_OK;
}

/******************************************************************************
 * @brief LED驱动注销
 *
 * @param p_led_handler LED处理器指针
 * @param led_which LED编号
 * @return led_handler_ret_code_t 返回码
 *****************************************************************************/
led_handler_ret_code_t bsp_led_handler_unregister_driver(
    bsp_led_handler_t *p_led_handler,
    led_which_t       led_which)
{
    LED_HANDLER_LOG_DEBUG("LED handler unregister driver start");

    /* 0.检查输入参数 */
    if (p_led_handler == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler unregister driver error, input parameter is NULL, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERRORPARAMETER;
    }

    if (p_led_handler->p_private_data == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler unregister driver error, private data is NULL, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERRORRESOURCE;
    }

    /* 1.查找LED状态 */
    led_state_t *p_led_state = _find_led_state_by_which(p_led_handler, led_which);
    if (p_led_state == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler unregister driver error, LED %d not found, file:%s, line:%d", led_which, __FILE__, __LINE__);
        return LED_HANDLER_ERROR;
    }

    /* 2.关闭LED */
    if (p_led_state->p_led_driver != NULL) {
        p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_OFF);
    }

    /* 3.从链表中移除 */
    bsp_led_handler_priv_data_t *p_private_data = p_led_handler->p_private_data;
    DELLinked_List(&p_private_data->led_list, &p_led_state->list_node);
    p_private_data->registered_count--;

    /* 4.释放LED状态对象 */
    _free_led_state(p_led_state);

    LED_HANDLER_LOG_DEBUG("LED handler unregister driver end, LED %d unregistered", led_which);
    return LED_HANDLER_OK;
}

/******************************************************************************
 * @brief 获取已注册LED数量
 *
 * @param p_led_handler LED处理器指针
 * @return uint8_t 已注册LED数量
 *****************************************************************************/
uint8_t bsp_led_handler_get_registered_count(bsp_led_handler_t *p_led_handler)
{
    if (p_led_handler == NULL || p_led_handler->p_private_data == NULL) {
        return 0;
    }

    bsp_led_handler_priv_data_t *p_private_data = p_led_handler->p_private_data;
    return p_private_data->registered_count;
}



/******************************************************************************
 * @brief LED控制接口 - 通过事件队列发送控制命令
 *
 * 此函数将LED控制请求封装为事件并发送到事件队列，由led_handler_task统一处理。
 * 这确保了所有LED操作都在同一个任务中执行，保证线程安全。
 *
 * @param p_led_handler LED处理器指针
 * @param p_control_param 控制参数指针
 * @param status 是否从ISR调用
 * @return led_handler_ret_code_t 返回码
 *****************************************************************************/
led_handler_ret_code_t bsp_led_handler_control(
    bsp_led_handler_t      *p_led_handler,
    led_control_param_t    *p_control_param,
    bool                   status)
{
    LED_HANDLER_LOG_DEBUG("LED handler control start");

    /* 0.检查输入参数 */
    if (p_led_handler == NULL || p_control_param == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler control error, input parameters are NULL, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERRORPARAMETER;
    }

    if (p_led_handler->led_handler_event_queue == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler control error, event queue is NULL, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERRORRESOURCE;
    }

    /* 1.创建控制事件 */
    led_handler_event_t control_event;
    control_event.event_type = LED_EVENT_CONTROL;
    control_event.control_param = *p_control_param;
    control_event.p_led_driver = NULL;  /* 控制事件不需要驱动指针 */
    control_event.timestamp = p_led_handler->p_led_timebase_interface->pfget_timetick_ms();

    /* 2.发送事件到队列 */
    led_handler_rtos_ret_code_t ret;
    switch(status)
    {
        case false:
        {
            ret = p_led_handler->p_led_os_interface->rtos_queue_send(
                p_led_handler->led_handler_event_queue, &control_event, 0);
        }break;
        case true:
        {
            ret = p_led_handler->p_led_os_interface->rtos_queue_send_fromisr(
                p_led_handler->led_handler_event_queue, &control_event, NULL);
        }break;
    }
    if (LED_HANDLER_PDTRUE != ret) {
        LED_HANDLER_LOG_ERROR("LED handler control error, failed to send event to queue, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERROR;
    }

    LED_HANDLER_LOG_DEBUG("LED handler control end, LED %d control event sent", p_control_param->led_which);
    return LED_HANDLER_OK;
}

/******************************************************************************
 * @brief 内部LED控制函数 - 实际执行LED控制逻辑
 *
 * 此函数在led_handler_task中被调用，执行实际的LED状态更新和控制。
 * 这是原bsp_led_handler_control函数中直接控制部分的重构版本。
 *
 * @param p_led_handler LED处理器指针
 * @param p_control_param 控制参数指针
 * @return led_handler_ret_code_t 返回码
 *****************************************************************************/
static led_handler_ret_code_t _led_handler_control_internal(
    bsp_led_handler_t      *p_led_handler,
    led_control_param_t    *p_control_param)
{
    LED_HANDLER_LOG_DEBUG("LED handler internal control start");

    /* 0.检查输入参数 */
    if (p_led_handler == NULL || p_control_param == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler internal control error, input parameters are NULL, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERRORPARAMETER;
    }

    if (p_led_handler->p_private_data == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler internal control error, private data is NULL, file:%s, line:%d", __FILE__, __LINE__);
        return LED_HANDLER_ERRORRESOURCE;
    }

    /* 1.查找LED状态 */
    led_state_t *p_led_state = _find_led_state_by_which(p_led_handler, p_control_param->led_which);
    if (p_led_state == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler internal control error, LED %d not found, file:%s, line:%d", p_control_param->led_which, __FILE__, __LINE__);
        return LED_HANDLER_ERROR;
    }

    /* 2.获取当前时间 */
    uint32_t current_time = p_led_handler->p_led_timebase_interface->pfget_timetick_ms();

    /* 3.检查是否需要重置任务完成标志 */
    bool need_reset_task = false;

    /* 如果是新的控制模式，或者是需要重新执行的一次性任务，则重置任务状态 */
    if (p_led_state->control_param.mode != p_control_param->mode) {
        /* 控制模式发生变化，重置任务状态 */
        need_reset_task = true;
    } else if (p_led_state->task_completed) {
        /*
         * 相同模式但任务已完成的情况：
         * - 对于一次性任务（定时器、有限次闪烁），用户重新发送相同命令表示要重新执行
         * - 对于持续性任务（常亮、常闪、呼吸灯），不应该到达这里
         */
        if (p_control_param->mode == LED_MODE_TIMER_ON ||
            p_control_param->mode == LED_MODE_TIMER_OFF ||
            (p_control_param->mode == LED_MODE_BLINK && p_control_param->blink_count > 0)) {
            /* 一次性任务重新执行 */
            need_reset_task = true;
        }
    }

    /* 4.更新控制参数 */
    p_led_state->control_param = *p_control_param;

    /* 5.根据需要重置状态变量 */
    if (need_reset_task) {
        p_led_state->task_completed = false;  /* 重置任务完成标志 */
        p_led_state->cycle_start_time = current_time;
        p_led_state->blink_counter = 0;
        p_led_state->breath_step = 0;
        p_led_state->breath_increasing = true;
    }
    p_led_state->last_update_time = current_time;

    /* 6.根据模式和任务状态设置激活状态 */
    if (p_control_param->mode == LED_MODE_OFF) {
        p_led_state->is_active = false;
        p_led_state->is_on = false;
        p_led_state->p_led_driver->pfled_control(p_led_state->p_led_driver, LED_OFF);
    } else if (p_led_state->task_completed) {
        /* 任务已完成，保持当前状态，但仍需要被处理系统管理 */
        p_led_state->is_active = true;
    } else {
        /* 正常激活状态 */
        p_led_state->is_active = true;
    }

    LED_HANDLER_LOG_DEBUG("LED handler internal control end, LED %d mode set to %d", p_control_param->led_which, p_control_param->mode);
    return LED_HANDLER_OK;
}

/******************************************************************************
 * @brief LED事件发送
 *
 * @param p_led_handler LED处理器指针
 * @param p_event 事件指针
 * @return led_handler_ret_code_t 返回码
 *****************************************************************************/
led_handler_ret_code_t bsp_led_handler_send_event(
    bsp_led_handler_t   *p_led_handler,
    led_handler_event_t *p_event)
{
    if (p_led_handler == NULL || p_event == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler send event error, input parameters are NULL");
        return LED_HANDLER_ERRORPARAMETER;
    }

    if (p_led_handler->led_handler_event_queue == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler send event error, event queue is NULL");
        return LED_HANDLER_ERRORRESOURCE;
    }

    /* 发送事件到队列 */
    led_handler_rtos_ret_code_t ret = p_led_handler->p_led_os_interface->rtos_queue_send(
        p_led_handler->led_handler_event_queue, p_event, 0);

    if (LED_HANDLER_PDTRUE != ret) {
        LED_HANDLER_LOG_ERROR("LED handler send event failed");
        return LED_HANDLER_ERROR;
    }

    return LED_HANDLER_OK;
}

/******************************************************************************
 * @brief LED处理任务 - 事件驱动架构的核心任务
 *
 * 此任务负责：
 * 1. 处理LED事件队列中的所有事件（控制、注册、注销等）
 * 2. 定期更新所有已注册LED的状态（闪烁、呼吸灯等）
 * 3. 确保所有LED操作都在同一任务中执行，保证线程安全
 *
 * 任务执行流程：
 * - 从事件队列接收事件（带超时）
 * - 根据事件类型调用相应的处理函数
 * - 遍历所有LED状态并更新（每10ms一次）
 * - 使用互斥量保护LED状态更新过程
 *
 * @param argument 任务参数(LED处理器指针)
 *****************************************************************************/
void led_handler_task(void *argument)
{
    bsp_led_handler_t *p_led_handler = (bsp_led_handler_t*)argument;
    led_handler_event_t event;

    LED_HANDLER_LOG_INFO("LED handler task started");

    if (p_led_handler == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler task error, handler is NULL");
        return;
    }

    bsp_led_handler_priv_data_t *p_private_data = p_led_handler->p_private_data;
    if (p_private_data == NULL) {
        LED_HANDLER_LOG_ERROR("LED handler task error, private data is NULL");
        return;
    }

    while (1) {
        /* 1.处理事件队列 */
        led_handler_rtos_ret_code_t ret = p_led_handler->p_led_os_interface->rtos_queue_receive(
            p_led_handler->led_handler_event_queue, &event, LED_HANDLER_TASK_DELAY_MS);

        if (LED_HANDLER_PDTRUE == ret) 
        {
            /* 处理接收到的事件 */
            switch (event.event_type) 
            {
                case LED_EVENT_CONTROL:
                    /* 使用内部控制函数处理LED控制事件 */
                    _led_handler_control_internal(p_led_handler, &event.control_param);
                    break;

                case LED_EVENT_REGISTER:
                    if (event.p_led_driver != NULL) {
                        bsp_led_handler_register_driver(p_led_handler, event.p_led_driver);
                    } else {
                        LED_HANDLER_LOG_ERROR("LED register event error, driver pointer is NULL");
                    }
                    break;

                case LED_EVENT_UNREGISTER:
                    bsp_led_handler_unregister_driver(p_led_handler, event.control_param.led_which);
                    break;

                case LED_EVENT_UPDATE:
                    /* 预留的更新事件，当前不需要特殊处理 */
                    LED_HANDLER_LOG_DEBUG("LED update event received");
                    break;

                default:
                    LED_HANDLER_LOG_ERROR("Unknown LED event type: %d", event.event_type);
                    break;
            }
        }

        /* 2.更新所有LED状态 */
        uint32_t current_time = p_led_handler->p_led_timebase_interface->pfget_timetick_ms();

        /* 获取互斥量保护LED状态更新 */
        if (LED_HANDLER_PDTRUE == p_led_handler->p_led_os_interface->\
                                        rtos_mutex_take(p_led_handler->led_handler_mutex, 10)) 
        {
            PNode current = p_private_data->led_list;
            while (current != NULL) {
                led_state_t *p_led_state = container_of(current, led_state_t, list_node);
                _update_led_state(p_led_state, current_time);
                current = GetNextNode(current);
            }
            p_led_handler->p_led_os_interface->rtos_mutex_give(p_led_handler->led_handler_mutex);
        }

        p_private_data->last_update_time = current_time;
    }
}