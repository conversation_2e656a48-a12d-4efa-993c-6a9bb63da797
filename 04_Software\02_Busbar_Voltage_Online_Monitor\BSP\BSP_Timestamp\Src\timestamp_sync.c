#include "timestamp_sync.h"
#include <math.h>
#include <string.h>

/* 数学常量 */
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

/* 默认配置参数 */
#define DEFAULT_TIMER_FREQ_HZ    1000000U    /* 1MHz定时器频率 */
#define DEFAULT_SAMPLE_RATE_HZ   6400U       /* 6.4kHz采样率 */
#define DEFAULT_FFT_LENGTH       1024U       /* 1024点FFT */
#define DEFAULT_NOMINAL_FREQ_HZ  50.0f       /* 50Hz标称频率 */

/* 频率测量参数 */
#define MIN_VALID_FREQ_HZ        45.0f       /* 最小有效频率 */
#define MAX_VALID_FREQ_HZ        55.0f       /* 最大有效频率 */
#define FREQ_FILTER_ALPHA        0.1f        /* 频率滤波系数 */

/* 静态变量 */
static uint32_t s_last_capture_value = 0;
static uint32_t s_last_period_ticks = 0;
static float s_filtered_frequency = DEFAULT_NOMINAL_FREQ_HZ;
static bool s_first_capture = true;

/**
 * @brief 初始化时间戳同步模块
 */
timestamp_sync_ret_t timestamp_sync_init(timestamp_sync_handle_t *handle, 
                                        const timestamp_sync_config_t *config)
{
    if (handle == NULL) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    /* 使用默认配置或用户配置 */
    if (config != NULL) {
        handle->config = *config;
    } else {
        handle->config.timer_freq_hz = DEFAULT_TIMER_FREQ_HZ;
        handle->config.sample_rate_hz = DEFAULT_SAMPLE_RATE_HZ;
        handle->config.fft_length = DEFAULT_FFT_LENGTH;
        handle->config.nominal_freq_hz = DEFAULT_NOMINAL_FREQ_HZ;
    }

    /* 初始化状态 */
    memset(&handle->current_timestamp, 0, sizeof(handle->current_timestamp));
    memset(&handle->freq_measurement, 0, sizeof(handle->freq_measurement));
    memset(&handle->phase_correction, 0, sizeof(handle->phase_correction));
    
    handle->frame_counter = 0;
    handle->initialized = true;
    
    /* 重置静态变量 */
    s_last_capture_value = 0;
    s_last_period_ticks = 0;
    s_filtered_frequency = handle->config.nominal_freq_hz;
    s_first_capture = true;

    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief 过零点中断回调函数
 */
timestamp_sync_ret_t timestamp_sync_zero_cross_callback(timestamp_sync_handle_t *handle,
                                                       uint32_t capture_value,
                                                       zero_cross_edge_t edge)
{
    if (handle == NULL || !handle->initialized) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    uint32_t period_ticks = 0;
    float frequency = 0.0f;

    /* 计算周期 */
    if (!s_first_capture) {
        /* 处理定时器溢出情况 */
        if (capture_value >= s_last_capture_value) {
            period_ticks = capture_value - s_last_capture_value;
        } else {
            /* 16位定时器溢出 */
            period_ticks = (65536 - s_last_capture_value) + capture_value;
        }

        /* 计算频率 */
        if (period_ticks > 0) {
            frequency = (float)handle->config.timer_freq_hz / (float)period_ticks;
            
            /* 频率有效性检查 */
            if (frequency >= MIN_VALID_FREQ_HZ && frequency <= MAX_VALID_FREQ_HZ) {
                /* 低通滤波 */
                s_filtered_frequency = s_filtered_frequency * (1.0f - FREQ_FILTER_ALPHA) + 
                                     frequency * FREQ_FILTER_ALPHA;
                
                /* 更新频率测量结果 */
                handle->freq_measurement.frequency = s_filtered_frequency;
                handle->freq_measurement.period_us = 1000000.0f / s_filtered_frequency;
                handle->freq_measurement.period_ticks = period_ticks;
                handle->freq_measurement.valid = true;
                
                s_last_period_ticks = period_ticks;
            }
        }
    } else {
        s_first_capture = false;
    }

    /* 更新时间戳信息 */
    handle->current_timestamp.capture_count++;
    handle->current_timestamp.t_zero = capture_value;
    handle->current_timestamp.system_tick = HAL_GetTick();
    handle->current_timestamp.edge = edge;
    handle->current_timestamp.valid = true;

    s_last_capture_value = capture_value;

    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief 获取当前时间戳
 */
timestamp_sync_ret_t timestamp_sync_get_current_timestamp(timestamp_sync_handle_t *handle,
                                                         timestamp_sync_data_t *timestamp)
{
    if (handle == NULL || timestamp == NULL || !handle->initialized) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    *timestamp = handle->current_timestamp;
    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief 为AD7606采样数据绑定时间戳
 */
timestamp_sync_ret_t timestamp_sync_bind_adc_data(timestamp_sync_handle_t *handle,
                                                  const int16_t adc_data[8],
                                                  ad7606_timestamped_data_t *timestamped_data)
{
    if (handle == NULL || adc_data == NULL || timestamped_data == NULL || !handle->initialized) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    /* 复制ADC数据 */
    memcpy(timestamped_data->adc_data, adc_data, sizeof(int16_t) * 8);
    
    /* 绑定时间戳 */
    timestamped_data->timestamp = handle->current_timestamp;
    
    /* 设置采样起始时间戳为当前定时器值 */
    /* 这里需要根据实际硬件获取当前定时器CNT值 */
    /* 暂时使用系统tick作为替代 */
    timestamped_data->timestamp.t_sample0 = HAL_GetTick();
    
    /* 分配帧ID */
    timestamped_data->frame_id = handle->frame_counter++;

    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief 计算电网频率
 */
timestamp_sync_ret_t timestamp_sync_calculate_frequency(timestamp_sync_handle_t *handle,
                                                       frequency_measurement_t *measurement)
{
    if (handle == NULL || measurement == NULL || !handle->initialized) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    *measurement = handle->freq_measurement;
    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief 计算FFT相位修正参数
 */
timestamp_sync_ret_t timestamp_sync_calculate_phase_correction(timestamp_sync_handle_t *handle,
                                                              uint32_t t_sample0,
                                                              uint32_t t_zero,
                                                              phase_correction_t *correction)
{
    if (handle == NULL || correction == NULL || !handle->initialized) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    if (!handle->freq_measurement.valid) {
        return TIMESTAMP_SYNC_ERROR;
    }

    /* 计算时间偏移 */
    uint32_t time_diff_ticks;
    if (t_sample0 >= t_zero) {
        time_diff_ticks = t_sample0 - t_zero;
    } else {
        /* 处理定时器溢出 */
        time_diff_ticks = (65536 - t_zero) + t_sample0;
    }

    /* 转换为微秒 */
    float time_offset_us = (float)time_diff_ticks * 1000000.0f / (float)handle->config.timer_freq_hz;
    
    /* 计算相位偏移（度） */
    float phase_offset_deg = (time_offset_us / handle->freq_measurement.period_us) * 360.0f;
    
    /* 归一化到0-360度 */
    while (phase_offset_deg >= 360.0f) {
        phase_offset_deg -= 360.0f;
    }
    while (phase_offset_deg < 0.0f) {
        phase_offset_deg += 360.0f;
    }

    /* 计算采样点偏移 */
    float samples_per_period = (float)handle->config.sample_rate_hz / handle->freq_measurement.frequency;
    uint32_t sample_offset = (uint32_t)(phase_offset_deg / 360.0f * samples_per_period);

    /* 填充修正参数 */
    correction->phase_offset_deg = phase_offset_deg;
    correction->time_offset_us = time_offset_us;
    correction->sample_offset = sample_offset;

    /* 保存到句柄中 */
    handle->phase_correction = *correction;

    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief 应用相位修正到FFT结果
 */
timestamp_sync_ret_t timestamp_sync_apply_phase_correction(float *fft_complex_data,
                                                          uint16_t fft_length,
                                                          const phase_correction_t *correction)
{
    if (fft_complex_data == NULL || correction == NULL) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    /* 将相位偏移转换为弧度 */
    float phase_offset_rad = correction->phase_offset_deg * M_PI / 180.0f;
    
    /* 对FFT结果应用相位修正 */
    for (uint16_t i = 0; i < fft_length; i++) {
        float real = fft_complex_data[2 * i];
        float imag = fft_complex_data[2 * i + 1];
        
        /* 计算幅值和相位 */
        float magnitude = sqrtf(real * real + imag * imag);
        float phase = atan2f(imag, real);
        
        /* 应用相位修正 */
        phase -= phase_offset_rad;
        
        /* 转换回实部和虚部 */
        fft_complex_data[2 * i] = magnitude * cosf(phase);
        fft_complex_data[2 * i + 1] = magnitude * sinf(phase);
    }

    return TIMESTAMP_SYNC_OK;
}
