#include "timestamp_sync.h"
#include <math.h>
#include <string.h>

/* 数学常量 */
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

/* 默认配置参数 - 按照文档规范 */
#define DEFAULT_TIMER_FREQ_HZ    1000000U    /* 1MHz定时器频率 */
#define DEFAULT_SAMPLE_RATE_HZ   6400U       /* 6.4kHz采样率 */
#define DEFAULT_FFT_LENGTH       1024U       /* 1024点FFT */
#define DEFAULT_NOMINAL_FREQ_HZ  50.0f       /* 50Hz标称频率 */

/* 频率测量参数 */
#define MIN_VALID_FREQ_HZ        45.0f       /* 最小有效频率 */
#define MAX_VALID_FREQ_HZ        65.0f       /* 最大有效频率 - 按文档要求45~65Hz */
#define FREQ_FILTER_ALPHA        0.1f        /* 频率滤波系数 */
#define FREQ_UPDATE_PERIOD       5           /* 每5个周期更新一次频率 - 按文档要求 */

/* 静态变量 - 按照文档规范分离TIM3和TIM4 */
/* TIM4 - 电网零点捕获相关 */
static uint16_t s_tim4_last_capture = 0;
static uint16_t s_tim4_period_count = 0;
static float s_grid_filtered_frequency = DEFAULT_NOMINAL_FREQ_HZ;
static bool s_tim4_first_capture = true;

/* TIM3 - 信号频率检测相关 */
static uint16_t s_tim3_last_capture = 0;
static float s_signal_filtered_frequency = DEFAULT_NOMINAL_FREQ_HZ;
static bool s_tim3_first_capture = true;

/**
 * @brief 初始化时间戳同步模块 - 按照文档规范
 */
timestamp_sync_ret_t timestamp_sync_init(timestamp_sync_handle_t *handle,
                                        const timestamp_sync_config_t *config)
{
    if (handle == NULL) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    /* 使用默认配置或用户配置 */
    if (config != NULL) {
        handle->config = *config;
    } else {
        handle->config.timer_freq_hz = DEFAULT_TIMER_FREQ_HZ;
        handle->config.sample_rate_hz = DEFAULT_SAMPLE_RATE_HZ;
        handle->config.fft_length = DEFAULT_FFT_LENGTH;
        handle->config.nominal_freq_hz = DEFAULT_NOMINAL_FREQ_HZ;
    }

    /* 初始化电网同步状态 - 按照文档规范 */
    handle->sync_status.capture_count = 0;
    handle->sync_status.t_zero = 0;
    handle->sync_status.grid_freq_hz = handle->config.nominal_freq_hz;
    handle->sync_status.signal_freq_hz = handle->config.nominal_freq_hz;

    /* 初始化频率测量结果 */
    memset(&handle->grid_freq_measurement, 0, sizeof(handle->grid_freq_measurement));
    memset(&handle->signal_freq_measurement, 0, sizeof(handle->signal_freq_measurement));
    memset(&handle->phase_correction, 0, sizeof(handle->phase_correction));

    handle->last_t_zero = 0;
    handle->frame_counter = 0;
    handle->initialized = true;
    handle->overflow_reset_pending = false;

    /* 重置静态变量 */
    s_tim4_last_capture = 0;
    s_tim4_period_count = 0;
    s_grid_filtered_frequency = handle->config.nominal_freq_hz;
    s_tim4_first_capture = true;

    s_tim3_last_capture = 0;
    s_signal_filtered_frequency = handle->config.nominal_freq_hz;
    s_tim3_first_capture = true;

    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief TIM4零点捕获中断回调函数 - 按照文档规范
 */
timestamp_sync_ret_t timestamp_sync_tim4_zero_cross_callback(timestamp_sync_handle_t *handle,
                                                            uint16_t capture_value)
{
    if (handle == NULL || !handle->initialized) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    uint16_t period_ticks = 0;
    float frequency = 0.0f;

    /* 计算周期 - 16位定时器 */
    if (!s_tim4_first_capture) {
        /* 处理16位定时器溢出情况 */
        if (capture_value >= s_tim4_last_capture) {
            period_ticks = capture_value - s_tim4_last_capture;
        } else {
            /* 16位定时器溢出 */
            period_ticks = (65536 - s_tim4_last_capture) + capture_value;
        }

        /* 计算频率 */
        if (period_ticks > 0) {
            frequency = (float)handle->config.timer_freq_hz / (float)period_ticks;

            /* 频率有效性检查 - 按文档要求45~65Hz */
            if (frequency >= MIN_VALID_FREQ_HZ && frequency <= MAX_VALID_FREQ_HZ) {
                /* 低通滤波 */
                s_grid_filtered_frequency = s_grid_filtered_frequency * (1.0f - FREQ_FILTER_ALPHA) +
                                           frequency * FREQ_FILTER_ALPHA;

                /* 每5个周期更新一次频率 - 按文档要求 */
                s_tim4_period_count++;
                if (s_tim4_period_count >= FREQ_UPDATE_PERIOD) {
                    handle->sync_status.grid_freq_hz = s_grid_filtered_frequency;
                    handle->grid_freq_measurement.frequency = s_grid_filtered_frequency;
                    handle->grid_freq_measurement.period_us = 1000000.0f / s_grid_filtered_frequency;
                    handle->grid_freq_measurement.period_ticks = period_ticks;
                    handle->grid_freq_measurement.valid = true;
                    s_tim4_period_count = 0;
                }
            }
        }
    } else {
        s_tim4_first_capture = false;
    }

    /* 备份上一次过零点时间戳 - 按文档要求 */
    handle->last_t_zero = handle->sync_status.t_zero;

    /* 更新电网同步状态 - 按照文档规范 */
    handle->sync_status.capture_count++;
    handle->sync_status.t_zero = capture_value;

    /* 检查溢出情况 - 按照文档规范 */
    if (handle->sync_status.capture_count >= CAPTURE_COUNT_OVERFLOW_THRESHOLD) {
        handle->overflow_reset_pending = true;
    }

    s_tim4_last_capture = capture_value;

    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief TIM3信号频率捕获中断回调函数 - 按照文档规范
 */
timestamp_sync_ret_t timestamp_sync_tim3_signal_callback(timestamp_sync_handle_t *handle,
                                                        uint16_t capture_value)
{
    if (handle == NULL || !handle->initialized) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    uint16_t period_ticks = 0;
    float frequency = 0.0f;

    /* 计算周期 - 16位定时器 */
    if (!s_tim3_first_capture) {
        /* 处理16位定时器溢出情况 */
        if (capture_value >= s_tim3_last_capture) {
            period_ticks = capture_value - s_tim3_last_capture;
        } else {
            /* 16位定时器溢出 */
            period_ticks = (65536 - s_tim3_last_capture) + capture_value;
        }

        /* 计算频率 */
        if (period_ticks > 0) {
            frequency = (float)handle->config.timer_freq_hz / (float)period_ticks;

            /* 频率有效性检查 */
            if (frequency >= MIN_VALID_FREQ_HZ && frequency <= MAX_VALID_FREQ_HZ) {
                /* 低通滤波 */
                s_signal_filtered_frequency = s_signal_filtered_frequency * (1.0f - FREQ_FILTER_ALPHA) +
                                             frequency * FREQ_FILTER_ALPHA;

                /* 更新信号频率测量结果 */
                handle->sync_status.signal_freq_hz = s_signal_filtered_frequency;
                handle->signal_freq_measurement.frequency = s_signal_filtered_frequency;
                handle->signal_freq_measurement.period_us = 1000000.0f / s_signal_filtered_frequency;
                handle->signal_freq_measurement.period_ticks = period_ticks;
                handle->signal_freq_measurement.valid = true;
            }
        }
    } else {
        s_tim3_first_capture = false;
    }

    s_tim3_last_capture = capture_value;

    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief 获取电网同步状态 - 按照文档规范
 */
timestamp_sync_ret_t timestamp_sync_get_grid_status(timestamp_sync_handle_t *handle,
                                                   GridSyncStatus *status)
{
    if (handle == NULL || status == NULL || !handle->initialized) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    *status = handle->sync_status;
    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief 生成压缩时间戳 - 按照文档规范
 */
uint32_t timestamp_sync_generate_packed_timestamp(timestamp_sync_handle_t *handle,
                                                  uint16_t t_sample0)
{
    if (handle == NULL || !handle->initialized) {
        return 0;
    }

    /* 使用文档规范的宏生成压缩时间戳 */
    return PACK_TIMESTAMP(handle->sync_status.capture_count, t_sample0);
}

/**
 * @brief 计算FFT相位修正参数 - 按照文档规范使用压缩时间戳
 */
timestamp_sync_ret_t timestamp_sync_calculate_phase_correction(timestamp_sync_handle_t *handle,
                                                              uint32_t packed_timestamp,
                                                              phase_correction_t *correction)
{
    if (handle == NULL || correction == NULL || !handle->initialized) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    if (!handle->grid_freq_measurement.valid) {
        return TIMESTAMP_SYNC_ERROR;
    }

    /* 解包时间戳 - 按照文档规范 */
    uint16_t capture_count = UNPACK_CAPTURE(packed_timestamp);
    uint16_t t_sample0 = UNPACK_T0(packed_timestamp);
    uint16_t t_zero = handle->sync_status.t_zero;

    /* 计算时间偏移 - 16位定时器 */
    uint16_t time_diff_ticks;
    if (t_sample0 >= t_zero) {
        time_diff_ticks = t_sample0 - t_zero;
    } else {
        /* 处理16位定时器溢出 */
        time_diff_ticks = (65536 - t_zero) + t_sample0;
    }

    /* 转换为微秒 */
    float time_offset_us = (float)time_diff_ticks * 1000000.0f / (float)handle->config.timer_freq_hz;

    /* 计算相位偏移（度） - 使用电网频率 */
    float phase_offset_deg = (time_offset_us / handle->grid_freq_measurement.period_us) * 360.0f;

    /* 归一化到0-360度 */
    while (phase_offset_deg >= 360.0f) {
        phase_offset_deg -= 360.0f;
    }
    while (phase_offset_deg < 0.0f) {
        phase_offset_deg += 360.0f;
    }

    /* 计算采样点偏移 */
    float samples_per_period = (float)handle->config.sample_rate_hz / handle->grid_freq_measurement.frequency;
    uint32_t sample_offset = (uint32_t)(phase_offset_deg / 360.0f * samples_per_period);

    /* 填充修正参数 */
    correction->phase_offset_deg = phase_offset_deg;
    correction->time_offset_us = time_offset_us;
    correction->sample_offset = sample_offset;

    /* 保存到句柄中 */
    handle->phase_correction = *correction;

    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief 应用相位修正到FFT结果
 */
timestamp_sync_ret_t timestamp_sync_apply_phase_correction(float *fft_complex_data,
                                                          uint16_t fft_length,
                                                          const phase_correction_t *correction)
{
    if (fft_complex_data == NULL || correction == NULL) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    /* 将相位偏移转换为弧度 */
    float phase_offset_rad = correction->phase_offset_deg * M_PI / 180.0f;

    /* 对FFT结果应用相位修正 */
    for (uint16_t i = 0; i < fft_length; i++) {
        float real = fft_complex_data[2 * i];
        float imag = fft_complex_data[2 * i + 1];

        /* 计算幅值和相位 */
        float magnitude = sqrtf(real * real + imag * imag);
        float phase = atan2f(imag, real);

        /* 应用相位修正 */
        phase -= phase_offset_rad;

        /* 转换回实部和虚部 */
        fft_complex_data[2 * i] = magnitude * cosf(phase);
        fft_complex_data[2 * i + 1] = magnitude * sinf(phase);
    }

    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief 检查并处理capture_count溢出 - 按照文档规范
 */
timestamp_sync_ret_t timestamp_sync_check_overflow(timestamp_sync_handle_t *handle)
{
    if (handle == NULL || !handle->initialized) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }

    if (handle->overflow_reset_pending) {
        /* 清零capture_count - 按照文档规范 */
        handle->sync_status.capture_count = 0;
        handle->overflow_reset_pending = false;

        /* 清空所有缓冲区 - 按照文档规范 */
        timestamp_sync_clear_all_buffers();

        return TIMESTAMP_SYNC_ERROR_OVERFLOW;
    }

    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief 清空所有缓冲区 - 按照文档规范
 */
timestamp_sync_ret_t timestamp_sync_clear_all_buffers(void)
{
    /* 这里应该调用具体的缓冲区清空函数 */
    /* 例如：清空AD7606数据缓冲和分析队列 */
    /* 通知FFT模块重置状态 */

    /* 具体实现需要根据系统架构来定义 */
    /* 这里提供接口框架 */

    return TIMESTAMP_SYNC_OK;
}
