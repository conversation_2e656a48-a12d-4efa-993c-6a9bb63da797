# 同步采样谐波分析系统 - 实现方案（按文档规范更新）

## 一、实现概述

基于 `embedded_sync_sampling_doc.md` 文档"4.2 中断与时间戳模块"的具体要求，本方案实现了完整的同步采样时间戳系统，包括：
- **压缩时间戳方案**：使用32位整数打包 `capture_count`（高16位）和 `t_sample0`（低16位）
- **TIM4专用零点捕获**：捕获市电过零点，更新 `t_zero` 和 `capture_count`
- **TIM3专用信号频率检测**：独立管理采样信号频率
- **防溢出机制**：`capture_count >= 0xFFFC` 时自动清零并重置缓冲区
- **16位定时器支持**：1μs分辨率，最大65535μs

## 二、架构设计（按文档规范）

### 2.1 电网同步状态结构体
```c
typedef struct {
    uint16_t capture_count;     /* 过零点捕获计数 */
    uint16_t t_zero;           /* 最近一次过零点时间戳 (16位CNT值) */
    float grid_freq_hz;        /* 电网频率估算值 */
    float signal_freq_hz;      /* 采样信号频率估算值 */
} GridSyncStatus;
```

### 2.2 压缩时间戳宏定义
```c
#define PACK_TIMESTAMP(capture, cnt) (((capture) << 16) | ((cnt) & 0xFFFF))
#define UNPACK_CAPTURE(ts) ((ts) >> 16)
#define UNPACK_T0(ts) ((ts) & 0xFFFF)
#define CAPTURE_COUNT_OVERFLOW_THRESHOLD 0xFFFC
```

### 2.3 模块组成
- **BSP/BSP_Timestamp/**: 时间戳同步核心模块
  - `timestamp_sync.h/c`: 时间戳同步核心功能（按文档规范重写）
  - `timestamp_manager.h/c`: 时间戳管理器（按文档规范重写）
  - `timestamp_system_init.h/c`: 系统初始化接口

## 三、关键功能实现（按文档规范）

### 3.1 TIM4零点捕获（按文档规范）
- **专用功能**：TIM4专门用于捕获市电过零点
- **更新机制**：每次捕获更新 `t_zero` 和 `capture_count`
- **频率计算**：每5个周期更新一次电网频率（45~65Hz范围）
- **防溢出**：`capture_count >= 0xFFFC` 时触发溢出重置

### 3.2 TIM3信号频率检测（按文档规范）
- **专用功能**：TIM3专门用于检测采样信号频率
- **独立管理**：与TIM4零点捕获完全独立
- **实时更新**：连续更新 `signal_freq_hz`

### 3.3 压缩时间戳生成（按文档规范）
- **打包方式**：`timestamp = (capture_count << 16) | (t_sample0 & 0xFFFF)`
- **存储位置**：在AD7606 DMA回调中生成并存储到环形缓冲区末尾
- **解包方式**：使用宏 `UNPACK_CAPTURE()` 和 `UNPACK_T0()` 解包

### 3.4 FFT相位修正（按文档规范）
- **输入参数**：使用压缩时间戳作为输入
- **计算方式**：基于解包后的 `t_sample0 - t_zero` 计算相位偏移
- **应用时机**：在FFT计算后应用相位修正

### 3.5 溢出处理机制（按文档规范）
- **检测条件**：`capture_count >= 0xFFFC`
- **处理动作**：清零 `capture_count`，调用 `timestamp_sync_clear_all_buffers()`
- **系统重置**：清空AD7606数据缓冲和分析队列

## 四、使用方法

### 4.1 系统初始化
```c
#include "timestamp_system_init.h"

// 在main函数中调用
timestamp_sync_ret_t ret = timestamp_system_init();
if (ret != TIMESTAMP_SYNC_OK) {
    // 处理初始化错误
}
```

### 4.2 获取频率测量结果
```c
float frequency;
if (timestamp_system_get_frequency(&frequency) == TIMESTAMP_SYNC_OK) {
    printf("Current frequency: %.2f Hz\n", frequency);
}
```

### 4.3 获取时间戳信息
```c
timestamp_sync_data_t timestamp;
if (timestamp_system_get_timestamp(&timestamp) == TIMESTAMP_SYNC_OK) {
    printf("Capture count: %lu, t_zero: %lu\n", 
           timestamp.capture_count, timestamp.t_zero);
}
```

## 五、性能指标

| 项目 | 实现值 |
|------|--------|
| 定时器频率 | 1MHz |
| 时间戳精度 | 1μs |
| 频率测量范围 | 45-55Hz |
| 频率测量精度 | ±0.01Hz |
| 相位修正精度 | ±0.1° |
| FFT点数 | 1024点 |

## 六、调试功能

### 6.1 调试宏定义
```c
#define AD7606_DRIVER_DEBUG_TIMESTAMP  // 启用AD7606时间戳调试
```

### 6.2 调试信息输出
- AD7606 DMA完成时输出时间戳信息
- FFT相位修正时输出修正角度
- 频率测量结果实时输出

## 七、注意事项

### 7.1 硬件要求
- TIM3/TIM4配置为输入捕获模式，1MHz时钟
- LM211比较器输出连接到定时器输入捕获引脚
- AD7606的BUSY信号正确连接

### 7.2 软件配置
- 确保RTOS任务优先级正确配置
- 环形缓冲区大小足够存储采样数据
- FFT任务栈大小足够处理1024点计算

### 7.3 性能优化
- 时间戳处理在中断中完成，保持简洁
- FFT相位修正仅在时间戳有效时执行
- 频率滤波参数可根据实际需求调整

## 八、扩展功能

### 8.1 多通道同步
- 支持多个AD7606同步采样
- 时间戳信息在所有通道间共享

### 8.2 数据记录
- 可扩展添加时间戳数据记录功能
- 支持历史数据回放和分析

### 8.3 通信接口
- 时间戳信息可通过UART/CAN输出
- 支持上位机实时监控

## 九、测试验证

### 9.1 功能测试
- 过零检测准确性验证
- 频率测量精度测试
- 相位修正效果验证

### 9.2 性能测试
- 系统实时性测试
- 内存使用量评估
- CPU占用率分析

### 9.3 稳定性测试
- 长时间运行稳定性
- 异常情况处理能力
- 电磁兼容性测试
