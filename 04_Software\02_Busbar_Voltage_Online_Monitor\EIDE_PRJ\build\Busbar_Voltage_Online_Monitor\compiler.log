>>> cc

"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG" (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 54: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ" (declared at line 39)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/handler/Inc/bsp_adc_collect_xxx_handler.h", line 58: Warning:  #47-D: incompatible redefinition of macro "ADC_COLLECT_LOG_IRQ" (declared at line 39)
  #define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
          ^
".\..\BSP\BSP_AD7606\handler\Src\bsp_adc_collect_xxx_handler.c", line 767: Warning:  #550-D: variable "ret_code" was set but never used
      uint8_t ret_code;
              ^
.\..\BSP\BSP_AD7606\handler\Src\bsp_adc_collect_xxx_handler.c: 4 warnings, 0 errors
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG" (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 54: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ" (declared at line 39)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
".\..\BSP\BSP_AD7606\hal_driver\Src\ad7606_driver.c", line 592: Error:  #513: a value of type "pf_get_timestamp_fun_t *" cannot be assigned to an entity of type "pf_get_timestamp_fun_t"
      psdata->pf_get_timestamp          = pinput->pf_get_timestamp;
                                        ^
".\..\BSP\BSP_AD7606\hal_driver\Src\ad7606_driver.c", line 734: Error:  #852: expression must be a pointer to a complete object type
          memcpy((void*)(current_write_addr + psdata->pring_buff->pfring_buff_get_item_size(psdata->pring_buff) - sizeof(uint32_t)),
                         ^
".\..\BSP\BSP_AD7606\hal_driver\Src\ad7606_driver.c", line 457: Warning:  #177-D: function "bsp_ad7606_irq_callback_fun" was declared but never referenced
  static ad7606_driver_ret_code_t bsp_ad7606_irq_callback_fun(void *param)
                                  ^
.\..\BSP\BSP_AD7606\hal_driver\Src\ad7606_driver.c: 3 warnings, 2 errors
"../MCU_Peripherals_Drivers/Inc/mcu_spi_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "MCU_SPI_DRIVER_DEBUG" (declared at line 95 of "../SYSTEM/Inc/system_config.h")
  #define MCU_SPI_DRIVER_DEBUG(...)
          ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG" (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 54: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ" (declared at line 39)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/handler/Inc/bsp_adc_collect_xxx_handler.h", line 58: Warning:  #47-D: incompatible redefinition of macro "ADC_COLLECT_LOG_IRQ" (declared at line 39)
  #define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
          ^
".\..\Core\Src\freertos.c", line 71: Warning:  #1295-D: Deprecated declaration config_elog - give arg types
  void config_elog();
       ^
.\..\Core\Src\freertos.c: 5 warnings, 0 errors
"../MCU_Peripherals_Drivers/Inc/mcu_spi_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "MCU_SPI_DRIVER_DEBUG" (declared at line 95 of "../SYSTEM/Inc/system_config.h")
  #define MCU_SPI_DRIVER_DEBUG(...)
          ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG" (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 54: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ" (declared at line 39)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/handler/Inc/bsp_adc_collect_xxx_handler.h", line 58: Warning:  #47-D: incompatible redefinition of macro "ADC_COLLECT_LOG_IRQ" (declared at line 39)
  #define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
          ^
".\..\SYSTEM\Src\system_adaption.c", line 787: Warning:  #175-D: subscript out of range
              input_data[FFT_INPUT_VOLTAGE_UC].data_value    = event.data[4];
                                                               ^
".\..\SYSTEM\Src\system_adaption.c", line 811: Warning:  #175-D: subscript out of range
              input_data[FFT_INPUT_VOLTAGE_UC].data_value    = event.data[4];
                                                               ^
".\..\SYSTEM\Src\system_adaption.c", line 756: Warning:  #550-D: variable "fft_calcu_adc_arg" was set but never used
      static fft_calcu_adc_arg_t fft_calcu_adc_arg;
                                 ^
".\..\SYSTEM\Src\system_adaption.c", line 897: Warning:  #550-D: variable "ac_phase_data" was set but never used
      static float ac_phase_data = 0.0f;
                   ^
".\..\SYSTEM\Src\system_adaption.c", line 1552: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, const uint8_t *, uint16_t, uint32_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
                           HAL_UART_Transmit,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1553: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, const uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UART_Transmit_IT,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1554: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t, uint32_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
                           HAL_UART_Receive,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1555: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UART_Receive_IT,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1556: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UARTEx_ReceiveToIdle_IT))
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1945: Warning:  #144-D: a value of type "void *(*)(const char *, uint32_t, _Bool, void *, void (*)(void *))" cannot be used to initialize an entity of type "rs485_os_ret_t (*)(const char *, uint32_t, void *, void (*)(void *), void **)"
          .rtos_timer_create  = (void*(*)(const char*, uint32_t, bool, void*, void(*)(void*)))os_timer_create,
                                ^
".\..\SYSTEM\Src\system_adaption.c", line 2297: Error:  #5: cannot open source input file "timestamp_manager.h": No such file or directory
  #include "timestamp_manager.h"
                                ^
.\..\SYSTEM\Src\system_adaption.c: 14 warnings, 1 error
"../MCU_Peripherals_Drivers/Inc/mcu_spi_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "MCU_SPI_DRIVER_DEBUG" (declared at line 95 of "../SYSTEM/Inc/system_config.h")
  #define MCU_SPI_DRIVER_DEBUG(...)
          ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG" (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 54: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ" (declared at line 39)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/handler/Inc/bsp_adc_collect_xxx_handler.h", line 58: Warning:  #47-D: incompatible redefinition of macro "ADC_COLLECT_LOG_IRQ" (declared at line 39)
  #define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
          ^
.\..\Core\Src\main.c: 4 warnings, 0 errors

>>> ld

