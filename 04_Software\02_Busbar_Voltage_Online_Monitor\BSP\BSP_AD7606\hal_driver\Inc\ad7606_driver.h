/******************************************************************************
 * @file ad7606_driver.h
 * @brief 
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-12
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 根据bsp_ad7606_driver_instance函数构造实例
 * 注意事项：
 * 1.输入的GPIO接口必须为数组
 * 2.构造的数组内容必须按ad7606_gpio_quantity_t来排列
 * @par dependencies
 * stdint.h
 * elog.h
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/
#ifndef __AD7606_DRIVER_H__
#define __AD7606_DRIVER_H__

//******************************** Includes *********************************//
#include <stdint.h>
#include <string.h>
#include "elog.h"
#include "linked_list.h"
#include "ringbuff.h"
#include "system_config.h"
//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define AD7606_DRIVER_USE_INTERRUPT
#define AD7606_DRIVER_USE_TIMESTAMP
/* 调试宏定义已迁移到 system_config.h 中统一管理 */

#ifdef AD7606_DRIVER_DEBUG
    #define AD7606_DRIVER_LOG_IRQ
#endif /*  ADC_COLLECT_DEBUG*/

#ifdef AD7606_DRIVER_DEBUG
    #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)

#ifdef AD7606_DRIVER_LOG_D
    #define AD7606_DRIVER_LOG_DEUBG(...) log_d(__VA_ARGS__)
#else
    #define AD7606_DRIVER_LOG_DEUBG(...)
#endif  /* AD7606_DRIVER_LOG_D */

#define AD7606_DRIVER_LOG_ERROR(...) log_e(__VA_ARGS__)

#ifdef AD7606_DRIVER_LOG_IRQ
    #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
#else
    #define AD7606_DRIVER_LOG_IRQ(...)
#endif  /* AD7606_DRIVER_LOG_I */

#else
#define AD7606_DRIVER_DEBUG(...) 
#define AD7606_DRIVER_LOG_DEUBG(...)
#define AD7606_DRIVER_LOG_ERROR(...)
#endif

#define AD7606_RTOS_SUPPORTING
#define AD7606_ADC_FS   32767

typedef enum
{
    GPIO_LOW_LEVEL  = 0u,    // 低电平
    GPIO_HIGH_LEVEL = 1,     // 高电平
    GPIO_ERROR_LEVEL,
}gpio_interface_pinstate_t;

#ifdef AD7606_RTOS_SUPPORTING
typedef enum 
{
  AD7606_RTOS_PDFALSE = 0, // 返回值为假
  AD7606_RTOS_PDTRUE  = 1, // 返回值为真
}ad7606_rtos_ret_code_t;
#endif //END OF AD7606_RTOS_SUPPORTING

typedef enum
{
  AD7606_CHANNEL_1 = 0,
  AD7606_CHANNEL_2 = 1,
  AD7606_CHANNEL_3 = 2,
  AD7606_CHANNEL_4 = 3,
  AD7606_CHANNEL_5 = 4,
  AD7606_CHANNEL_6 = 5,
  AD7606_CHANNEL_7 = 6,
  AD7606_CHANNEL_8 = 7,
  AD7606_CHANNEL_QUANTITY, // AD7606通道数量
}ad7606_channels_t;

typedef enum 
{
  // AD7606_BUSY_GPIO表示AD7606的忙信号引脚
  AD7606_BUSY_GPIO  = 0,
  // AD7606_RESET_GPIO表示AD7606的复位引脚
  AD7606_RESET_GPIO = 1,
  // AD7606_CONV_GPIO表示AD7606的转换引脚
  AD7606_CONV_GPIO  = 2,
  // AD7606_OS2_GPIO表示AD7606的过采样引脚2
  AD7606_OS2_GPIO   = 3,
  // AD7606_OS1_GPIO表示AD7606的过采样引脚1
  AD7606_OS1_GPIO   = 4,
  // AD7606_OS0_GPIO表示AD7606的过采样引脚0
  AD7606_OS0_GPIO   = 5,
  // AD7606_RANGE_GPIO表示AD7606的量程引脚
  AD7606_RANGE_GPIO = 6,
  // AD7606_GPIO_QUANTITY表示AD7606的GPIO数量
  AD7606_GPIO_QUANTITY  = 7,
}ad7606_gpio_quantity_t;


typedef enum 
{
    // 无采样
    SAMPLING_NONE = 0,
    // 2倍采样
    SAMPLING_2    = 1,
    // 4倍采样
    SAMPLING_4    = 2,
    // 8倍采样
    SAMPLING_8    = 3,
    // 16倍采样
    SAMPLING_16   = 4,
    // 32倍采样
    SAMPLING_32   = 5,
    // 64倍采样
    SAMPLING_64   = 6,
}ad7606_sampling_mode_t;


typedef enum
{
  // 采样范围为5V
  SAMPLING_RANGE_5V  = 0,
  // 采样范围为10V
  SAMPLING_RANGE_10V = 1,
}ad7606_sampling_range_t;

typedef enum
{
  AD7606_OK             = 0,
  AD7606_ERROR          = 1,
  AD7606_ERRORTIMEOUT   = 2,
  AD7606_ERRORPARAMETER = 3,   
}ad7606_driver_ret_code_t;
//******************************** Defines **********************************//


//******************************** Declaring ********************************//
#ifdef AD7606_DRIVER_USE_TIMESTAMP
typedef uint32_t (*pf_get_timestamp_fun_t)(void);
#endif

/* From Core层：     GPIO 接口            */

typedef struct gpio_interface_t
{
  void                      *pgpio_handler;
  int8_t                    (*pfgpio_init)        (void *pgpio_handler);                            
  int8_t                    (*pfgpio_deinit)      (void *pgpio_handler);                       
  int8_t                    (*pfgpio_write_pin)   (void *pgpio_handler, gpio_interface_pinstate_t pinstate);  
  gpio_interface_pinstate_t (*pfgpio_read_pin)    (void *pgpio_handler);                           
  int8_t                    (*pfgpio_toggle_pin)  (void *pgpio_handler);                         
}gpio_interface_t;

/* From Core层：     GPIO 接口            */

/* From Core层：     SPI 接口            */
typedef int8_t (*pf_irq_callback_fun_t)(void*);
typedef struct spi_interface_t
{
  void   *pspi_handler;
  int8_t (*pfspi_dev_init)              (void* pspi_handler);
  int8_t (*pfspi_dev_deinit)            (void* pspi_handler);
  int8_t (*pfspi_dev_write)             (void* pspi_handler, uint8_t* pdata, uint16_t len, uint32_t timeout);
  int8_t (*pfspi_dev_read)              (void* pspi_handler, uint8_t* pdata, uint16_t len, uint32_t timeout);
  int8_t (*pfspi_dev_read_dma)          (void* pspi_handler, uint8_t* pdata, uint16_t len);
  int8_t (*pfspi_dev_register_callback) (void* pspi_dev, 
                                          pf_irq_callback_fun_t txcallbackfun, 
                                          pf_irq_callback_fun_t rxcallbackfun,
                                          void *p_arg);
}spi_interface_t;
/* From Core层：     SPI 接口            */

/* From Core层：  busy中断回调接口        */
typedef struct 
{
    pf_irq_callback_fun_t *pf_busy_callback_fun;
    void                  **p_arg;
}ad7606_busy_irq_callback_t;
/* From Core层：  busy中断回调接口        */

/* From Core层：   动态分配 接口        */
typedef struct
{
  void *(*pfmalloc)   (uint32_t mem_size);
  void (*pffree)      (void *p_mem);
}ad7606_dynamic_allocation_t;
/* From Core层：   动态分配 接口        */

/* From Core层：   通知采集完成接口      */

typedef int8_t (*pf_notify_collect_finish_fun_t)(void);
/* From Core层：   通知采集完成接口      */

typedef struct ad7606_private_data_t ad7606_private_data_t;

/*  ad7606_driver 实例对象结构          */
typedef struct bsp_ad7606_driver_t
{
  char                      *name;
  ad7606_driver_ret_code_t  (*pfinit)               (struct bsp_ad7606_driver_t* pbsp_ad7606_driver);
  ad7606_driver_ret_code_t  (*pfdeinit)             (struct bsp_ad7606_driver_t* pbsp_ad7606_driver);
  ad7606_driver_ret_code_t  (*pfreset)              (struct bsp_ad7606_driver_t* pbsp_ad7606_driver);
  ad7606_driver_ret_code_t  (*pfstartconvst)        (struct bsp_ad7606_driver_t* pbsp_ad7606_driver);
  uint8_t                   (*pfread_busy)          (struct bsp_ad7606_driver_t* pbsp_ad7606_driver);
  ad7606_driver_ret_code_t  (*pfread_data)          (struct bsp_ad7606_driver_t* pbsp_ad7606_driver, int16_t *pdata, ad7606_channels_t start_channel, uint8_t channels);
  ad7606_driver_ret_code_t  (*pfset_sampling_mode)  (struct bsp_ad7606_driver_t* pbsp_ad7606_driver, ad7606_sampling_mode_t  sampling_mode);
  ad7606_driver_ret_code_t  (*pfset_sampling_range) (struct bsp_ad7606_driver_t* pbsp_ad7606_driver, ad7606_sampling_range_t range);
  ad7606_private_data_t*    private_data;
  Node                      ad7606_node;
}bsp_ad7606_driver_t;

/*  ad7606_driver 输入参数结构            */
typedef struct
{
    gpio_interface_t              * const pgpio_interface;
    spi_interface_t               * const pspi_interface;
    ad7606_dynamic_allocation_t   * const pdynamic_allocation;
    ad7606_busy_irq_callback_t    * const pbusy_irq_callback;
#ifdef AD7606_DRIVER_USE_TIMESTAMP
    pf_get_timestamp_fun_t          const pf_get_timestamp;
#endif
}ad7606_driver_input_t;
/*  ad7606_driver 输入参数结构            */

/*  ad7606_driver 实例对象构造函数          */
ad7606_driver_ret_code_t bsp_ad7606_driver_instance(
                                                    char                           *name,
                                                    ad7606_driver_input_t          * const pinput,
                                                    ring_buff_t                    * const pring_buff,
                                                    pf_notify_collect_finish_fun_t   const pf_notify_collect_finish,
                                                    bsp_ad7606_driver_t            * const pbsp_ad7606_driver
);


//******************************** Declaring ********************************//

#endif //END OF __AD7606_DRIVER_H__