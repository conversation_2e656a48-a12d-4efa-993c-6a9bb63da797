/**
 * @file mutex_debug.h
 * @brief 互斥锁竞态问题诊断工具头文件
 * <AUTHOR> Assistant
 * @date 2024
 */

#ifndef __MUTEX_DEBUG_H__
#define __MUTEX_DEBUG_H__

#include "FreeRTOS.h"
#include "semphr.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 注册要监控的互斥锁
 * @param mutex 互斥锁句柄
 * @param name 互斥锁名称
 * @return pdTRUE: 成功, pdFALSE: 失败
 */
BaseType_t register_mutex_for_debug(SemaphoreHandle_t mutex, const char* name);

/**
 * @brief 安全的互斥锁获取（带调试）
 * @param mutex 互斥锁句柄
 * @param timeout 超时时间
 * @return pdTRUE: 成功, pdFALSE: 失败
 */
BaseType_t debug_mutex_take(SemaphoreHandle_t mutex, TickType_t timeout);

/**
 * @brief 安全的互斥锁释放（带调试）
 * @param mutex 互斥锁句柄
 * @return pdTRUE: 成功, pdFALSE: 失败
 */
BaseType_t debug_mutex_give(SemaphoreHandle_t mutex);

/**
 * @brief 打印互斥锁统计信息
 */
void print_mutex_debug_stats(void);

/**
 * @brief 检查互斥锁死锁情况
 */
void check_mutex_deadlock(void);

/**
 * @brief 重置互斥锁调试统计
 */
void reset_mutex_debug_stats(void);

/**
 * @brief 互斥锁调试主函数
 */
void mutex_debug_main(void);

/* 便捷宏定义 */
#ifdef DEBUG_MUTEX
#define MUTEX_TAKE(mutex, timeout) debug_mutex_take(mutex, timeout)
#define MUTEX_GIVE(mutex) debug_mutex_give(mutex)
#else
#define MUTEX_TAKE(mutex, timeout) xSemaphoreTake(mutex, timeout)
#define MUTEX_GIVE(mutex) xSemaphoreGive(mutex)
#endif

#ifdef __cplusplus
}
#endif

#endif /* __MUTEX_DEBUG_H__ */
