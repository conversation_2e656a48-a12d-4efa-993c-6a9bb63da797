#ifndef __TIMESTAMP_UTILS_H__
#define __TIMESTAMP_UTILS_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include <math.h>

/* 时间戳工具模块 - 提供通用的时间戳处理功能 */

/* 压缩时间戳操作宏 - 按文档规范 */
#define TIMESTAMP_PACK(capture, cnt)    (((uint32_t)(capture) << 16) | ((uint32_t)(cnt) & 0xFFFF))
#define TIMESTAMP_UNPACK_CAPTURE(ts)    ((uint16_t)((ts) >> 16))
#define TIMESTAMP_UNPACK_T0(ts)         ((uint16_t)((ts) & 0xFFFF))

/* 16位定时器溢出处理宏 */
#define TIMESTAMP_TIMER_DIFF_16BIT(current, last) \
    (((current) >= (last)) ? ((current) - (last)) : ((65536 - (last)) + (current)))

/* 时间戳工具返回值 */
typedef enum {
    TIMESTAMP_UTILS_OK = 0,
    TIMESTAMP_UTILS_ERROR = -1,
    TIMESTAMP_UTILS_ERROR_PARAM = -2,
    TIMESTAMP_UTILS_ERROR_OVERFLOW = -3
} timestamp_utils_ret_t;

/* 频率测量结果结构体 */
typedef struct {
    float frequency_hz;         /* 频率值 (Hz) */
    float period_us;           /* 周期 (微秒) */
    uint16_t period_ticks;     /* 周期 (定时器ticks) */
    bool valid;                /* 测量结果有效标志 */
    uint32_t update_count;     /* 更新次数 */
} timestamp_freq_result_t;

/* 相位修正参数结构体 */
typedef struct {
    float phase_offset_deg;    /* 相位偏移 (度) */
    float phase_offset_rad;    /* 相位偏移 (弧度) */
    float time_offset_us;      /* 时间偏移 (微秒) */
    uint32_t sample_offset;    /* 采样点偏移 */
} timestamp_phase_correction_t;

/* 时间戳统计信息结构体 */
typedef struct {
    uint32_t total_captures;   /* 总捕获次数 */
    uint32_t valid_captures;   /* 有效捕获次数 */
    uint32_t overflow_count;   /* 溢出次数 */
    float avg_frequency;       /* 平均频率 */
    float min_frequency;       /* 最小频率 */
    float max_frequency;       /* 最大频率 */
} timestamp_statistics_t;

/* 压缩时间戳操作函数 */

/**
 * @brief 打包时间戳
 * @param capture_count 捕获计数
 * @param t_sample0 采样起始时间
 * @return uint32_t 压缩后的时间戳
 */
static inline uint32_t timestamp_utils_pack(uint16_t capture_count, uint16_t t_sample0)
{
    return TIMESTAMP_PACK(capture_count, t_sample0);
}

/**
 * @brief 解包时间戳 - 获取捕获计数
 * @param packed_timestamp 压缩时间戳
 * @return uint16_t 捕获计数
 */
static inline uint16_t timestamp_utils_unpack_capture(uint32_t packed_timestamp)
{
    return TIMESTAMP_UNPACK_CAPTURE(packed_timestamp);
}

/**
 * @brief 解包时间戳 - 获取采样起始时间
 * @param packed_timestamp 压缩时间戳
 * @return uint16_t 采样起始时间
 */
static inline uint16_t timestamp_utils_unpack_t0(uint32_t packed_timestamp)
{
    return TIMESTAMP_UNPACK_T0(packed_timestamp);
}

/**
 * @brief 计算16位定时器时间差
 * @param current_cnt 当前计数值
 * @param last_cnt 上次计数值
 * @return uint16_t 时间差
 */
static inline uint16_t timestamp_utils_calc_timer_diff(uint16_t current_cnt, uint16_t last_cnt)
{
    return TIMESTAMP_TIMER_DIFF_16BIT(current_cnt, last_cnt);
}

/* 频率计算函数 */

/**
 * @brief 计算频率（基于周期ticks）
 * @param period_ticks 周期ticks
 * @param timer_freq_hz 定时器频率
 * @return float 频率值 (Hz)
 */
float timestamp_utils_calc_frequency(uint16_t period_ticks, uint32_t timer_freq_hz);

/**
 * @brief 频率有效性检查
 * @param frequency 频率值
 * @param min_freq 最小有效频率
 * @param max_freq 最大有效频率
 * @return bool 是否有效
 */
bool timestamp_utils_is_frequency_valid(float frequency, float min_freq, float max_freq);

/**
 * @brief 频率低通滤波
 * @param current_freq 当前频率
 * @param new_freq 新频率
 * @param alpha 滤波系数 (0-1)
 * @return float 滤波后的频率
 */
float timestamp_utils_filter_frequency(float current_freq, float new_freq, float alpha);

/* 相位计算函数 */

/**
 * @brief 计算相位偏移
 * @param t_sample0 采样起始时间
 * @param t_zero 过零点时间
 * @param frequency 频率
 * @param timer_freq_hz 定时器频率
 * @param correction 输出相位修正参数
 * @return timestamp_utils_ret_t 返回值
 */
timestamp_utils_ret_t timestamp_utils_calc_phase_correction(
    uint16_t t_sample0,
    uint16_t t_zero,
    float frequency,
    uint32_t timer_freq_hz,
    timestamp_phase_correction_t *correction
);

/**
 * @brief 应用相位修正到FFT结果
 * @param fft_complex_data FFT复数数据
 * @param fft_length FFT长度
 * @param correction 相位修正参数
 * @return timestamp_utils_ret_t 返回值
 */
timestamp_utils_ret_t timestamp_utils_apply_phase_correction(
    float *fft_complex_data,
    uint16_t fft_length,
    const timestamp_phase_correction_t *correction
);

/* 统计函数 */

/**
 * @brief 初始化统计信息
 * @param stats 统计信息结构体
 */
void timestamp_utils_init_statistics(timestamp_statistics_t *stats);

/**
 * @brief 更新统计信息
 * @param stats 统计信息结构体
 * @param frequency 新的频率值
 * @param is_valid 是否有效
 */
void timestamp_utils_update_statistics(timestamp_statistics_t *stats, float frequency, bool is_valid);

/**
 * @brief 打印统计信息
 * @param stats 统计信息结构体
 */
void timestamp_utils_print_statistics(const timestamp_statistics_t *stats);

/* 调试辅助函数 */

/**
 * @brief 打印压缩时间戳信息
 * @param packed_timestamp 压缩时间戳
 * @param prefix 前缀字符串
 */
void timestamp_utils_print_packed_timestamp(uint32_t packed_timestamp, const char *prefix);

/**
 * @brief 打印频率测量结果
 * @param result 频率测量结果
 * @param prefix 前缀字符串
 */
void timestamp_utils_print_freq_result(const timestamp_freq_result_t *result, const char *prefix);

#ifdef __cplusplus
}
#endif

#endif /* __TIMESTAMP_UTILS_H__ */
