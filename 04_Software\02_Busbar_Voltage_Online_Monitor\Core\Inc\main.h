/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define RS485_DE_Pin GPIO_PIN_1
#define RS485_DE_GPIO_Port GPIOA
#define AD7606_BUSY_Pin GPIO_PIN_4
#define AD7606_BUSY_GPIO_Port GPIOA
#define AD7606_BUSY_EXTI_IRQn EXTI4_IRQn
#define AD7606_RESET_Pin GPIO_PIN_5
#define AD7606_RESET_GPIO_Port GPIOA
#define AD7606_CONV_Pin GPIO_PIN_6
#define AD7606_CONV_GPIO_Port GPIOA
#define AD7606_OS2_Pin GPIO_PIN_7
#define AD7606_OS2_GPIO_Port GPIOA
#define AD7606_OS1_Pin GPIO_PIN_4
#define AD7606_OS1_GPIO_Port GPIOC
#define AD7606_OS0_Pin GPIO_PIN_5
#define AD7606_OS0_GPIO_Port GPIOC
#define AD7606_RANGE_Pin GPIO_PIN_0
#define AD7606_RANGE_GPIO_Port GPIOB
#define SPI2_CS_Pin GPIO_PIN_12
#define SPI2_CS_GPIO_Port GPIOB
#define LED_RUN_Pin GPIO_PIN_8
#define LED_RUN_GPIO_Port GPIOB
#define LED_485_Pin GPIO_PIN_9
#define LED_485_GPIO_Port GPIOB

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
