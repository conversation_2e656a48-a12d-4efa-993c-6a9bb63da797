/******************************************************************************
 * @file led_handler_example.c
 * @brief LED处理器使用示例
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-31
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 1. 初始化LED处理器
 * 2. 注册LED驱动
 * 3. 控制LED各种模式
 * 
 * @par dependencies
 * - bsp_led_handler.h
 * - bsp_led_driver.h
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/

//******************************** Includes *********************************//
#include "bsp_led_handler.h"
#include "bsp_led_driver.h"
//******************************** Includes *********************************//

//******************************** Example **********************************//

/* 全局变量 */
static bsp_led_handler_t g_led_handler;
static bsp_led_driver_t g_led_run_driver;
static bsp_led_driver_t g_led_485_driver;
static bsp_led_driver_t g_led_power_driver;

/* 系统配置 */
static led_handler_system_config_t g_led_system_config = {
    .led_handler_task_priority = LED_HANDLER_TASK_PRIORITY,
    .led_handler_task_stack_size = LED_HANDLER_TASK_STACK_SIZE,
    .led_handler_event_queue_size = LED_HANDLER_EVENT_QUEUE_SIZE,
};

/* RTOS接口实现示例(需要根据实际RTOS实现) */
static led_handler_ret_code_t example_rtos_task_create(task_function_t task_function,
                                                      const char * const task_name,
                                                      const uint16_t stack_size,
                                                      void * const task_argument,
                                                      uint32_t priority,
                                                      void ** const task_handle)
{
    /* 这里需要调用实际的RTOS任务创建函数 */
    /* 例如：xTaskCreate(task_function, task_name, stack_size, task_argument, priority, task_handle); */
    return LED_HANDLER_OK;
}

static led_handler_ret_code_t example_rtos_queue_create(void ** const pqueue,
                                                       uint32_t queue_size,
                                                       uint32_t item_size)
{
    /* 这里需要调用实际的RTOS队列创建函数 */
    /* 例如：*pqueue = xQueueCreate(queue_size, item_size); */
    return LED_HANDLER_OK;
}

/* 其他RTOS接口函数实现... */
static led_handler_os_interface_t g_led_os_interface = {
    .rtos_task_create = example_rtos_task_create,
    .rtos_queue_create = example_rtos_queue_create,
    /* 其他接口函数指针... */
};

/* 动态分配接口实现示例 */
static void* example_malloc(uint32_t mem_size)
{
    /* 这里需要调用实际的内存分配函数 */
    /* 例如：return malloc(mem_size); */
    return NULL;
}

static void example_free(void *p_mem)
{
    /* 这里需要调用实际的内存释放函数 */
    /* 例如：free(p_mem); */
}

static led_handler_dynamic_allocation_t g_led_dynamic_allocation = {
    .pfmalloc = example_malloc,
    .pffree = example_free,
};

/* 时基接口实现示例 */
static uint32_t example_get_timetick_ms(void)
{
    /* 这里需要返回系统时间戳(毫秒) */
    /* 例如：return HAL_GetTick(); */
    return 0;
}

static led_handler_timebase_interface_t g_led_timebase_interface = {
    .pfget_timetick_ms = example_get_timetick_ms,
};

/* 临界区接口实现示例 */
static led_handler_rtos_ret_code_t example_critical_enter(void)
{
    /* 进入临界区 */
    /* 例如：taskENTER_CRITICAL(); */
    return LED_HANDLER_PDTRUE;
}

static led_handler_rtos_ret_code_t example_critical_exit(void)
{
    /* 退出临界区 */
    /* 例如：taskEXIT_CRITICAL(); */
    return LED_HANDLER_PDTRUE;
}

static led_handler_os_critical_t g_led_os_critical = {
    .pf_rtos_critical_enter = example_critical_enter,
    .pf_rtos_critical_exit = example_critical_exit,
};

/******************************************************************************
 * @brief LED处理器初始化示例
 * 
 * @return led_handler_ret_code_t 返回码
 *****************************************************************************/
led_handler_ret_code_t led_handler_example_init(void)
{
    led_handler_input_all_arg_t led_handler_input = {
        .p_system_config = &g_led_system_config,
        .p_led_os_interface = &g_led_os_interface,
        .p_led_os_critical = &g_led_os_critical,
        .p_led_dynamic_allocation = &g_led_dynamic_allocation,
        .p_led_timebase_interface = &g_led_timebase_interface,
    };
    
    /* 初始化LED处理器 */
    led_handler_ret_code_t ret = bsp_led_handler_instance(&g_led_handler, &led_handler_input);
    if (ret != LED_HANDLER_OK) {
        return ret;
    }
    
    /* 这里需要初始化各个LED驱动... */
    /* 例如：
    bsp_led_driver_inst(&g_led_run_driver, &run_gpio_interface, &g_led_dynamic_allocation, LED_RUN);
    bsp_led_driver_inst(&g_led_485_driver, &rs485_gpio_interface, &g_led_dynamic_allocation, LED_485);
    bsp_led_driver_inst(&g_led_power_driver, &power_gpio_interface, &g_led_dynamic_allocation, LED_POWER);
    */
    
    /* 注册LED驱动 */
    bsp_led_handler_register_driver(&g_led_handler, &g_led_run_driver);
    bsp_led_handler_register_driver(&g_led_handler, &g_led_485_driver);
    bsp_led_handler_register_driver(&g_led_handler, &g_led_power_driver);
    
    return LED_HANDLER_OK;
}

/******************************************************************************
 * @brief LED控制示例
 *****************************************************************************/
void led_handler_example_control(void)
{
    led_control_param_t control_param;
    
    /* 示例1：运行LED常亮 */
    control_param.led_which = LED_RUN;
    control_param.mode = LED_MODE_ON;
    bsp_led_handler_control(&g_led_handler, &control_param);
    
    /* 示例2：485LED闪烁，周期1秒，闪烁10次，50%占空比 */
    control_param.led_which = LED_485;
    control_param.mode = LED_MODE_BLINK;
    control_param.cycle_time_ms = 1000;
    control_param.blink_count = 10;
    control_param.proportion = LED_BLINK_RATIO_1_1;
    bsp_led_handler_control(&g_led_handler, &control_param);
    
    /* 示例3：电源LED呼吸灯，周期3秒，最大亮度80% */
    control_param.led_which = LED_POWER;
    control_param.mode = LED_MODE_BREATH;
    control_param.breath_period_ms = 3000;
    control_param.brightness = 80;
    bsp_led_handler_control(&g_led_handler, &control_param);
    
    /* 示例4：运行LED定时亮起5秒后关闭 */
    control_param.led_which = LED_RUN;
    control_param.mode = LED_MODE_TIMER_ON;
    control_param.timer_duration_ms = 5000;
    bsp_led_handler_control(&g_led_handler, &control_param);
}

/******************************************************************************
 * @brief 获取LED状态示例
 *****************************************************************************/
void led_handler_example_status(void)
{
    /* 获取已注册LED数量 */
    uint8_t led_count = bsp_led_handler_get_registered_count(&g_led_handler);
    
    /* 打印LED数量 */
    /* printf("Registered LED count: %d\n", led_count); */
}

//******************************** Example **********************************//
