#ifndef __TIMESTAMP_SYSTEM_INIT_H__
#define __TIMESTAMP_SYSTEM_INIT_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "timestamp_sync.h"

/**
 * @brief 初始化同步采样时间戳系统
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_system_init(void);

/**
 * @brief 获取系统频率测量结果
 * @param frequency 输出频率值
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_system_get_frequency(float *frequency);

/**
 * @brief 获取系统时间戳信息
 * @param timestamp 输出时间戳数据
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_system_get_timestamp(timestamp_sync_data_t *timestamp);

#ifdef __cplusplus
}
#endif

#endif /* __TIMESTAMP_SYSTEM_INIT_H__ */
