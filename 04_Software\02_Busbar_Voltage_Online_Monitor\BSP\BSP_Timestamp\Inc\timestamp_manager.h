#ifndef __TIMESTAMP_MANAGER_H__
#define __TIMESTAMP_MANAGER_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "timestamp_sync.h"
#include "stm32f4xx_hal.h"

/**
 * @brief 初始化时间戳管理器
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_manager_init(void);

/**
 * @brief 获取时间戳管理器句柄
 * @return timestamp_sync_handle_t* 时间戳同步句柄指针
 */
timestamp_sync_handle_t* timestamp_manager_get_handle(void);

/**
 * @brief 获取当前定时器CNT值
 * @return uint32_t 当前定时器计数值
 */
uint32_t timestamp_manager_get_timer_cnt(void);

/**
 * @brief TIM3输入捕获中断回调函数
 * @param htim 定时器句柄
 */
void timestamp_manager_tim3_ic_callback(TIM_HandleTypeDef *htim);

/**
 * @brief TIM4输入捕获中断回调函数
 * @param htim 定时器句柄
 */
void timestamp_manager_tim4_ic_callback(TIM_HandleTypeDef *htim);

/**
 * @brief 为AD7606数据绑定时间戳
 * @param adc_data AD7606采样数据
 * @param timestamped_data 输出带时间戳的数据
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_manager_bind_adc_data(const int16_t adc_data[8],
                                                    ad7606_timestamped_data_t *timestamped_data);

/**
 * @brief 获取当前频率测量结果
 * @param measurement 输出频率测量结果
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_manager_get_frequency(frequency_measurement_t *measurement);

/**
 * @brief 计算相位修正参数
 * @param t_sample0 采样起始时间
 * @param t_zero 过零点时间
 * @param correction 输出相位修正参数
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_manager_calculate_phase_correction(uint32_t t_sample0,
                                                                 uint32_t t_zero,
                                                                 phase_correction_t *correction);

/**
 * @brief 应用相位修正到FFT结果
 * @param fft_complex_data FFT复数结果数组
 * @param fft_length FFT长度
 * @param correction 相位修正参数
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_manager_apply_phase_correction(float *fft_complex_data,
                                                             uint16_t fft_length,
                                                             const phase_correction_t *correction);

/**
 * @brief 获取当前时间戳信息
 * @param timestamp 输出时间戳数据
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_manager_get_current_timestamp(timestamp_sync_data_t *timestamp);

#ifdef __cplusplus
}
#endif

#endif /* __TIMESTAMP_MANAGER_H__ */
