#ifndef __TIMESTAMP_MANAGER_H__
#define __TIMESTAMP_MANAGER_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "timestamp_sync.h"
#include "stm32f4xx_hal.h"

/**
 * @brief 初始化时间戳管理器
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_manager_init(void);

/**
 * @brief 获取时间戳管理器句柄
 * @return timestamp_sync_handle_t* 时间戳同步句柄指针
 */
timestamp_sync_handle_t* timestamp_manager_get_handle(void);

/**
 * @brief 获取当前定时器CNT值
 * @return uint32_t 当前定时器计数值
 */
uint32_t timestamp_manager_get_timer_cnt(void);

/**
 * @brief TIM3输入捕获中断回调函数
 * @param htim 定时器句柄
 */
void timestamp_manager_tim3_ic_callback(TIM_HandleTypeDef *htim);

/**
 * @brief TIM4输入捕获中断回调函数
 * @param htim 定时器句柄
 */
void timestamp_manager_tim4_ic_callback(TIM_HandleTypeDef *htim);

/**
 * @brief 生成压缩时间戳 - 按照文档规范
 * @return uint32_t 压缩时间戳
 */
uint32_t timestamp_manager_generate_packed_timestamp(void);

/**
 * @brief 获取电网同步状态 - 按照文档规范
 * @param status 输出电网同步状态
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_manager_get_grid_status(GridSyncStatus *status);

/**
 * @brief 计算相位修正参数 - 按照文档规范使用压缩时间戳
 * @param packed_timestamp 压缩时间戳
 * @param correction 输出相位修正参数
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_manager_calculate_phase_correction(uint32_t packed_timestamp,
                                                                 phase_correction_t *correction);

/**
 * @brief 应用相位修正到FFT结果
 * @param fft_complex_data FFT复数结果数组
 * @param fft_length FFT长度
 * @param correction 相位修正参数
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_manager_apply_phase_correction(float *fft_complex_data,
                                                             uint16_t fft_length,
                                                             const phase_correction_t *correction);

/**
 * @brief 检查并处理溢出 - 按照文档规范
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_manager_check_overflow(void);

#ifdef __cplusplus
}
#endif

#endif /* __TIMESTAMP_MANAGER_H__ */
