# 同步采样谐波分析系统 - 需求文档

## 一、背景与目标

本系统用于电网同步测量，包括母线电压与避雷器泄漏电流的幅值、相位、谐波成分提取。实现对50Hz基波和3~9次谐波的幅值与相位分析，满足2%误差要求。

## 二、硬件平台

- 主控芯片：STM32F411CEUx（Cortex-M4F）
- 采样芯片：AD7606（8通道同步采样）
- 方波基准：LM211 比较器 + 零点捕获电路
- 时钟同步：TIM3、TIM4（16位定时器）

## 三、主要功能

1. 支持基于市电零点的同步采样
2. 使用 DMA + SPI 从 AD7606 采样 1024点帧数据
3. 每帧绑定捕获编号 capture_count 与起始时间 t_sample0
4. 使用 Nuttall 窗加权，CMSIS-DSP FFT 提取频谱
5. 三谱线插值提升幅值/频率精度
6. 通过过零点时间差修正 FFT 相位
7. 输出幅值、频率、相位（带电网参考）

## 四、性能指标

| 项目 | 目标 |
|------|------|
| 采样率 | 6400Hz ±0.1% |
| 谐波覆盖 | 3~9次（150Hz~450Hz） |
| 相位误差 | ≤ ±1° |
| 幅值误差 | ≤ ±2% |
| FFT点数 | 1024 点 |
| 频率适应 | 电网49~51Hz 范围 |

## 五、系统组成

- AD7606采样模块（含SPI + DMA配置）
- 输入捕获过零检测模块
- 输入捕获次数与输入捕获定时器的CNT组成时间戳计算
- 采样数据缓存与任务分发
- FFT分析任务
- 输出接口（UART或CAN）

## 六、同步采样逻辑

- 使用 LM211 提供50Hz方波同步信号
- 使用 TIMx 输入捕获上升沿，记录 `t_zero`，计数 `capture_count++`
- 每次 DMA 采样完成，绑定 `t_sample0` 与 `capture_count`
- 使用 `t_sample0 - t_zero` 计算 FFT 修正角度


