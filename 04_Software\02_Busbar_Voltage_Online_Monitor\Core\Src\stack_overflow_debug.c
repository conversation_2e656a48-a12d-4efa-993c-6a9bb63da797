/**
 * @file stack_overflow_debug.c
 * @brief FreeRTOS栈溢出排查示例和调试工具
 * <AUTHOR> Assistant
 * @date 2024
 */

#include "FreeRTOS.h"
#include "task.h"
#include "elog.h"
#include "stack_monitor.h"
#include <string.h>
#include <stdio.h>

#define STACK_DEBUG_TAG "STACK_DEBUG"

/**
 * @brief 栈溢出排查步骤示例
 */
void stack_overflow_debug_guide(void)
{
    printf("[%s] === FreeRTOS栈溢出排查指南 ===\r\n", STACK_DEBUG_TAG);
    printf("[%s] 1. 启用栈溢出检测: configCHECK_FOR_STACK_OVERFLOW = 2\r\n", STACK_DEBUG_TAG);
    printf("[%s] 2. 实现栈溢出钩子函数: vApplicationStackOverflowHook()\r\n", STACK_DEBUG_TAG);
    printf("[%s] 3. 监控任务栈使用情况\r\n", STACK_DEBUG_TAG);
    printf("[%s] 4. 检查可能的原因:\r\n", STACK_DEBUG_TAG);
    printf("[%s]    - 任务栈大小不足\r\n", STACK_DEBUG_TAG);
    printf("[%s]    - 递归调用过深\r\n", STACK_DEBUG_TAG);
    printf("[%s]    - 局部变量占用过多栈空间\r\n", STACK_DEBUG_TAG);
    printf("[%s]    - 中断嵌套过深\r\n", STACK_DEBUG_TAG);
    printf("[%s]    - 函数调用链过长\r\n", STACK_DEBUG_TAG);
    printf("[%s] ================================\r\n", STACK_DEBUG_TAG);
}

/**
 * @brief 模拟栈溢出的任务（仅用于测试）
 * 警告：此函数会故意造成栈溢出，仅用于测试栈溢出检测机制
 */
void stack_overflow_test_task(void *pvParameters)
{
    printf("[%s] WARNING: Stack overflow test task started - THIS WILL CAUSE STACK OVERFLOW!\r\n", STACK_DEBUG_TAG);

    /* 故意创建大的局部数组来消耗栈空间 */
    volatile char large_array[2048];  /* 2KB数组 */

    /* 填充数组防止编译器优化 */
    for (int i = 0; i < sizeof(large_array); i++)
    {
        large_array[i] = (char)(i & 0xFF);
    }

    /* 递归调用进一步消耗栈空间 */
    static int recursion_depth = 0;
    recursion_depth++;

    printf("[%s] WARNING: Recursion depth: %d, Array sum: %d\r\n",
          STACK_DEBUG_TAG, recursion_depth, (int)large_array[0]);

    /* 检查当前栈使用情况 */
    UBaseType_t uxHighWaterMark = uxTaskGetStackHighWaterMark(NULL);
    printf("[%s] WARNING: Remaining stack: %d bytes\r\n", STACK_DEBUG_TAG, (int)uxHighWaterMark * 4);
    
    /* 继续递归直到栈溢出 */
    if (recursion_depth < 100)
    {
        stack_overflow_test_task(pvParameters);  /* 递归调用 */
    }
    
    /* 正常情况下不会执行到这里 */
    vTaskDelete(NULL);
}

/**
 * @brief 创建栈溢出测试任务
 * 警告：此函数会创建一个故意造成栈溢出的任务
 */
void create_stack_overflow_test_task(void)
{
    printf("[%s] WARNING: Creating stack overflow test task...\r\n", STACK_DEBUG_TAG);

    BaseType_t xReturn = xTaskCreate(
        stack_overflow_test_task,     /* 任务函数 */
        "StackOverflowTest",         /* 任务名称 */
        128,                         /* 很小的栈大小，故意造成溢出 */
        NULL,                        /* 任务参数 */
        2,                           /* 任务优先级 */
        NULL                         /* 任务句柄 */
    );

    if (xReturn == pdPASS)
    {
        printf("[%s] WARNING: Stack overflow test task created - EXPECT STACK OVERFLOW!\r\n", STACK_DEBUG_TAG);
    }
    else
    {
        printf("[%s] ERROR: Failed to create stack overflow test task\r\n", STACK_DEBUG_TAG);
    }
}

/**
 * @brief 分析任务栈使用模式
 * @param pcTaskName 任务名称
 */
void analyze_task_stack_pattern(const char* pcTaskName)
{
    TaskHandle_t xTaskHandle;
    UBaseType_t uxHighWaterMark;
    TaskStatus_t xTaskDetails;
    
    printf("[%s] === 分析任务 '%s' 的栈使用模式 ===\r\n", STACK_DEBUG_TAG, pcTaskName);

    /* 查找任务句柄 */
    xTaskHandle = xTaskGetHandle(pcTaskName);
    if (xTaskHandle == NULL)
    {
        printf("[%s] ERROR: 任务 '%s' 未找到\r\n", STACK_DEBUG_TAG, pcTaskName);
        return;
    }
    
    /* 获取任务详细信息 */
    vTaskGetInfo(xTaskHandle, &xTaskDetails, pdTRUE, eInvalid);
    uxHighWaterMark = uxTaskGetStackHighWaterMark(xTaskHandle);
    
    /* 计算栈使用统计 */
    UBaseType_t uxTotalStack = xTaskDetails.usStackHighWaterMark + uxHighWaterMark;
    UBaseType_t uxUsedStack = uxTotalStack - uxHighWaterMark;
    float fUsagePercent = ((float)uxUsedStack / (float)uxTotalStack) * 100.0f;
    
    printf("[%s] 任务状态: %s\r\n", STACK_DEBUG_TAG,
          (xTaskDetails.eCurrentState == eReady) ? "就绪" :
          (xTaskDetails.eCurrentState == eBlocked) ? "阻塞" :
          (xTaskDetails.eCurrentState == eSuspended) ? "挂起" :
          (xTaskDetails.eCurrentState == eRunning) ? "运行" : "未知");

    printf("[%s] 任务优先级: %d\r\n", STACK_DEBUG_TAG, (int)xTaskDetails.uxCurrentPriority);
    printf("[%s] 栈总大小: %d 字节\r\n", STACK_DEBUG_TAG, (int)uxTotalStack * 4);
    printf("[%s] 已使用栈: %d 字节 (%.1f%%)\r\n", STACK_DEBUG_TAG, (int)uxUsedStack * 4, fUsagePercent);
    printf("[%s] 剩余栈空间: %d 字节\r\n", STACK_DEBUG_TAG, (int)uxHighWaterMark * 4);
    
    /* 栈使用建议 */
    if (fUsagePercent > 90.0f)
    {
        printf("[%s] ERROR: 建议: 立即增加栈大小！当前使用率过高\r\n", STACK_DEBUG_TAG);
    }
    else if (fUsagePercent > 80.0f)
    {
        printf("[%s] WARNING: 建议: 考虑增加栈大小，当前使用率较高\r\n", STACK_DEBUG_TAG);
    }
    else if (fUsagePercent < 30.0f)
    {
        printf("[%s] 建议: 可以考虑减少栈大小以节省内存\r\n", STACK_DEBUG_TAG);
    }
    else
    {
        printf("[%s] 建议: 栈大小配置合理\r\n", STACK_DEBUG_TAG);
    }

    printf("[%s] === 分析完成 ===\r\n", STACK_DEBUG_TAG);
}

/**
 * @brief 检查系统整体内存使用情况
 */
void check_system_memory_usage(void)
{
    size_t xFreeHeapSize;
    size_t xMinimumEverFreeHeapSize;
    
    printf("[%s] === 系统内存使用情况 ===\r\n", STACK_DEBUG_TAG);

    /* 获取堆内存使用情况 */
    xFreeHeapSize = xPortGetFreeHeapSize();
    xMinimumEverFreeHeapSize = xPortGetMinimumEverFreeHeapSize();

    printf("[%s] 当前可用堆内存: %d 字节\r\n", STACK_DEBUG_TAG, (int)xFreeHeapSize);
    printf("[%s] 历史最小可用堆内存: %d 字节\r\n", STACK_DEBUG_TAG, (int)xMinimumEverFreeHeapSize);

    /* 计算堆内存使用率 */
    size_t xTotalHeapSize = configTOTAL_HEAP_SIZE;
    size_t xUsedHeapSize = xTotalHeapSize - xFreeHeapSize;
    float fHeapUsagePercent = ((float)xUsedHeapSize / (float)xTotalHeapSize) * 100.0f;

    printf("[%s] 堆内存总大小: %d 字节\r\n", STACK_DEBUG_TAG, (int)xTotalHeapSize);
    printf("[%s] 已使用堆内存: %d 字节 (%.1f%%)\r\n", STACK_DEBUG_TAG, (int)xUsedHeapSize, fHeapUsagePercent);
    
    /* 内存使用建议 */
    if (fHeapUsagePercent > 90.0f)
    {
        printf("[%s] ERROR: 警告: 堆内存使用率过高，可能导致内存分配失败\r\n", STACK_DEBUG_TAG);
    }
    else if (fHeapUsagePercent > 80.0f)
    {
        printf("[%s] WARNING: 注意: 堆内存使用率较高\r\n", STACK_DEBUG_TAG);
    }

    /* 获取任务数量 */
    UBaseType_t uxTaskCount = uxTaskGetNumberOfTasks();
    printf("[%s] 当前任务数量: %d\r\n", STACK_DEBUG_TAG, (int)uxTaskCount);

    printf("[%s] === 内存检查完成 ===\r\n", STACK_DEBUG_TAG);
}

/**
 * @brief 栈溢出排查主函数
 */
void stack_overflow_debug_main(void)
{
    printf("[%s] 开始栈溢出排查...\r\n", STACK_DEBUG_TAG);

    /* 1. 显示排查指南 */
    stack_overflow_debug_guide();

    /* 2. 检查系统内存使用情况 */
    check_system_memory_usage();

    /* 3. 打印所有任务的栈使用情况 */
    print_all_tasks_stack_usage();

    /* 4. 分析特定任务（如果存在的话） */
    analyze_task_stack_pattern("fft_collection_data_thread");

    printf("[%s] 栈溢出排查完成\r\n", STACK_DEBUG_TAG);
}
