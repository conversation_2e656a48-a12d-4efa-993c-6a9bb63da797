#include <stdio.h>


#include "FreeRTOS.h"
#include "task.h"
#include "queue.h"
#include "semphr.h"
#include "timers.h"
#include "cmsis_os.h"

#include "led_device.h"
#include "led_event_handler.h"

static PLED_DEVICE* g_led_device[LED_DEV_Quantity];

static led_event_handle_t g_led_event_handle =
{
    .inited                 = false,
    .led_run_thread         = NULL,
    .led_rs485_thread       = NULL,
    .led_event_handle_queue = NULL,
    .private_data           = g_led_device,
};

static void led_run_thread(void *params)
{
    led_event_handle_t* handle = (led_event_handle_t*)params;
    PLED_DEVICE* pdevice = handle->private_data;
    while(1)
    {
       pdevice[LED_RUN]->LED_Control(pdevice[LED_RUN], Toggle);
       vTaskDelay(500);
    }
}





/**
 * @brief  LED句柄初始化函数
 * @note   
 * @retval -1:已初始化
 *         -2:获取LED设备失败
 *         -3:创建队列失败
 *         -4:创建任务失败
 */
int8_t led_event_handle_init(void)
{
		
    // 如果LED事件处理程序已经初始化，则返回-1
    if(true == g_led_event_handle.inited)
    {
        return -1;
    }
		//增加LED设备
		ADDLEDDEVICE();
    // 获取LED设备
    PLED_DEVICE* pdev = g_led_event_handle.private_data;
    pdev[LED_RUN] = Get_LED_Device(LED_RUN);
    // 如果获取LED设备失败，则返回-2
    if(!pdev[LED_RUN])
    {
        return -2;
    }
		//LED设备初始化
		pdev[LED_RUN]->Init(pdev[LED_RUN]);
    // 创建LED事件处理队列
    g_led_event_handle.led_event_handle_queue = xQueueCreate(5, sizeof(led_event_type));
    // 如果创建队列失败，则返回-3
    if(!g_led_event_handle.led_event_handle_queue)
    {
        return -3;
    }
    // 创建LED_RS485线程
    if( pdTRUE != xTaskCreate(led_run_thread,
                             "led_run_thread",
                              64,
                              &g_led_event_handle,
                              osPriorityNormal,
                              g_led_event_handle.led_run_thread))
    {
        // 如果创建线程失败，则删除队列
        vQueueDelete(g_led_event_handle.led_event_handle_queue);
        return -4;
    }
    // 设置LED事件处理程序为已初始化
    g_led_event_handle.inited = true;
    return 0;
}


int8_t led_event_handle_send(led_event_type event, send_type type)
{
    if(false == g_led_event_handle.inited)
    {
        return -1;
    }
    switch (type)
    {
        case LED_EVENT_THREAD:
        {
            if(pdTRUE != xQueueSend(g_led_event_handle.led_event_handle_queue, &event, 0))
            {
                return -2;
            }
        }break;
        case LED_EVENT_IRQ:
        {
            if(pdTRUE != xQueueSendFromISR(g_led_event_handle.led_event_handle_queue, &event, NULL))
            {
                return -3;
            }
        }break;
    }
    return 0;
}