/******************************************************************************
 * @file flash_event_fun.h
 * @brief Flash事件处理函数
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-16
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 
 * @par dependencies
 * 
 * @note 1 tab == 4 spaces!
 *****************************************************************************/
#ifndef __FLASH_EVENT_FUN_H_
#define __FLASH_EVENT_FUN_H_

//******************************** Includes *********************************//
#include "flash_event_handler.h"
//******************************** Includes *********************************//

//******************************** Declaration ******************************//

//******************************** Declaration ******************************//

//******************************** Variables ********************************//

//******************************** Variables ********************************//

//******************************** Functions ********************************//
/**
 * @brief 注册所有Flash事件处理函数的回调
 * 
 * @return flash_handler_ret_t 注册结果
 */
flash_handler_ret_t flash_event_register_cb_init(void);

//******************************** Functions ********************************//

#endif /* __FLASH_EVENT_FUN_H_ */ 