#include "timestamp_manager.h"
#include "main.h"

/**
 * @brief 初始化同步采样时间戳系统
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_system_init(void)
{
    timestamp_sync_ret_t ret;
    
    /* 1. 初始化时间戳管理器 */
    ret = timestamp_manager_init();
    if (ret != TIMESTAMP_SYNC_OK) {
        return ret;
    }
    
    /* 2. 系统初始化完成后的其他配置 */
    // 这里可以添加其他初始化代码
    
    return TIMESTAMP_SYNC_OK;
}

/**
 * @brief 获取系统频率测量结果
 * @param frequency 输出频率值
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_system_get_frequency(float *frequency)
{
    if (frequency == NULL) {
        return TIMESTAMP_SYNC_ERROR_PARAM;
    }
    
    frequency_measurement_t measurement;
    timestamp_sync_ret_t ret = timestamp_manager_get_frequency(&measurement);
    
    if (ret == TIMESTAMP_SYNC_OK && measurement.valid) {
        *frequency = measurement.frequency;
        return TIMESTAMP_SYNC_OK;
    }
    
    return TIMESTAMP_SYNC_ERROR;
}

/**
 * @brief 获取系统时间戳信息
 * @param timestamp 输出时间戳数据
 * @return timestamp_sync_ret_t 返回值
 */
timestamp_sync_ret_t timestamp_system_get_timestamp(timestamp_sync_data_t *timestamp)
{
    return timestamp_manager_get_current_timestamp(timestamp);
}
